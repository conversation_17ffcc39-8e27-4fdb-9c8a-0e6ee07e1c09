<?xml version="1.0" encoding="UTF-8"?>
<!-- <?xml version="1.0" encoding="gb2312"?> -->
<project name="javagame" default="all" basedir=".">

	<!-- �������� -->
	<property environment="env" />
	<property name="build" value="build" />
	<property name="dist" value="../main" />
	<property name="gof-src" value="../gof/connsrv;../gof/dbsrv;../gof/core;../gof/gen"/>
	<property name="lz-src" value="src;gen;seam" />
	<property name="platform-src" value="../platform/src;../platform/gen" />
	
	<!--<property name="bridge-src" value="../bridge/gen;../bridge/seam;../bridge/src" />-->
	<!--<property name="admin-src" value="../admin/gen;../admin/seam;../admin/src" />-->

	<property name="version" value="1.0" />
	<property name="publish-dir" value="../main" />
	<property name="realease-dir" value="../../server_release/main" />
	<path id="classpath">
		<fileset dir="../libs">
			<include name="*.jar" />
		</fileset>
	</path>

	
	<target name="all" depends="startClean,init,make-all,endClean,publish,copyTorealease" />

	<target name="startClean">
		<delete dir="${build}" />
		<delete dir="${dist}" />
	</target>

	<target name="init">
		<mkdir dir="${build}" />
		<mkdir dir="${dist}" />

		<uptodate property="game.uptodate" targetfile="${dist}/lz-${version}.jar">
			<srcfiles dir="${gof-src};${lz-src};" includes="*" />
		</uptodate>		
	</target>

	<target name="make-all" unless="game.uptodate">
		<echo message="javac game"></echo>
		<javac destdir="${build}" debug="true" fork="true" memorymaximumsize="2048M" encoding="UTF-8">
			<src path="${gof-src};${platform-src};${lz-src};"/>
			<classpath refid="classpath" />
		</javac>		
		<echo message="javac finished"></echo>
		<jar jarfile="${dist}/lz-${version}.jar" basedir="${build}">
			<include name="**/*" />
		</jar>
		<echo message="jar all game finished"></echo>
	</target>
	
	<target name="endClean">
		<delete dir="${build}" />
		<echo message="build finished"></echo>
	</target>
	
	<!-- ���Ƶ�main���� -->
	<target name="publish">
		<echo message="copy jar"></echo>
		<copy todir="${publish-dir}">
			<fileset dir="${dist}">
				<include name="*.jar" />
			</fileset>
		</copy>			
		<echo message="copy finished"></echo>		
	</target>
	
	<!-- ���Ƶ� server_realease ��main���� -->
	<target name="copyTorealease">
			<echo message="copy jar"></echo>
			<copy todir="${realease-dir}">
				<fileset dir="${dist}">
					<include name="*.jar" />
				</fileset>
			</copy>			
			<echo message="copy finished"></echo>		
	</target>
	

</project>
