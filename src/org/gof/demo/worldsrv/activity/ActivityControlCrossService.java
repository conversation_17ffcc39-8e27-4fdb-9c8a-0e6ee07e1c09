package org.gof.demo.worldsrv.activity;

import com.alibaba.fastjson.JSONObject;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.redis.client.Command;
import io.vertx.redis.client.Request;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.CrossRedis;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.dbsrv.redis.Tool;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.*;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.distr.admin.AdminCenterManager;
import org.gof.demo.distr.admin.AdminCenterService;
import org.gof.demo.distr.admin.AdminCenterServiceProxy;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.worldsrv.activity.calculator.ActivityControlRankCopy;
import org.gof.demo.worldsrv.blackMarket.BlackMarketCrossServiceProxy;
import org.gof.demo.worldsrv.common.GamePort;
import org.gof.demo.worldsrv.common.GameServiceBase;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@DistrClass(
        servId = D.SERV_ACTIVITY_CROSS
)
public class ActivityControlCrossService extends GameServiceBase {
    private static final int PageNum = 200;
    private final Map<Integer, Integer> actRoundMap = new HashMap<>();// 开启的活动
    private TickTimer checkTime;// 检测TickTimer
    private List<Integer> crossTypeList = new ArrayList<>();// 该跨服service管理的跨服功能类型
    private List<Integer> actTypeList = new ArrayList<>();// 该跨服service管理的活动类型
    private boolean isStart = false;
    private Map<Integer, String> luckyLotteryCodeMap;// 福签抽奖活动码

    public ActivityControlCrossService(GamePort port) {
        super(port);
    }

    @Override
    protected void init() {
        Log.temp.info("===初始化跨服活动服务serverId={}===", Config.SERVER_ID);
        Calendar instance = Calendar.getInstance();
        instance.set(Calendar.SECOND, 0);
        instance.set(Calendar.MILLISECOND, 0);
        long initTime = instance.getTimeInMillis();
        checkTime = new TickTimer(initTime, Time.MIN);
        isStart = false;
    }

    @Override
    public void pulseOverride() {
        super.pulseOverride();
        long timeNow = Port.getTime();
        if (isStart && checkTime != null && checkTime.isPeriod(timeNow)) {
            checkEnd();
            checkOpen();
        }
    }

    private void checkOpen() {
        for (ConfActivityControl conf : ConfActivityControl.findAll()) {
            if (!actTypeList.contains(conf.type)) {
                continue;
            }
            if (actRoundMap.containsKey(conf.sn)) {
                continue;
            }
            if (conf.isClose == 1) {
                continue;
            }
            long[] times = getOpenEndTime(conf);
            if (times == null) {
                continue;
            }
            long currTime = Port.getTime();
            if (currTime < times[0] || currTime >= times[1]) {
                continue;
            }
            actRoundMap.put(conf.sn, conf.sn);
            openCrossActivity(conf);
        }
    }

    private void openCrossActivity(ConfActivityControl conf) {
        if (ActivityControlTypeFactory.blackMarketSet.contains(conf.type)) {
            int crossServerId = Config.SERVER_ID - GlobalConfVal.cross_server_id_base;
            ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.跨服黑市服务器开启区间.SN);
            if (confGlobal == null || confGlobal.intArray == null) {
                return;
            }
            List<Integer> crossList = Utils.intArrToList(confGlobal.intArray);
            if (!crossList.contains(crossServerId)) {
                return;// 说明当前服务器不在跨服黑市开启区间
            }
            BlackMarketCrossServiceProxy prx = BlackMarketCrossServiceProxy.newInstance();
            prx.blackMarketOpen(conf.sn);
        } else if (ActivityControlTypeFactory.luckyLotterySet.contains(conf.type)) {
            luckyLotteryCodeMap = new HashMap<>();
            // 福签抽奖
            String redisKey = Utils.createStr("{}_{}", RedisKeys.act_lucky_lottery_code, conf.sn);
            RedisTools.getHashJsonObject(CrossRedis.getClient(), redisKey, res -> {
                if (res.failed()) {
                    Log.activity.error("[福签抽奖]活动起服加载数据失败, sn={}", conf.sn, res.cause());
                    return;
                }
                JsonObject object = res.result();
                for (String key : object.fieldNames()) {
                    luckyLotteryCodeMap.put(Integer.parseInt(key), object.getString(key));
                }
                long[] times = getOpenEndTime(conf);
                int days = Utils.getDaysBetween(times[0], times[1]) + 1;
                boolean isAdd = false;
                for (int i = 1; i <= days; i++) {
                    if (!luckyLotteryCodeMap.containsKey(i) || "".equals(luckyLotteryCodeMap.get(i))) {
                        String code = ActivityManager.inst().genRandomCode(null, 0);
                        luckyLotteryCodeMap.put(i, code);
                        isAdd = true;
                    }
                }
                if (isAdd) {
                    JsonObject jo = new JsonObject();
                    for (Map.Entry<Integer, String> entry : luckyLotteryCodeMap.entrySet()) {
                        jo.put(entry.getKey().toString(), entry.getValue());
                    }
                    RedisTools.setHashJsonObject(CrossRedis.redis, redisKey, jo, (ret) -> {
                        if (ret.failed()) {
                            Log.activity.error("[福签抽奖]活动保存幸运号码失败, sn={}", conf.sn, ret.cause());
                            return;
                        }
                    });
                }

                Log.activity.info("[福签抽奖]活动起服, sn={}, 福签号码={}", conf.sn, Utils.mapIntStrToJSON(luckyLotteryCodeMap));
            });
        }
    }

    private void closeCrossActivity(ConfActivityControl conf) {
        if (ActivityControlTypeFactory.blackMarketSet.contains(conf.type)) {
            int crossServerId = Config.SERVER_ID - GlobalConfVal.cross_server_id_base;
            ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.跨服黑市服务器开启区间.SN);
            if (confGlobal == null || confGlobal.intArray == null) {
                return;
            }
            List<Integer> crossList = Utils.intArrToList(confGlobal.intArray);
            if (!crossList.contains(crossServerId)) {
                return;// 说明当前服务器不在跨服黑市开启区间
            }
            BlackMarketCrossServiceProxy prx = BlackMarketCrossServiceProxy.newInstance();
            prx.blackMarketClose(conf.sn);
        } else if (ActivityControlTypeFactory.luckyLotterySet.contains(conf.type)) {
            luckyLotteryCodeMap = null;
            // 福签抽奖
            String redisKey = Utils.createStr("{}_{}", RedisKeys.act_lucky_lottery_code, conf.sn);
            RedisTools.expire(CrossRedis.getClient(), redisKey, 2 * Tool.DAY);
        }
    }

    /**
     * 检测活动关闭
     */
    private void checkEnd() {
        if (actRoundMap.isEmpty()) {
            return;
        }
        // 1.收集关闭的活动和轮次
        Map<Integer, List<Integer>> crossTypeActMap = new HashMap<>();
        Map<Integer, Integer> endActRoundMap = new HashMap<>(0);
        for (Integer confSn : actRoundMap.keySet()) {
            int round = actRoundMap.get(confSn);
            ConfActivityControl conf = ConfActivityControl.get(confSn);
            boolean isClose = false;
            if (conf.isClose == 1) {
                isClose = true;
            } else {
                long currTime = Port.getTime();
                long[] times = getOpenEndTime(conf);
                if (times == null || (currTime >= times[0] && currTime >= times[1])) {
                    isClose = true;
                }
            }
            if (isClose) {
                endActRoundMap.put(confSn, round);
                ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(conf.type, round);
                if (confTerm.rank_id != 0) {
                    ConfRanktype confRanktype = ConfRanktype.get(confTerm.rank_id);
                    if (confRanktype.crossType != 0 && crossTypeList.contains(confRanktype.crossType)) {
                        crossTypeActMap.computeIfAbsent(confRanktype.crossType, k -> new ArrayList<>()).add(confSn);
                    }
                }
            }
        }
        if (endActRoundMap.isEmpty()) {
            return;
        }
        // 2.先删除掉内存数据
        Log.activity.info("跨服关闭活动, sn={}", Utils.listToString(new ArrayList<>(endActRoundMap.keySet())));
        for (Integer confSn : endActRoundMap.keySet()) {
            actRoundMap.remove(confSn);
            closeCrossActivity(ConfActivityControl.get(confSn));
        }
        //结算排行榜活动类型复制排行榜
        // 3.查询全部分组，然后进行结算
        for (int crossType : crossTypeList) {
            List<Integer> snList = crossTypeActMap.getOrDefault(crossType, new ArrayList<>());
            if (snList.isEmpty()) {
                continue;
            }
            AdminCenterServiceProxy prx = AdminCenterManager.createAdminProxy();
            prx.getCrossGroup(crossType, Config.SERVER_ID);
            prx.listenResult((result, context) -> {
                Set<Integer> groupSet = result.get("groupSet");
                if (groupSet.isEmpty()) {
                    Log.activity.error("跨服活动结算出错, 取不到跨服的组!");
                    return;
                }
                for (Integer actSn : snList) {
                    int confSn = actSn;
                    int round = actRoundMap.getOrDefault(actSn, 0);
                    ConfActivityControl conf = ConfActivityControl.get(confSn);
                    if (onCopyRankActivityEnd(conf, round, groupSet)) {
                        continue;
                    }
                    settleActivity(conf, round, groupSet);
                }
            });
        }
    }

    /**
     * 计算活动开启结束时间
     */
    public static long[] getOpenEndTime(ConfActivityControl conf) {
        if (conf.timeType == ParamKey.activityTimeType_1) {
            if (conf.is_round == 1) {
                return null;
            }
            String[] timeArr = Utils.splitStr(conf.time, "\\|");
            if (timeArr == null || timeArr.length < 2) {
                Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
                return null;
            }
            long openTime = Utils.formatTimeToLong(timeArr[0]);
            long closeTime = Utils.formatTimeToLong(timeArr[1]);
            return new long[]{
                    openTime,
                    closeTime
            };
        }
        return null;
    }

    /**
     * 活动结算
     */
    private void settleActivity(ConfActivityControl conf, int round, Set<Integer> groupSet) {
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(conf.type, round);
        if (confTerm == null) {
            Log.activity.error("跨服活动结算找不到期数表，type={}. round={}", conf.type, round);
            return;
        }
        // 判断活动对应的类型是否是跨服活动
        ConfRanktype confRankType = ConfRanktype.get(confTerm.rank_id);
        if (confRankType == null || confRankType.crossType == 0 || !crossTypeList.contains(confRankType.crossType)) {
            return;
        }
        Log.activity.info("跨服活动结算开始, sn={}, round={}", conf.sn, round);
        // 遍历所有跨服排行榜，进行结算（结算延迟）
        int rankSn = confTerm.rank_id;
        int index = 0;
        for (Integer groupId : groupSet) {
            scheduleOnce(new ScheduleTask() {
                @Override
                public void execute() {
                    sendActivityRankReward(conf, round, rankSn, groupId);
                }
            }, index * 10L);
            index ++;
        };
    }

    private void sendActivityRankReward(ConfActivityControl conf, int round, int rankSn, int groupId) {
        ConfRanktype confRanktype = ConfRanktype.get(rankSn);
        String prefix = confRanktype.rank_type == RankParamKey.rankTypeHuman ? RedisKeys.bridge_activity_human_rank : RedisKeys.bridge_activity_guild_rank;
        String redisKey = Utils.createStr("{}_{}_{}", prefix, rankSn, groupId);
        // 先设置排行榜过期时间，给2天
        CrossRedis.expire(redisKey, conf.showTime + (int) (2 * Time.DAY / Time.SEC));
        // 查询排行榜
        RedisTools.getRankLen(EntityManager.redisClient, redisKey, handler -> {
            if (handler.failed()) {
                Log.activity.error("跨服活动排行榜结算查询排行榜长度报错, e={}", handler.cause().getMessage(), handler.cause());
                return;
            }
            int rankLen = Utils.intValue(handler.result());
            if (rankLen == 0) {
                Log.activity.info("跨服活动跨服排行榜为空, sn={}, round={}, rankSn={}, groupId={}", conf.sn, round, rankSn, groupId);
                return;
            }
            Log.activity.info("跨服开始结算活动跨服排行榜奖励, sn={}, round={}, rankSn={}, groupId={}", conf.sn, round, rankSn, groupId);
            int maxPage = rankLen / PageNum + (rankLen % PageNum == 0 ? 0 : 1);
            for (int i = 0; i < maxPage; i++) {
                int index = i;
                // 延时查询结算
                scheduleOnce(new ScheduleTask() {
                    @Override
                    public void execute() {
                        int min = index * PageNum;
                        int max = (index + 1) * PageNum - 1;
                        RedisTools.getRankListByIndex(EntityManager.redisClient, redisKey, min, max, true, handler1 -> {
                            if (handler1.failed()) {
                                Log.activity.error("跨服活动排行榜结算查询排行榜报错, min={}, max={}, e={}", min, max, handler1.cause().getMessage(), handler1.cause());
                                return;
                            }
                            JsonArray resultJA = handler1.result();
                            if (resultJA.size() == 0) {
                                return;
                            }
                            Map<Integer, List<ActivityCrossRankSettleInfo>> serverRankMap = new HashMap<>(0);
                            int rankAddValue = 1;
                            List resultList = resultJA.getList();
                            List<Long> humanIdList = new ArrayList<>();
                            for (int j = 0; j + 1 < resultList.size(); j += 2, rankAddValue++) {
                                long humanId = Utils.longValue(resultList.get(j));
                                long score = Utils.longValue(resultList.get(j + 1));

                                ActivityCrossRankSettleInfo rankSettleInfo = new ActivityCrossRankSettleInfo();
                                rankSettleInfo.id = humanId;
                                rankSettleInfo.score = score;
                                rankSettleInfo.rank = min + rankAddValue;
                                humanIdList.add(humanId);

                                int serverId = Utils.getServerIdByHumanId(humanId);
                                serverRankMap.computeIfAbsent(serverId, k -> new ArrayList<>(1)).add(rankSettleInfo);
                            }
                            if (serverRankMap.isEmpty()) {
                                return;
                            }
                            Set<Integer> serverIdSet = serverRankMap.keySet();
                            if (confRanktype.rank_type == RankParamKey.rankTypeHuman) {
                                for (Integer serverId : serverIdSet) {
                                    List<ActivityCrossRankSettleInfo> infoList = serverRankMap.get(serverId);
                                    HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance(DistrKit.getWorldNodeID(serverId));
                                    prx.sendActivityCrossHumanRankSettle(infoList, conf.sn, round, rankSn);
                                    Log.activity.info("跨服排行结算, sn={}, round={}, rankSn={}, serverId={}, humanIdList={}", conf.sn, round, rankSn, serverId, Utils.listToString(humanIdList));
                                }
                            }
                        });
                    }
                }, index * Time.SEC);
            }
        });
    }

    @DistrMethod
    public void queryServiceControlActType() {
        Log.activity.error("[跨服活动]Service开始查询该跨服管理的跨服功能类型和活动类型！serverId={}", Config.SERVER_ID);
        try {
            AdminCenterServiceProxy prx = AdminCenterManager.createAdminProxy();
            prx.getCrossServerActivityGroupSn(CrossType.cross_activity.getType(), 0);
            Param result = prx.waitForResult();
            int sn = result.get("sn");
            if (sn != 0) {
                crossTypeList.add(CrossType.cross_activity.getType());
                ConfServerActivityGroup conf = ConfServerActivityGroup.get(sn);
                if (!"".equals(conf.param))
                    actTypeList.addAll(Utils.strToIntList(conf.param));
            }
            prx.getCrossServerActivityGroupSn(CrossType.cross_black_market.getType(), 0);
            result = prx.waitForResult();
            sn = result.get("sn");
            if (sn != 0) {
                crossTypeList.add(CrossType.cross_black_market.getType());
                ConfServerActivityGroup conf = ConfServerActivityGroup.get(sn);
                if (!"".equals(conf.param))
                    actTypeList.addAll(Utils.strToIntList(conf.param));
            }
            prx.getCrossServerActivityGroupSn(CrossType.cross_activity2.getType(), 0);
            result = prx.waitForResult();
            sn = result.get("sn");
            if (sn != 0) {
                crossTypeList.add(CrossType.cross_activity2.getType());
                ConfServerActivityGroup conf = ConfServerActivityGroup.get(sn);
                if (!"".equals(conf.param))
                    actTypeList.addAll(Utils.strToIntList(conf.param));
            }
            prx.getCrossServerActivityGroupSn(CrossType.cross_activity3.getType(), 0);
            result = prx.waitForResult();
            sn = result.get("sn");
            if (sn != 0) {
                crossTypeList.add(CrossType.cross_activity3.getType());
                ConfServerActivityGroup conf = ConfServerActivityGroup.get(sn);
                if (!"".equals(conf.param))
                    actTypeList.addAll(Utils.strToIntList(conf.param));
            }
            Log.activity.error("[跨服活动]Service查询完毕该跨服管理的跨服功能类型和活动类型！serverId={}", Config.SERVER_ID);
        } catch (Exception e) {
            Log.activity.error("[跨服活动]Service查询该跨服管理的跨服功能类型和活动类型出错，停止服务器！serverId={}", Config.SERVER_ID, e);
            System.exit(1);
        }
        isStart = true;
    }


    @DistrMethod
    public void getOpenActSnRoundMap() {
        port.returns(actRoundMap);
    }

    private boolean onCopyRankActivityEnd(ConfActivityControl conf, int round, Set<Integer> groupSet) {
        // 检查是否是需要复制排行榜的活动类型
        Set<Integer> rankCopySet = ActivityControlTypeFactory.getTypeSet(ActivityControlRankCopy.class);
        if (rankCopySet != null && rankCopySet.contains(conf.type)) {
            // 获取活动期数配置
            ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(conf.type, round);
            if (confTerm == null) {
                Log.activity.error("跨服活动开启找不到期数表，type={}. round={}", conf.type, round);
                return true;
            }

            // 获取目标排行榜配置
            ConfRanktype targetRankConf = ConfRanktype.get(confTerm.rank_id);
            if (targetRankConf == null) {
                Log.activity.error("找不到目标排行榜配置, rankId={}", confTerm.rank_id);
                return true;
            }

            // 获取源排行榜配置
            ConfRanktype sourceRankConf = ConfRanktype.get(confTerm.parameter);
            if (sourceRankConf == null) {
                Log.activity.error("找不到源排行榜配置, rankId={}", confTerm.parameter);
                return true;
            }
            if (sourceRankConf.crossType == 0) {
                return true;
            }
            // 获取所有跨服组
            AtomicInteger completedGroups = new AtomicInteger(0);
            for (Integer groupId : groupSet) {
                copyRank(sourceRankConf, targetRankConf, groupId, () -> {
                    if (completedGroups.incrementAndGet() == groupSet.size()) {
                        settleActivity(conf, round, groupSet); // round 和 groupSet 根据实际情况调整
                    }
                });
            }
            return true;
        }
        return false;
    }

    private void copyRank(ConfRanktype sourceRankConf, ConfRanktype targetRankConf, int groupId, Runnable onComplete) {
        String prefix = RedisKeys.bridge_activity_human_rank;
        String sourceKey = Utils.createStr("{}_{}_{}", prefix, sourceRankConf.sn, groupId);
        String targetKey = Utils.createStr("{}_{}_{}", prefix, targetRankConf.sn, groupId);

        // 清除目标排行榜数据，设置过期时间
        CrossRedis.del(targetKey);

        // 获取源排行榜长度
        CrossRedis.getRankLen(sourceKey, handler -> {
            if (handler.failed()) {
                Log.activity.error("Get source rank length failed sourceKey:{}, error:{}", sourceKey, handler.cause());
                onComplete.run();
                return;
            }

            int rankLen = Utils.intValue(handler.result());
            if (rankLen == 0) {
                Log.activity.info("Source rank is empty sourceKey:{}", sourceKey);
                onComplete.run();
                return;
            }

            // 计算页数
            int maxPage = rankLen / PageNum + (rankLen % PageNum == 0 ? 0 : 1);
            AtomicInteger completedPages = new AtomicInteger(0);

            for (int i = 0; i < maxPage; i++) {
                int pageIndex = i;
                processRankPage(sourceKey, targetKey, pageIndex * PageNum, (pageIndex + 1) * PageNum - 1, () -> {
                    if (completedPages.incrementAndGet() == maxPage) {
                        onComplete.run();
                    }
                });
            }
        });
    }
    private void processRankPage(String sourceKey, String targetKey, int min, int max, Runnable onComplete) {
        RedisTools.getRankListByIndex(EntityManager.redisClient, sourceKey, min, max, true, handler -> {
            if (handler.failed()) {
                Log.activity.error("Get rank page failed, sourceKey:{}, min:{}, max:{}, error:", sourceKey, min, max, handler.cause());
                return;
            }

            JsonArray rankData = handler.result();
            if (rankData.isEmpty()) {
                onComplete.run();
                return;
            }

            // 批量请求复制
            List<Request> reqList = new ArrayList<>();

            for (int i = 0; i < rankData.size(); i += 2) {
                long humanId = Utils.longValue(rankData.getString(i));
                double score = Utils.doubleValue(rankData.getString(i + 1));

                Request req = Request.cmd(Command.ZADD, targetKey, score, String.valueOf(humanId));
                reqList.add(req);
            }

            // 执行批量复制
            CrossRedis.batch(reqList, batchHandler -> {
                if (batchHandler.failed()) {
                    Log.activity.error("复制活动排行榜批量复制失败, error:", batchHandler.cause());
                    return;
                }
                onComplete.run();
            });
        });
    }

    /**
     * 手动触发活动排行榜结算
     * @param confSn 活动配置SN
     * @param round 活动轮次
     */
    @DistrMethod
    public void settleActivityRank(int confSn, int round) {
        ConfActivityControl conf = ConfActivityControl.get(confSn);
        if (conf == null) {
            Log.activity.error("手动触发活动排行榜结算失败, 找不到活动配置, sn={}", confSn);
            return;
        }
        Log.activity.info("跨服关闭活动, sn={}", confSn);
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(conf.type, round);
        ConfRanktype confRanktype = ConfRanktype.get(confTerm.rank_id);
        //结算排行榜活动类型复制排行榜
        // 3.查询全部分组，然后进行结算
        AdminCenterServiceProxy prx = AdminCenterManager.createAdminProxy();
        prx.getCrossGroup(confRanktype.crossType, Config.SERVER_ID);
        prx.listenResult((result, context) -> {
            Set<Integer> groupSet = result.get("groupSet");
            if (groupSet.isEmpty()) {
                Log.activity.error("跨服活动结算出错, 取不到跨服的组!");
                return;
            }
            if (onCopyRankActivityEnd(conf, round, groupSet)) {
                return;
            }
            settleActivity(conf, round, groupSet);
        });
    }
    // region 福签抽奖
    @DistrMethod
    public void getLuckyLotteryCode() {
        port.returns("codeMap", luckyLotteryCodeMap);
    }
    // endregion 福签抽奖
}
