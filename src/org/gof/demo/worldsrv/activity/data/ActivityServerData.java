package org.gof.demo.worldsrv.activity.data;

import com.alibaba.fastjson.JSONObject;
import io.vertx.core.json.JsonArray;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.dbsrv.redis.Tool;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.*;
import org.gof.demo.distr.world.srvmerge.ServerMerge;
import org.gof.demo.support.Symbol;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.calculator.IActivityControl;
import org.gof.demo.worldsrv.activity.data.serverdata.IServerActData;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.ActivityData;
import org.gof.demo.worldsrv.entity.ActivityRoundData;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.gm.GameDebugManager;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.DateTimeUtils;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * TODO 所有活动结束时间减去一秒
 *
 */
public class ActivityServerData implements ISerilizable {
    private static final int DELAY_DISTRIBUTE_TIME = 30;
    private static final int PAGE_SIZE = 200; // 每页数量
    private static final int SCHEDULE_DELAY = 2000; // 每批次延迟(毫秒)
    private static final int MAX_RANK = 20000; // 最大发榜数量

    private int serverId;
    private boolean isLoadTypeFinish = false;
    private boolean isLoadRoundFinish = false;
    // 当前开启的活动sn列表
    private Set<Integer> openSnList = new HashSet<>();
    // 当前处于展示期的活动sn列表
    private Set<Integer> endShowSnList = new HashSet<>();

    // 活动sn对应活动类型(开启的活动)
    private static Map<Integer,Integer> snTypeMap = new HashMap<>();
    // 所有活动数据 type, data（同一时间一个类型只能开启一个活动）
    public Map<Integer, ActivityData> typeDataMap = new ConcurrentHashMap<>();
    // 循环活动数据
    public Map<Integer, ActivityRoundData> snRountDataMap = new ConcurrentHashMap<>();
    /**
     * Map<活动sn, 活动服务器数据>
     */
    public Map<Integer, IServerActData> snServerDataMap = new ConcurrentHashMap<>();
    // 展示期结束时间Map
    private Map<Integer, Long> showEndTimeMap = new ConcurrentHashMap<>();

    public boolean isUseMergeConf = false;// 是否合服表格触发效果
    public Set<Integer> typeSet = new HashSet<>();
    public TickTimer checkGmTT = null;// 执行GM检查的定时器

    public ActivityServerData(int serverId) {
        this.serverId = serverId;
        loadData();
    }
    public ActivityServerData() {
    }

    private void loadData(){
        long timeNow = Port.getTime();
        boolean isMerge = false;
        if (serverId != Config.SERVER_ID && Util.getMergeServerIdList(Config.SERVER_ID).size() > 1) {
            isMerge = true;
        }
        boolean isMergeFinal = isMerge;
        EntityManager.getEntityListAsync(ActivityData.class, serverId, (res)-> {
            if (!res.succeeded()) {
                //加载失败
                Log.temp.error("===加载ActivityData失败，group={}", serverId, res.cause());
                isLoadTypeFinish = true;
                return;
            }
            List<ActivityData> modelEnrollList = res.result();
            for (ActivityData data : modelEnrollList) {
                int actType = data.getActivityType();
                if (typeDataMap.containsKey(actType)) {
                    Log.temp.info("===ConfActivityControl配表错误，同一时间一个类型只能开启一个活动。type={}, sn={}", data.getActivityType(), data.getActivitySn());
                    continue;
                }
                
                ConfActivityControl conf = ConfActivityControl.get(data.getActivitySn());
                if (conf == null) {
                    continue;
                }
                if(isMergeFinal && conf.timeType != ParamKey.activityTimeType_2){
                    data.remove();
                    continue;
                }

                if (data.getOpenTime() <= timeNow && timeNow < data.getCloseTime()) {
                    // 活动在开启时间内
                    if (!openSnList.contains(data.getActivitySn())) {
                        openSnList.add(data.getActivitySn());
                        snTypeMap.put(data.getActivitySn(), actType);
                    }
                } else if (data.getCloseTime() <= timeNow && conf.showTime > 0) {
                    // 活动已结束但在展示期内
                    long showEndTime = data.getCloseTime() + conf.showTime * Time.SEC;
                    if (timeNow < showEndTime) {
                        if (!endShowSnList.contains(data.getActivitySn())) {
                            endShowSnList.add(data.getActivitySn());
                            showEndTimeMap.put(data.getActivitySn(), showEndTime);
                            snTypeMap.put(data.getActivitySn(), actType);
                        }
                    }
                }
                
                typeDataMap.put(actType, data);
                IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
                if (control != null) {
                    IServerActData serverData = control.parseServerActData(data.getActData(), data.getRound(), data.getServerId());
                    if (serverData != null) {
                        snServerDataMap.put(data.getActivitySn(), serverData);
                    }
                }
            }
            isLoadTypeFinish = true;
        });

        EntityManager.getEntityListAsync(ActivityRoundData.class, serverId, (res)-> {
            if (!res.succeeded()) {
                //加载失败
                Log.temp.error("===加载ActivityData失败，group={}", serverId, res.cause());
                isLoadTypeFinish = true;
                return;
            }
            List<ActivityRoundData> modelRoundList = res.result();
            for (ActivityRoundData roundData : modelRoundList) {
                int actType = roundData.getActivityType();
                if (snRountDataMap.containsKey(roundData.getActivitySn())) {
                    Log.temp.error("===ConfActivityControl配表错误，同一时间一个类型只能开启一个活动。type={}, sn={}", actType, roundData.getActivitySn());
                    continue;
                }
                
                ConfActivityControl conf = ConfActivityControl.get(roundData.getActivitySn());
                if (conf == null) {
                    continue;
                }
                if(isMergeFinal && conf.timeType != ParamKey.activityTimeType_2){
                    roundData.remove();
                    continue;
                }
                
                if (roundData.getOpenTime() <= timeNow && timeNow < roundData.getCloseTime()) {
                    // 活动在开启时间内
                    if (!openSnList.contains(roundData.getActivitySn())) {
                        openSnList.add(roundData.getActivitySn());
                        snTypeMap.put(roundData.getActivitySn(), actType);
                    }
                } else if (roundData.getCloseTime() <= timeNow && conf.showTime > 0) {
                    // 活动已结束但在展示期内
                    long showEndTime = roundData.getCloseTime() + conf.showTime * Time.SEC;
                    if (timeNow < showEndTime) {
                        if (!endShowSnList.contains(roundData.getActivitySn())) {
                            endShowSnList.add(roundData.getActivitySn());
                            showEndTimeMap.put(roundData.getActivitySn(), showEndTime);
                            snTypeMap.put(roundData.getActivitySn(), actType);
                        }
                    }
                }
                
                snRountDataMap.put(roundData.getActivitySn(), roundData);
                IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
                if (control != null) {
                    IServerActData serverData = control.parseServerActData(roundData.getActData(), roundData.getRound(), roundData.getServerId());
                    if (serverData != null) {
                        snServerDataMap.put(roundData.getActivitySn(), serverData);
                    }
                }
            }
            List<Integer> removeTypeList = new ArrayList<>();
            for(ActivityData data : typeDataMap.values()){
                if(snRountDataMap.containsKey(data.getActivitySn())){
                    ConfActivityControl conf = ConfActivityControl.get(data.getActivitySn());
                    if(conf == null){
                        continue;
                    }
                    if(conf.is_round == 1){
                        // 只删不循环的活动，当活动变成循环时。
                        removeTypeList.add(data.getActivityType());
                    } else {
                        Log.temp.error("===ConfActivityControl配表错误，循环活动不能和非循环活动混合。type={}, sn={}", data.getActivityType(), data.getActivitySn());
                    }
                }
            }
            for(int type : removeTypeList){
                ActivityData data = typeDataMap.get(type);
                data.remove();
                typeDataMap.remove(type);
                snServerDataMap.remove(data.getActivitySn());
            }
            removeTypeList.clear();
            checkActivityRoundInit();
            isLoadRoundFinish = true;
        });
    }

    private void checkActivityRoundInit(){
        Log.temp.info("===检查活动循环初始化：serverId={} , roundSnList={}", serverId, snRountDataMap.keySet());
        long timeNow = Port.getTime();
        for(ConfActivityControl conf : ConfActivityControl.findAll()){
            if(conf == null || conf.is_round != 1 || conf.isClose == 1 || conf.timeType != ParamKey.activityTimeType_3){
                continue;
            }
            if(snRountDataMap.containsKey(conf.sn)){
                continue;
            }
            if(openSnList.contains(conf.sn)){
                continue;
            }
            if(isMergeServerContinueActivity(conf.type, timeNow)){
                continue;
            }
            timeType_3_init(conf);
        }
        Log.temp.info("===检查活动循环初始化：serverId={} , roundSnList={}", serverId, snRountDataMap.keySet());
    }

    private void timeType_3_init(ConfActivityControl conf){
        if(conf.is_round != 1){
            Log.temp.error("===ConfActivityControl配表错误，is_round={}, timeType={}, sn={}", conf.is_round, conf.timeType, conf.sn);
            return;
        }
        int[] timeArr = Utils.arrayStrToInt(conf.time);
        if(timeArr.length < 3){
            Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
            return;
        }
        long timeNow = Port.getTime();
        long openServerTime = Util.getOpenServerTime(serverId);
        long restrictTime = Utils.getOffDayTime(openServerTime, conf.openTime, 0);
        int week = Utils.getDayOfWeek(openServerTime);
        ConfActivityAdjust confActivityAdjust = ConfActivityAdjust.get(GlobalConfVal.getConfActivityAdjustSn(conf.type, week));
        if(confActivityAdjust == null){
            Log.temp.error("===ConfActivityControl配表错误，未找到ConfActivityAdjust数据。type={}, week={},sn={}", conf.type, week, conf.sn);
            return;
        }

        int roundNum = 0;
        long openTime = 0;
        long closeTime = 0;
        for(int i = 0; i < confActivityAdjust.day.length; i++){
            // 策划要求当天就要生效，所以要减一
            long openTimeTemp = Utils.getOffDayTime(openServerTime, confActivityAdjust.day[i][0]-1, 0);
            // 策划要求所有配置时间包含当天23：59.59，又因为取0点时间所以这里需要并减去1秒
            long closeTimeTemp = Utils.getOffDayTime(openServerTime, confActivityAdjust.day[i][1], 0) - Time.SEC;
            if(timeNow >= closeTimeTemp){
                openTime = openTimeTemp;
                closeTime = closeTimeTemp;
                roundNum++;
            }
        }

        // 限制时间未到，走开服活动期数
        if(restrictTime > timeNow){
//            return isOpenActivityTerm(conf);
            if(roundNum > 0 && openTime > 0 && closeTime > 0){
                createActivityRoundData(conf.sn, conf.type, roundNum, openTime, closeTime);
                Log.temp.info("===初始化活动循环：serverId={} , sn={} , round={}， openTime={}， closeTime={}", serverId, conf.sn, roundNum, openTime, closeTime);
            }
            return;
        }

        if(confActivityAdjust.day.length == 0){
            Log.temp.error("===ConfActivityControl配表错误，未找到ConfActivityAdjust数据。type={}, week={},sn={}", conf.type, week, conf.sn);
            return;
        }
        int day = confActivityAdjust.day[confActivityAdjust.day.length-1][1] - confActivityAdjust.day[confActivityAdjust.day.length-1][0] + 1;
        if(day <= 0){
            return;
        }

        long timeTemp = closeTime;

        int openWeek = Utils.intValue(timeArr[0]);
        int openHour = Utils.intValue(timeArr[1]);
        int openMin = Utils.intValue(timeArr[2]);


        long timeOldOpen = openTime;
        long timeOldEnd = closeTime;

        int loopNum = 3650; // 限制循环次数
        while(timeTemp < timeNow){
            loopNum--;
            if(loopNum <= 0){
                break;
            }
            timeTemp += Time.DAY;
            long openTimeTemp = Utils.getTimeOfWeek(timeTemp, openWeek == 1 ? 7 : openWeek - 1, openHour) + openMin * Time.MIN;
            if(openTimeTemp < restrictTime){
                continue;
            }
            long cdTime = timeOldOpen + conf.round_day * Time.DAY - 5 * Time.SEC;
            if(cdTime > timeTemp || cdTime > openTimeTemp){
                continue;
            }
            long closeTimeTemp = Utils.getOffDayTime(openTimeTemp, day, 0) - Time.SEC;
            if(openTimeTemp <= timeTemp && timeTemp < closeTimeTemp){
                roundNum++;
                timeOldOpen = openTimeTemp;
                timeOldEnd = closeTimeTemp;
            }
        }
        if(roundNum > 0 && timeOldOpen > 0 && timeOldEnd > 0){
            createActivityRoundData(conf.sn, conf.type, roundNum, timeOldOpen, timeOldEnd);
            Log.temp.info("===初始化活动循环：serverId={} , sn={} , round={}， openTime={}， closeTime={}", serverId, conf.sn, roundNum, timeOldOpen, timeOldEnd);
        }
    }


    private void checkBattlePass(){

    }

    public List<ActivityVo> getOpenActivityVoList() {
        Log.temp.info("===获取活动开启列表 {}：{}", Config.SERVER_ID, openSnList);
        List<ActivityVo> activityVoList = new ArrayList<>();
        int now = (int)(Port.getTime()/Time.SEC);
        // 添加开启中的活动
        for(int activitySn : openSnList){
            ActivityVo activityVo = createActivityVo(activitySn, false);
            if (activityVo != null && now < activityVo.closeTime) {
                activityVoList.add(activityVo);
                Log.temp.info("===活动开启列表：{},{},{},{},{}", activityVo.activitySn, activityVo.round, ConfActivityControl.get(activitySn).is_round,
                        Utils.formatTime(activityVo.openTime*Time.SEC, "yyyy-MM-dd HH:mm:ss"),
                        Utils.formatTime(activityVo.closeTime*Time.SEC, "yyyy-MM-dd HH:mm:ss"));
            }
        }
        
        // 添加展示期的活动
        for(int activitySn : endShowSnList){
            ActivityVo activityVo = createActivityVo(activitySn, true);
            if (activityVo != null && now < activityVo.endShowTime) {
                activityVoList.add(activityVo);
                Log.temp.info("===活动展示期列表：{},{},{},{},{}", activityVo.activitySn, activityVo.round, ConfActivityControl.get(activitySn).is_round,
                        Utils.formatTime(activityVo.openTime*Time.SEC, "yyyy-MM-dd HH:mm:ss"),
                        Utils.formatTime(activityVo.closeTime*Time.SEC, "yyyy-MM-dd HH:mm:ss"));
            }
        }
        
        return activityVoList;
    }


    public List<ActivityVo> getOpenActivityVoList(int timeType) {
        Log.temp.info("===获取活动开启列表 {}：{}", Config.SERVER_ID, openSnList);
        List<ActivityVo> activityVoList = new ArrayList<>();
        // 添加开启中的活动
        for(int activitySn : openSnList){
            ConfActivityControl conf = ConfActivityControl.get(activitySn);
            if(conf.timeType == timeType){
                continue;
            }
            ActivityVo activityVo = createActivityVo(activitySn, false);
            if (activityVo != null) {
                activityVoList.add(activityVo);
                Log.temp.info("===活动开启列表：{},{},{},{},{}", activityVo.activitySn, activityVo.round, ConfActivityControl.get(activitySn).is_round,
                        Utils.formatTime(activityVo.openTime*Time.SEC, "yyyy-MM-dd HH:mm:ss"),
                        Utils.formatTime(activityVo.closeTime*Time.SEC, "yyyy-MM-dd HH:mm:ss"));
            }
        }

        // 添加展示期的活动
        for(int activitySn : endShowSnList){
            ConfActivityControl conf = ConfActivityControl.get(activitySn);
            if(conf.timeType == timeType){
                continue;
            }
            ActivityVo activityVo = createActivityVo(activitySn, true);
            if (activityVo != null) {
                activityVoList.add(activityVo);
                Log.temp.info("===活动展示期列表：{},{},{},{},{}", activityVo.activitySn, activityVo.round, ConfActivityControl.get(activitySn).is_round,
                        Utils.formatTime(activityVo.openTime*Time.SEC, "yyyy-MM-dd HH:mm:ss"),
                        Utils.formatTime(activityVo.closeTime*Time.SEC, "yyyy-MM-dd HH:mm:ss"));
            }
        }

        return activityVoList;
    }

    /**
     * 创建活动VO对象
     */
    private ActivityVo createActivityVo(int activitySn, boolean isShowPeriod) {
        ActivityVo activityVo = new ActivityVo();
        ActivityRoundData roundData = snRountDataMap.get(activitySn);
        if(roundData != null){
            activityVo.round = roundData.getRound();
            activityVo.openTime = (int)(roundData.getOpenTime()/Time.SEC);
            activityVo.closeTime = (int)(roundData.getCloseTime()/Time.SEC);
        } else {
            int type = snTypeMap.get(activitySn);
            ActivityData data = typeDataMap.get(type);
            if(data != null){
                activityVo.round = data.getRound();
                activityVo.openTime = (int)(data.getOpenTime()/Time.SEC);
                activityVo.closeTime = (int)(data.getCloseTime()/Time.SEC);
            }else {
                Log.temp.error("===ConfActivityControl配表错误，未找到活动数据。type={}, sn={}", type, activitySn);
                return null;
            }
        }
        
        activityVo.activitySn = activitySn;
        activityVo.isShowPeriod = isShowPeriod;
        if (isShowPeriod && showEndTimeMap.containsKey(activitySn)) {
            activityVo.endShowTime = (int)(showEndTimeMap.get(activitySn)/Time.SEC);
        }
        
        return activityVo;
    }

    public boolean isOpen(int activitySn){
        if(openSnList.contains(activitySn)){
            return true;
        }
        return false;
    }

    /**
     * 检测活动是否关闭
     */
    public void checkActivityClose(){
        long timeNow = Port.getTime();
        List<Integer> removeList = new ArrayList<>();
        List<Integer> endShowList = new ArrayList<>();
        // 检查开启中的活动是否需要关闭
        for(int activitySn : openSnList){
            ConfActivityControl conf = ConfActivityControl.get(activitySn);
            if(conf == null){
                continue;
            }
            if(conf.isClose == 1){
                removeList.add(conf.sn);
                continue;
            }
            if(serverId != Config.SERVER_ID && conf.timeType != ParamKey.activityTimeType_2){
                continue;
            }
            ActivityRoundData roundData = snRountDataMap.get(activitySn);
            if(roundData != null){
//                Log.temp.info("====活动sn={}, openTime={}, closeTime={}", activitySn, Utils.formatTime(roundData.getOpenTime(), "yyyy-MM-dd HH:mm:ss"), Utils.formatTime(roundData.getCloseTime(), "yyyy-MM-dd HH:mm:ss"));
                if(conf.type == 33){
                    // 跨服战活动常驻，不在开启区间内的关闭，其他常驻开启不关闭
                    ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.跨服战服务器开启区间.SN);
                    int minServerId = confGlobal.intArray[0] + Config.GAME_SERVER_PREFIX_VALUE;
                    int maxServerId = confGlobal.intArray[1] + Config.GAME_SERVER_PREFIX_VALUE;
                    if(Config.SERVER_ID < minServerId || Config.SERVER_ID > maxServerId){
                        removeList.add(conf.sn);
                        Log.crossWar.info("跨服战活动关闭，serverId={} 不在{}到{}之间", Config.SERVER_ID, minServerId, maxServerId);
                        continue;
                    }
                }
                if(roundData.getCloseTime() <= timeNow) {
                    // 活动结束
                    if (conf.showTime > 0) {
                        // 有展示期，移到展示期列表
                        endShowList.add(activitySn);
                        long showEndTime = roundData.getCloseTime() + conf.showTime * Time.SEC;
                        showEndTimeMap.put(activitySn, showEndTime);
                    } else {
                        // 无展示期，直接移除
                        removeList.add(activitySn);
                    }
                    onActivityClose(serverId, activitySn, roundData.getRound());
                }
                if(roundData.getRound() == roundData.getActivitySn()){
                    Log.temp.error("===循环活动出错：{},{}", roundData.getActivitySn(), roundData.getRound());
                }
            }
            int type = snTypeMap.get(activitySn);
            ActivityData activityData = typeDataMap.get(type);
            if(activityData != null){
//                Log.temp.info("====活动sn={}, openTime={}, closeTime={}", activitySn, Utils.formatTime(activityData.getOpenTime(), "yyyy-MM-dd HH:mm:ss"), Utils.formatTime(activityData.getCloseTime(), "yyyy-MM-dd HH:mm:ss"));
                if(snRountDataMap.containsKey(activitySn)){
                    activityData.remove();
                    continue;
                }
                if(openSnList.contains(activityData.getActivitySn())){
                    if(activityData.getCloseTime() <= timeNow) {
                        // 活动结束
                        if (conf.showTime > 0) {
                            // 有展示期，移到展示期列表
                            endShowList.add(activitySn);
                            long showEndTime = activityData.getCloseTime() + conf.showTime * Time.SEC;
                            showEndTimeMap.put(activitySn, showEndTime);
                        } else {
                            // 无展示期，直接移除
                            removeList.add(activitySn);
                        }
                        onActivityClose(serverId, activitySn, activityData.getRound());
                    }
                }
                if(activityData.getRound() != activityData.getActivitySn()){
                    Log.temp.error("===非循环活动出错：{},{}", activityData.getActivitySn(), activityData.getRound());
                }
            }
        }
        
        // 检查展示期的活动是否需要结束展示
        List<Integer> endShowRemoveList = new ArrayList<>();
        List<Integer> endShowRemoveNotType2List  = new ArrayList<>();
        for (int activitySn : endShowSnList) {
            Long showEndTime = showEndTimeMap.get(activitySn);
            if (showEndTime != null && timeNow >= showEndTime) {
                endShowRemoveList.add(activitySn);
                ConfActivityControl conf = ConfActivityControl.get(activitySn);
                if(conf.timeType != ParamKey.activityTimeType_2){
                    endShowRemoveNotType2List.add(activitySn);
                }
            }
        }
        List<Integer> removeNotType2List  = new ArrayList<>();
        List<Integer> endShowNotType2List  = new ArrayList<>();
        // 处理需要从开启列表移除的活动
        for(Integer activitySn : removeList) {
            openSnList.remove(activitySn);
            ConfActivityControl conf = ConfActivityControl.get(activitySn);
            if(conf.timeType != ParamKey.activityTimeType_2){
                removeNotType2List.add(activitySn);
            }
        }
        
        // 处理需要从开启列表移到展示期列表的活动
        for(Integer activitySn : endShowList) {
            openSnList.remove(activitySn);
            if (!endShowSnList.contains(activitySn)) {
                endShowSnList.add(activitySn);

            }
            ConfActivityControl conf = ConfActivityControl.get(activitySn);
            if(conf.timeType != ParamKey.activityTimeType_2){
                endShowNotType2List.add(activitySn);
            }
        }
        
        // 处理需要从展示期列表移除的活动
        for(Integer activitySn : endShowRemoveList) {
            endShowSnList.remove(activitySn);
            showEndTimeMap.remove(activitySn);
            ConfActivityControl conf = ConfActivityControl.get(activitySn);
            IActivityControl control = ActivityControlTypeFactory.getTypeData(conf.type);
            if (control != null) {
                control.endShowServerData(serverId, activitySn);
            }

            // 清理相关数据
//            ConfActivityControl conf = ConfActivityControl.get(activitySn);
//            if (conf != null) {
//                if (conf.is_round == 1) {
//                    ActivityRoundData roundData = snRountDataMap.remove(activitySn);
//                    if (roundData != null) {
//                        roundData.remove();
//                    }
//                } else {
//                    int type = snTypeMap.getOrDefault(activitySn, 0);
//                    if (type > 0) {
//                        ActivityData data = typeDataMap.remove(type);
//                        if (data != null) {
//                            data.remove();
//                        }
//                    }
//                }
//                snServerDataMap.remove(activitySn);
//                snTypeMap.remove(activitySn);
//            }
        }
        
        // 发送活动关闭通知
        if(!removeList.isEmpty() || !endShowList.isEmpty()){
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            List<Integer> combineList = new ArrayList<>(removeList);
            combineList.addAll(endShowList);
            proxy.closeActivity(serverId, combineList);
            Log.temp.error("===关闭活动：serverId={} , {},{} , openSnList={}", serverId, removeList, endShowList, openSnList);
        }

        // 发送活动展示期结束通知
        if(!endShowRemoveList.isEmpty()){
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.endShowActivity(serverId, endShowRemoveList);
            Log.temp.error("===活动展示期结束：serverId={} , {} , endShowRemoveList={}", serverId, endShowList, endShowSnList);
        }

        // 处理子服
        if(!removeNotType2List.isEmpty() || !endShowNotType2List.isEmpty()){
            if(S.isServerMerge){
                List<Integer> serverIdList = Util.getMergeServerIdList(serverId);
                List<Integer> combineList = new ArrayList<>(removeNotType2List);
                combineList.addAll(endShowNotType2List);
                for(int serverIdTemp : serverIdList){
                    if(serverIdTemp == serverId){
                        continue;
                    }
                    HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
                    proxy.closeActivity(serverIdTemp, combineList);
                    Log.temp.error("===关闭活动：serverId={} , {},{} , openSnList={}", serverIdTemp, removeList, endShowList, openSnList);
                }
            }
        }
        if(!endShowRemoveNotType2List.isEmpty()){
            if(S.isServerMerge){
                List<Integer> serverIdList = Util.getMergeServerIdList(serverId);
                for(int serverIdTemp : serverIdList){
                    if(serverIdTemp == serverId){
                        continue;
                    }
                    HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
                    proxy.endShowActivity(serverIdTemp, endShowRemoveNotType2List);
                    Log.temp.error("===关闭活动：serverId={} , {},{} , openSnList={}", serverIdTemp, removeList, endShowList, openSnList);
                }
            }
            Log.temp.error("===活动展示期结束：serverId={} , {} , endShowRemoveList={}", serverId, endShowList, endShowSnList);
        }

        removeList.clear();
        removeNotType2List.clear();

        endShowList.clear();
        endShowNotType2List.clear();

        endShowRemoveList.clear();
        endShowRemoveNotType2List.clear();
    }

    /**
     * 检测活动是否开启
     */
    public void checkActivityOpen(){
        long timeNow = Port.getTime();
        if (!isLoadTypeFinish || !isLoadRoundFinish) {
            Log.activity.error("服务器活动数据还没加载完成，不允许检测活动开启，防止重复生成数据！serverId={}, isLoadActivityDataFinish={}, isLoadActivityRoundDataFinish={}", serverId, isLoadTypeFinish, isLoadRoundFinish);
            return;
        }
		if(checkGmTT != null && checkGmTT.isPeriod(timeNow)){
			checkGmActivityType(timeNow);
		}
        List<ActivityVo> activityVoList = new ArrayList<>();
        List<ActivityVo> activityVoNotType2List = new ArrayList<>();
        for(ConfActivityControl conf : ConfActivityControl.findAll()){
            if(openSnList.contains(conf.sn)){
                continue;
            }
            if(conf.isClose == 1){
                continue;
            }
            if(serverId != Config.SERVER_ID && conf.timeType != ParamKey.activityTimeType_2){
                continue;
            }
            if(isMergeServerContinueActivity(conf.type, timeNow)){
                continue;
            }
            if(serverId != Config.SERVER_ID && Util.getMergeServerIdList(Config.SERVER_ID).size() > 1 && conf.timeType != ParamKey.activityTimeType_2){
                continue;
            }
            if(isOpen(conf)){
                // 不循环活动不允许存在循环活动中
                if(conf.is_round == 0 && snRountDataMap.containsKey(conf.sn)){
                    Log.temp.error("===ConfActivityControl配表错误，不循环活动不能和循环活动混合。type={}, sn={}", conf.type, conf.sn);
                    continue;
                }
                openSnList.add(conf.sn);
                snTypeMap.put(conf.sn, conf.type);
                int activityType = snTypeMap.get(conf.sn); // 获取活动类型
                ActivityData data = typeDataMap.get(activityType); // 根据活动类型获取数据
                ActivityRoundData roundData = snRountDataMap.get(conf.sn);

                ActivityVo activityVo = new ActivityVo();
                activityVo.activitySn = conf.sn;
                if(roundData != null){
                    activityVo.round = roundData.getRound();
                    activityVo.openTime = (int)(roundData.getOpenTime()/Time.SEC);
                    activityVo.closeTime = (int)(roundData.getCloseTime()/Time.SEC);
                } else {
                    activityVo.round = data.getRound();
                    activityVo.openTime = (int)(data.getOpenTime()/Time.SEC);
                    activityVo.closeTime = (int)(data.getCloseTime()/Time.SEC);
                }
                activityVoList.add(activityVo);
                if(conf.timeType != ParamKey.activityTimeType_2){
                    activityVoNotType2List.add(activityVo);
                }
            }
        }
        if(!activityVoList.isEmpty()){
            HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
            proxy.openActivity(serverId, activityVoList);
            Log.temp.error("===开启活动：serverId={}:{}, openSnList={}",
                    serverId, activityVoList.stream().map(vo -> vo.activitySn).collect(Collectors.toList()), openSnList);
            if(S.isServerMerge && !activityVoNotType2List.isEmpty()){
                List<Integer> serverIdList = Util.getMergeServerIdList(serverId);
                for(int serverIdTemp : serverIdList){
                    if(serverIdTemp == serverId){
                        continue;
                    }
                    HumanGlobalServiceProxy proxyTemp = HumanGlobalServiceProxy.newInstance();
                    proxyTemp.openActivity(serverIdTemp, activityVoNotType2List);
                }
            }
        }
        activityVoList.clear();
    }
	
	private void checkGmActivityType(long timeNow){
        if(!isUseMergeConf){
            return;
        }
        isUseMergeConf = false;
		for(ConfActivityControl conf : ConfActivityControl.findAll()){
			if(!typeSet.contains(conf.type)){
                continue;
            }
            if(openSnList.contains(conf.sn)){
                continue;
            }
            if(conf.isClose == 1){
                continue;
            }
            if(serverId != Config.SERVER_ID && conf.timeType != ParamKey.activityTimeType_2){
                continue;
            }
            if(serverId != Config.SERVER_ID && Util.getMergeServerIdList(Config.SERVER_ID).size() > 1 && conf.timeType != ParamKey.activityTimeType_2){
                continue;
            }
            if(conf.is_round != 1){
                continue;
            }
            // gm类不安全，copy过来
            gm_timeType_3_init(conf, serverId, timeNow,  snRountDataMap);
        }
		Log.activity.info("=========执行类型活动检测循环活动轮次, typeSet={}", typeSet);
		for (ActivityRoundData roundData : snRountDataMap.values()) {
			int actType = roundData.getActivityType();
			if (roundData.getOpenTime() <= Port.getTime() && Port.getTime() < roundData.getCloseTime()) {
				if (!openSnList.contains(roundData.getActivitySn())) {
					openSnList.add(roundData.getActivitySn());
					snTypeMap.put(roundData.getActivitySn(), actType);
				}
			}
		}
        typeSet.clear();
        checkGmTT = null;
	}
	

    private boolean isMergeServerContinueActivity(int activityType, long timeNow){
        Map<Integer, ServerMerge> confMap = GlobalConfVal.getServerMergeMap();
        for(ServerMerge merge : confMap.values()){
            if (merge.validTime > timeNow || merge.invalidTime < timeNow) {
                continue;
            }
            if (!merge.serverSets.contains(Utils.getServerIdTo(serverId))) {
                continue;
            }
            if (merge.mergeStartTime > timeNow || merge.mergeEndTime < timeNow) {
                continue;
            }
            if(!isUseMergeConf){
                isUseMergeConf = true;
                typeSet.clear();
                typeSet.addAll(merge.activityTypeSets);
                long interval = merge.mergeEndTime - timeNow;
                checkGmTT = new TickTimer(interval);
                Log.activity.info("触发合服检测脚本, serverId={}, typeSet={}, sn={}, mergeEndTime={}, interval={}", serverId, typeSet, merge.id,  merge.mergeEndTime, interval);
            }
            if (!merge.activityTypeSets.contains(activityType)) {
                continue;
            }
            return true;
        }
        return false;
    }

    private void checkActivityData(ConfActivityControl conf, long openTime, long closeTime){

        if(conf.is_round == 1){// 循环活动-
            if(!snRountDataMap.containsKey(conf.sn)){
                Log.temp.info("===循环活动 type={}, sn={}, round= 1", conf.type, conf.sn);
                createActivityRoundData(conf.sn, conf.type, 1, openTime, closeTime);
            }
        } else {
            // 非循环活动
            if(!typeDataMap.containsKey(conf.type)){
                int round = (conf.sn >= ParamKey.activity_10000) ? conf.sn :  1;
                Log.temp.info("===非循环活动。type={}, sn={}, round= {}", conf.type, conf.sn, round);
                createActivityData(conf.sn, conf.type, round, openTime, closeTime);
            } else {
                ActivityData activityData = typeDataMap.get(conf.type);
                int round = (conf.sn >= ParamKey.activity_10000) ? conf.sn : activityData.getRound() + 1;
                round = conf.is_round == 0 ? conf.sn : round;
                if (activityData.getActivitySn() != conf.sn) {
                    // 说明开了新的活动，需要初始化数据
                    Log.temp.info("===非循环活动。type={}, sn={}, round= {}", conf.type, conf.sn, round);
                    IActivityControl control = ActivityControlTypeFactory.getTypeData(conf.type);
                    IServerActData serverActData = control.initServerActData(round, activityData);
                    if (serverActData != null) {
                        snServerDataMap.put(conf.sn, serverActData);
                        activityData.setActData(serverActData.toJSONString());
                    }
                }
                if(activityData.getOpenTime() != openTime || activityData.getCloseTime() != closeTime) {
                    activityData.setActivitySn(conf.sn);
                    activityData.setOpenTime(openTime);
                    activityData.setCloseTime(closeTime);
                    activityData.setRound(round);
                    activityData.update();
                }
            }
        }
    }



    private boolean isOpen(ConfActivityControl conf){
        switch (conf.timeType){
            case ParamKey.activityTimeType_1:
                return timeType_1(conf);
            case ParamKey.activityTimeType_2:
                return timeType_2(conf);
            case ParamKey.activityTimeType_3:
                return timeType_3(conf);
            case ParamKey.activityTimeType_4:
                return false;
            case ParamKey.activityTimeType_5:
                return timeType_5(conf);
            case ParamKey.activityTimeType_6:
                return timeType_6(conf);
            default:
                Log.temp.error("====ConfActivityControl配表错误，无此类型代码。timeType={}, sn={}", conf.timeType, conf.sn);
                break;
        }
        return false;
    }

    private boolean timeType_1(ConfActivityControl conf){
        if(conf.is_round == 1){
//            Log.temp.error("===ConfActivityControl配表错误，is_round={}, timeType={}, sn={}", conf.is_round, conf.timeType, conf.sn);
            return false;
        }
        String[] timeArr = Utils.splitStr(conf.time, "\\|");
        if(timeArr == null || timeArr.length < 2){
            Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
            return false;
        }
        Long openTime = Utils.formatTimeToLong(timeArr[0]);
        Long closeTime = Utils.formatTimeToLong(timeArr[1]);
        if(openTime >= closeTime){
//            Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
            return false;
        }
        long timeNow = Port.getTime();
        if(timeNow >= closeTime || timeNow < openTime){
            return false;
        }
        checkActivityData(conf, openTime, closeTime);
        return true;
    }

    private boolean timeType_2(ConfActivityControl conf){
        if(conf.is_round == 1){
            Log.temp.error("===ConfActivityControl配表错误，is_round={}, timeType={}, sn={}", conf.is_round, conf.timeType, conf.sn);
            return false;
        }
        String[] timeArr = Utils.splitStr(conf.time, "\\|");
        if(timeArr == null || timeArr.length < 2){
            Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
            return false;
        }
        int openDay = Utils.intValue(timeArr[0]);
        int closeDay = Utils.intValue(timeArr[1]);
        long openServerTime = Util.getOpenServerTime(serverId);
        long openTime = Utils.getOffDayTime(openServerTime, openDay - 1, 0);
        long closeTime  = Utils.getOffDayTime(openServerTime, closeDay, 0) - Time.SEC;
        if(openTime > closeTime){
            Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
            return false;
        }
        long timeNow = Port.getTime();
        if(timeNow >= closeTime || timeNow < openTime){
            return false;
        }
        checkActivityData(conf, openTime, closeTime);
        return true;
    }

    private boolean timeType_3(ConfActivityControl conf){
        if(conf.is_round != 1){
            Log.temp.error("===ConfActivityControl配表错误，is_round={}, timeType={}, sn={}", conf.is_round, conf.timeType, conf.sn);
            return false;
        }
        // 跨服战特殊处理，不在服务器开启区间内的，不开活动
        if(conf.type == 33){
            ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.跨服战服务器开启区间.SN);
            int minServerId = confGlobal.intArray[0] + Config.GAME_SERVER_PREFIX_VALUE;
            int maxServerId = confGlobal.intArray[1] + Config.GAME_SERVER_PREFIX_VALUE;
            if(Config.SERVER_ID < minServerId || Config.SERVER_ID > maxServerId){
                Log.crossWar.info("跨服战活动不开启，serverId={} 不在{}到{}之间", Config.SERVER_ID, minServerId, maxServerId);
                return false;
            }
            ActivityRoundData roundData = snRountDataMap.get(conf.sn);
            if(roundData != null){
                // 当前开启状态
                if(roundData.getOpenTime() <= Port.getTime() && Port.getTime() <= roundData.getCloseTime()){
                    return true;
                }
            }
        }

        int[] timeArr = Utils.arrayStrToInt(conf.time);
        if(timeArr.length < 3){
            Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
            return false;
        }
        long timeNow = Port.getTime();
        long openServerTime = Util.getOpenServerTime(serverId);
        long restrictTime = Utils.getOffDayTime(openServerTime, conf.openTime, 0);
        if(restrictTime > timeNow){ // 限制时间未到，走开服活动期数
            return isOpenActivityTerm(conf);
        }
        int week = Utils.getDayOfWeek(openServerTime);
        ConfActivityAdjust confActivityAdjust = ConfActivityAdjust.get(GlobalConfVal.getConfActivityAdjustSn(conf.type, week));
        if(confActivityAdjust == null){
            Log.temp.error("===ConfActivityControl配表错误，未找到ConfActivityAdjust数据。type={}, week={},sn={}", conf.type, week, conf.sn);
            return false;
        }
        int round = confActivityAdjust.day.length;
        ActivityRoundData roundData = snRountDataMap.get(conf.sn);
        if(roundData == null || roundData.getRound() < round){
            return isOpenActivityTerm(conf);
        }
        if(confActivityAdjust.day.length == 0){
            Log.temp.error("===ConfActivityControl配表错误，未找到ConfActivityAdjust数据。type={}, week={},sn={}", conf.type, week, conf.sn);
            return false;
        }
        int day = confActivityAdjust.day[confActivityAdjust.day.length-1][1] - confActivityAdjust.day[confActivityAdjust.day.length-1][0] + 1;
        if(day <= 0){
            return false;
        }
        int openWeek = Utils.intValue(timeArr[0]);
        int openHour = Utils.intValue(timeArr[1]);
        int openMin = Utils.intValue(timeArr[2]);
        long openTime = Utils.getTimeOfWeek(timeNow, openWeek == 1 ? 7 : openWeek - 1, openHour) + openMin * Time.MIN;
        if(openTime < restrictTime){
            return false;
        }
        if (roundData.getCloseTime() >= openTime) {
            return false;
        }
        long cdTime = roundData.getOpenTime() + conf.round_day * Time.DAY - 5 * Time.SEC;
        if(cdTime > timeNow || cdTime > openTime){
            return false;
        }
        long closeTime = Utils.getOffDayTime(openTime, day, 0) - Time.SEC;
        if(openTime <= timeNow && timeNow < closeTime){
            updateActivityRoundData(roundData, roundData.getRound() + 1, openTime, closeTime);
            return true;
        }
        return false;
    }

    /**
     * 合服前的活动时间类型
     * 开启就放入typeDataMap中
     * 开启时间读取合服配置，结束时间为开启时间+活动持续天数
     */
    private boolean timeType_5(ConfActivityControl conf) {
        if (conf.is_round == 1) {
            Log.temp.error("===ConfActivityControl配表错误，is_round={}, timeType={}, sn={}", conf.is_round, conf.timeType, conf.sn);
            return false;
        }

        long timeNow = Port.getTime();
        Map<Integer, ServerMerge> confMap = GlobalConfVal.getServerMergeMap();

        for (ServerMerge merge : confMap.values()) {
            if (!merge.serverSets.contains(Utils.getServerIdTo(serverId))) {
                continue;
            }

            long openTime = merge.mergePartyTime;

            String[] timeStr = Utils.splitStr(conf.time, Symbol.SHUXIAN_REG);
            if (timeStr == null || timeStr.length < 2) {
                Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
                return false;
            }

            int durationDays = Utils.intValue(timeStr[1]);
            if (durationDays <= 0) {
                Log.temp.error("===ConfActivityControl配表错误，持续天数必须大于0，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
                return false;
            }

            long closeTime = openTime + (long)durationDays * Time.DAY - Time.SEC;

            if (timeNow >= openTime && timeNow < closeTime) {
                checkActivityData(conf, openTime, closeTime);
                return true;
            }
        }

        return false;
    }

    /**
     * 合服后的活动时间类型
     */
    private boolean timeType_6(ConfActivityControl conf) {
        if (conf.is_round == 1) {
            Log.temp.error("===ConfActivityControl配表错误，is_round={}, timeType={}, sn={}", conf.is_round, conf.timeType, conf.sn);
            return false;
        }

        long timeNow = Port.getTime();
        long openTime = DateTimeUtils.getStartMergeServerTime();

        if (openTime <= 0) {
            return false;
        }

        Map<Integer, ServerMerge> confMap = GlobalConfVal.getServerMergeMap();
        long closeTime = 0;
        long durationTime = 0;
        for (ServerMerge merge : confMap.values()) {
            if (Util.getServerIdReal(serverId) == merge.mainServerId &&
                timeNow >= merge.validTime && timeNow <= merge.invalidTime) {

                String[] timeStr = Utils.splitStr(conf.time, Symbol.SHUXIAN_REG);
                if (timeStr == null || timeStr.length < 2) {
                    Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
                    return false;
                }

                int durationDays = Utils.intValue(timeStr[1]);
                if (durationDays <= 0) {
                    Log.temp.error("===ConfActivityControl配表错误，持续天数必须大于0，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
                    return false;
                }
                durationTime = (long)durationDays * Time.DAY;
                closeTime = merge.mergePartyTime + durationTime - Time.SEC;
                break;
            }
        }

        if (closeTime == 0) {
            return false;
        }
        if(openTime < closeTime - durationTime ){
            return false;
        }

        if (timeNow >= openTime && timeNow < closeTime) {
            checkActivityData(conf, openTime, closeTime);
            return true;
        }

        return false;
    }

    private boolean isOpenActivityTerm(ConfActivityControl conf){

        long timeNow = Port.getTime();
        long openServerTime = Util.getOpenServerTime(serverId);
        int week = Utils.getDayOfWeek(openServerTime);
        ConfActivityAdjust confActivityAdjust = ConfActivityAdjust.get(GlobalConfVal.getConfActivityAdjustSn(conf.type, week));
        if(confActivityAdjust == null){
            Log.temp.error("===ConfActivityControl配表错误，未找到ConfActivityAdjust数据。type={}, week={},sn={}", conf.type, week, conf.sn);
            return false;
        }
        ActivityRoundData roundData = snRountDataMap.get(conf.sn);
        for(int i = 0; i < confActivityAdjust.day.length; i++){
            // 策划要求当天就要生效，所以要减一
            long openTimeTemp = Utils.getOffDayTime(openServerTime, confActivityAdjust.day[i][0]-1, 0);
            // 策划要求所有配置时间包含当天23：59.59，又因为取0点时间所以这里需要并减去1秒
            long closeTimeTemp = Utils.getOffDayTime(openServerTime, confActivityAdjust.day[i][1], 0) - Time.SEC;

            if(openTimeTemp <= timeNow && closeTimeTemp >= timeNow){
                if(roundData == null){
                    Log.temp.error("===循环活动开启， activitySn={}, round={}, openTime={}, closeTime={}", conf.sn, i+1, openTimeTemp, closeTimeTemp);
                    createActivityRoundData(conf.sn,conf.type, i+1, openTimeTemp, closeTimeTemp);
                } else {
                    Log.temp.error("===循环活动开启， activitySn={}, round={}, num={}, confRound={} openTime={}, closeTime={}", conf.sn,
                            roundData.getRound() + 1, confActivityAdjust.day.length, i+1, openTimeTemp, closeTimeTemp);
                    updateActivityRoundData(roundData , roundData.getRound() > confActivityAdjust.day.length ? roundData.getRound() + 1 : i+1, openTimeTemp, closeTimeTemp);
                }
                checkActivityData(conf, openTimeTemp, closeTimeTemp);
                return true;
            }
        }
        return false;
    }

    private void updateActivityRoundData(ActivityRoundData roundData,int round, long openTime, long closeTime){
        Log.temp.error("===更新活动轮次数据。sn={}, round={}, openTime={}, closeTime={}", roundData.getActivitySn(), round, openTime, closeTime);
        roundData.setRound(round);
        roundData.setOpenTime(openTime);
        roundData.setCloseTime(closeTime);
        roundData.setUpdateTime(Port.getTime());
        IActivityControl control = ActivityControlTypeFactory.getTypeData(roundData.getActivityType());
        if (control != null) {
            IServerActData serverActData = control.initServerActData(round);
            if (serverActData != null) {
                snServerDataMap.put(roundData.getActivitySn(), serverActData);
                roundData.setActData(serverActData.toJSONString());
            }
        }
        roundData.update();
        snRountDataMap.put(roundData.getActivitySn(), roundData);
    }


    public List<Integer> getActivityTaskGroup(int activitySn){
        List<Integer> taskGroupList = new ArrayList<>();
        int confSn= 0;
        ActivityRoundData roundData = snRountDataMap.get(activitySn);
        if(roundData != null){
            confSn = ActivityManager.inst().getActivityTermSn(roundData.getActivityType(), roundData.getRound());
        } else {
            int type = snTypeMap.get(activitySn);
            ActivityData data = typeDataMap.get(type);
            if (data != null) {
                confSn = ActivityManager.inst().getActivityTermSn(type, data.getActivityType());
            }
        }
        ConfActivityTerm conf = ConfActivityTerm.get(confSn);
        if(conf == null || conf.task_list == null || conf.task_list.length == 0){
            return taskGroupList;
        }
        return Utils.intArrToList(conf.task_list);
    }

    private void createActivityData(int activitySn,int type, int round, long openTime, long closeTime){
        ActivityData typeData = new ActivityData();
        if(typeDataMap.containsKey(type)){
            return;
        }
        typeData.setId(Port.applyId());
        typeData.setActivitySn(activitySn);
        typeData.setRound(activitySn);
        typeData.setActivityType(type);
        typeData.setServerId(serverId);
        typeData.setOpenTime(openTime);
        typeData.setCloseTime(closeTime);
        typeData.setUpdateTime(Port.getTime());
        IActivityControl control = ActivityControlTypeFactory.getTypeData(type);
        if (control != null) {
            IServerActData serverActData = control.initServerActData(round, typeData);
            if (serverActData != null) {
                snServerDataMap.put(activitySn, serverActData);
                typeData.setActData(serverActData.toJSONString());
            }
        }
        typeData.persist();
        typeDataMap.put(type, typeData);
    }


    private void createActivityRoundData(int activitySn, int type, int round, long openTime, long closeTime){
        if (snRountDataMap.containsKey(activitySn)) {
            return;
        }
        ActivityRoundData roundData = new ActivityRoundData();
        roundData.setId(Port.applyId());
        roundData.setActivitySn(activitySn);
        roundData.setActivityType(type);
        roundData.setRound(round);
        roundData.setServerId(serverId);
        roundData.setOpenTime(openTime);
        roundData.setCloseTime(closeTime);
        roundData.setUpdateTime(Port.getTime());
        IActivityControl control = ActivityControlTypeFactory.getTypeData(type);
        if (control != null) {
            IServerActData serverActData = control.initServerActData(round);
            if (serverActData != null) {
                snServerDataMap.put(activitySn, serverActData);
                roundData.setActData(serverActData.toJSONString());
            }
        }
        roundData.persist();
        snRountDataMap.put(activitySn, roundData);
    }

    private void onActivityClose(int serverId, int activitySn, int round){
        Service actControlService = Port.getCurrent().getServices(D.SERV_ACTIVITY);
        if(serverId == Config.SERVER_ID && S.isServerMerge){
            ConfActivityControl conf = ConfActivityControl.get(activitySn);
            if(conf.timeType != ParamKey.activityTimeType_2){
                List<Integer> serverIdList = Util.getMergeServerIdList(serverId);
                for(int serverIdTemp : serverIdList){
                    if(serverIdTemp == serverId){
                        continue;
                    }
                    actControlService.scheduleOnce(new ScheduleTask() {
                        @Override
                        public void execute() {
                            //登录角色信息检查
                            distributReward(serverIdTemp, activitySn, round);
                            settleServerActData(serverIdTemp, activitySn, round);
                        }
                    }, DELAY_DISTRIBUTE_TIME * Time.SEC);
                }
            }
        }

        actControlService.scheduleOnce(new ScheduleTask() {
            @Override
            public void execute() {
                //登录角色信息检查
                distributReward(serverId, activitySn, round);
                settleServerActData(serverId, activitySn, round);
            }
        }, DELAY_DISTRIBUTE_TIME * Time.SEC);
    }

    private void settleServerActData(int serverId, int activitySn, int round) {
        ConfActivityControl conf = ConfActivityControl.get(activitySn);
        IActivityControl control = ActivityControlTypeFactory.getTypeData(conf.type);
        control.settleServerActData(serverId, activitySn, round);
    }

    private void distributReward(int serverId, int activitySn, int round){
        ConfActivityControl confActivityControl = ConfActivityControl.get(activitySn);
        if(confActivityControl == null){
            Log.temp.error("===ConfActivityControl，未找到数据。sn={}", activitySn);
            return;
        }
        int termSn = ActivityManager.inst().getActivityTermSn(confActivityControl.type, round);
        ConfActivityTerm conf = ConfActivityTerm.get(termSn);
        if(conf == null){
            Log.temp.error("===ConfActivityTerm配表错误，未找到数据。sn={}", termSn);
            return;
        }
        if(conf.rank_id == 0){
            return;
        }
        ConfRanktype confRanktype = ConfRanktype.get(conf.rank_id);
        if(confRanktype == null){
            Log.temp.error("===ConfRanktype配表错误，未找到数据。rank_id={}", conf.rank_id);
            return;
        }

        if (confRanktype.crossType > 0) {
            Log.activity.info("排行榜为跨服排行榜，不在游服做结算！actSn={}, round={}", activitySn, round);
            return;
        }
        String redisLockKey = RankManager.inst().getRedisRankTypeLockKey(serverId, confRanktype);
        RedisTools.lockWithExpireTimeAsync(EntityManager.redisClient, redisLockKey, "1", 3600, lockRes -> {
            if (lockRes.failed() || !lockRes.result()) {
                Log.game.error("===获取锁失败，无法进行活动结算。serverId={}, activitySn={}, round={}", serverId, activitySn, round);
                return;
            }

            String redisKey = RankManager.inst().getRedisRankTypeKey(serverId, confRanktype);
            List<Integer> snList = GlobalConfVal.getConfActivityRankRewardSnList(conf.type, conf.group_id);
            if(snList == null || snList.isEmpty()){
                Log.game.error("===ConfActivityRankReward配表错误， type={}， groupId={}", conf.type, conf.group_id);
                return;
            }

            // 先获取排行榜长度
            RedisTools.getRankLen(EntityManager.redisClient, redisKey, lenRes -> {
                if (lenRes.failed()) {
                    Log.game.error("获取排行榜长度失败, rankSn={}, error={}", redisKey, lenRes.cause().getMessage());
                    return;
                }

                int totalLen = Utils.intValue(lenRes.result());
                if (totalLen == 0) {
                    Log.game.error("获取排行榜数据为空,无需结算, rankSn={}", redisKey);
                    return;
                }
                totalLen = Math.min(totalLen, MAX_RANK); // 限制最大长度

                // 设置过期时间为一周
                RedisTools.expire(EntityManager.getRedisClient(), redisKey, Tool.WEEK);

                // 计算总页数
                int totalPages = totalLen / PAGE_SIZE + (totalLen % PAGE_SIZE == 0 ? 0 : 1);

                // 分页处理
                for (int page = 0; page < totalPages; page++) {
                    final int pageIndex = page;
                    Service actControlService = Port.getCurrent().getServices(D.SERV_ACTIVITY);
                    int finalTotalLen = totalLen;
                    actControlService.scheduleOnce(new ScheduleTask() {
                        @Override
                        public void execute() {
                            int start = pageIndex * PAGE_SIZE;
                            int end = Math.min(start + PAGE_SIZE - 1, finalTotalLen - 1);
                            processRankPage(redisKey, start, end, snList, activitySn, confRanktype);
                        }
                    }, pageIndex * SCHEDULE_DELAY);
                }
            });
        });
    }

    private void processRankPage(String redisKey, int start, int end, List<Integer> snList, int activitySn, ConfRanktype confRanktype) {
        RedisTools.getRankListByIndex(EntityManager.getRedisClient(), redisKey, start, end, false, res -> {
            if (!res.succeeded()) {
                Log.game.error("获取排行榜分页数据失败, rankSn={}, start={}, end={}", redisKey, start, end);
                return;
            }

            JsonArray jsonArray = res.result();
            if (jsonArray.isEmpty()) {
                return;
            }

            ConfActivityRankReward lastReward = null;

            for (int i = 0; i < jsonArray.size(); i++) {
                int currentRank = start + i + 1;
                boolean rewardDistributed = false;
                String rankId = jsonArray.getString(i);

                for (int sn : snList) {
                    ConfActivityRankReward reward = ConfActivityRankReward.get(sn);
                    if (reward == null) {
                        continue;
                    }
                    lastReward = reward;
                    if (currentRank >= reward.rank_range[0] && currentRank <= reward.rank_range[1]) {
                        RankManager.inst().distributeReward(rankId, currentRank, activitySn, confRanktype, reward);
                        rewardDistributed = true;
                        break;
                    }
                }

                if (lastReward == null) {
                    continue;
                }

                if (!rewardDistributed && currentRank >= lastReward.rank_range[0]) {
                    RankManager.inst().distributeReward(rankId, currentRank, activitySn, confRanktype, lastReward);
                }
            }
        });
    }


    @Override
    public void writeTo(OutputStream out) throws IOException {
        out.write(openSnList);
        out.write(endShowSnList);
        out.write(showEndTimeMap);
    }

    @Override
    public void readFrom(InputStream in) throws IOException {
        openSnList = in.read();
        endShowSnList = in.read();
        showEndTimeMap = in.read();
    }

    public String toJSON(){
        JSONObject jo = new JSONObject();
        jo.put("openSnList", openSnList);
        jo.put("endShowSnList", endShowSnList);
        return jo.toJSONString();
    }

    public void gmActivity(int type, String[] json) {
        if (type == 1) {
            List<Integer> snList = new ArrayList<>();
            for (ActivityData data : typeDataMap.values()) {
                data.remove();
                snList.add(data.getActivitySn());
            }
            typeDataMap.clear();
            openSnList.clear();
            for (Integer sn : snList) {
                snServerDataMap.remove(sn);
            }
            checkActivityOpen();
        } else if (type == 2) {
            List<Integer> snList = new ArrayList<>();
            for (ActivityRoundData data : snRountDataMap.values()){
                data.remove();
                snList.add(data.getActivitySn());
            }
            openSnList.clear();
            snRountDataMap.clear();
            for (Integer sn : snList) {
                snServerDataMap.remove(sn);
            }
        } else if (type == 3) {
            gmCheckCreateOpenActivity();
            checkActivityOpen();
        } else if (type == 4) {
            gmRegenerateServerData(json);
        } else if(type == 5){
            ConfActivityControl conf = ConfActivityControl.get(Utils.intValue(json[2]));
            if(conf == null){
                Log.temp.error("===ConfActivityControl，未找到数据。sn={}", json[2]);
                return;
            }
            GameDebugManager.inst().gm_timeType_3_init(conf, serverId, Port.getTime(), snRountDataMap);
            for (ActivityRoundData roundData : snRountDataMap.values()) {
                int actType = roundData.getActivityType();
                if (roundData.getOpenTime() <= Port.getTime() && Port.getTime() < roundData.getCloseTime()) {
                    if (!openSnList.contains(roundData.getActivitySn())) {
                        openSnList.add(roundData.getActivitySn());
                        snTypeMap.put(roundData.getActivitySn(), actType);
                    }
                }
            }
            Log.temp.error("===gm_timeType_3_init, conf.sn={}, serverId={}, time={}, openSnList={}", conf.sn, serverId, Port.getTime(), openSnList);
        } else if(type == 6){
            ConfActivityControl conf = ConfActivityControl.get(Utils.intValue(json[2]));
            if(conf == null){
                Log.temp.error("===ConfActivityControl，未找到数据。sn={}", json[2]);
                return;
            }
            ActivityRoundData roundData = snRountDataMap.get(conf.sn);
            if(roundData != null){
                roundData.remove();
                checkActivityOpen();
                if (roundData.getOpenTime() <= Port.getTime() && Port.getTime() < roundData.getCloseTime()) {
                    if (!openSnList.contains(roundData.getActivitySn())) {
                        openSnList.add(roundData.getActivitySn());
                        snTypeMap.put(roundData.getActivitySn(), roundData.getActivityType());
                    }
                }
            }

            ActivityRoundData roundData2 = snRountDataMap.get(100001);
            if(roundData2 != null){
                roundData.remove();
                checkActivityOpen();
                if (roundData2.getOpenTime() <= Port.getTime() && Port.getTime() < roundData2.getCloseTime()) {
                    if (!openSnList.contains(roundData2.getActivitySn())) {
                        openSnList.add(roundData2.getActivitySn());
                        snTypeMap.put(roundData2.getActivitySn(), roundData2.getActivityType());
                    }
                }
            }
            Log.temp.error("===gm_timeType_3_init, conf.sn={}, serverId={}, time={}, openSnList={}", conf.sn, serverId, Port.getTime(), openSnList);
        } else if(type == 7){
            ConfActivityControl conf = ConfActivityControl.get(Utils.intValue(json[2]));
            if(conf == null){
                Log.temp.error("===ConfActivityControl，未找到数据。sn={}", json[2]);
                return;
            }
            if(snRountDataMap.containsKey(conf.sn)){
                ActivityRoundData roundData = snRountDataMap.get(conf.sn);
                if(roundData != null){
                    snRountDataMap.remove(conf.sn);
                    roundData.remove();
                }
            }
            GameDebugManager.inst().gm_timeType_3_init(conf, serverId, Port.getTime(), snRountDataMap);
            for (ActivityRoundData roundData : snRountDataMap.values()) {
                int actType = roundData.getActivityType();
                if (roundData.getOpenTime() <= Port.getTime() && Port.getTime() < roundData.getCloseTime()) {
                    if (!openSnList.contains(roundData.getActivitySn())) {
                        openSnList.add(roundData.getActivitySn());
                        snTypeMap.put(roundData.getActivitySn(), actType);
                    }
                }
            }
            Log.temp.error("===gm_删除循环活动重新生成, conf.sn={}, serverId={}, time={}, openSnList={}", conf.sn, serverId, Port.getTime(), openSnList);
        } else if(type == 8){
            ConfActivityControl conf = ConfActivityControl.get(Utils.intValue(json[2]));
            if(conf == null){
                Log.temp.error("===ConfActivityControl，未找到数据。sn={}", json[2]);
                return;
            }
            ActivityData data = typeDataMap.get(conf.type);
            if(data != null){
                typeDataMap.remove(conf.type);
                data.remove();
            }
            checkActivityOpen();
            Log.temp.error("===gm_删除活动重新生成, conf.sn={}, serverId={}, time={}, openSnList={}", conf.sn, serverId, Port.getTime(), openSnList);
        } else if(type == 9){
            //-gm gmActivity 9 30024 60001
           onActivityClose(serverId, Utils.intValue(json[2]), Utils.intValue(json[2]));
        } else if (type == 10) {
            // 不在这里执行，但是声明一下，10已经用掉了
        } else if (type == 11) {
            // 不在这里执行，但是声明一下，11已经用掉了
        }
    }

    /**
     * gm检测过期的活动3类型是否存在，不存在自动创建
     */
    private void gmCheckCreateOpenActivity(){
        long timeNow = Port.getTime();
        for(ConfActivityControl conf : ConfActivityControl.findAll()) {
            if (openSnList.contains(conf.sn)) {
                continue;
            }
            if (conf.isClose == 1) {
                continue;
            }
            if(conf.timeType != 3){
                continue;
            }
            ActivityRoundData roundData = snRountDataMap.get(conf.sn);
            if(roundData != null){
                continue;
            }
            // gm类不安全，copy过来
            gm_timeType_3_init(conf, serverId, timeNow,  snRountDataMap);
//            GameDebugManager.inst().gm_timeType_3_init(conf, serverId, timeNow,  snRountDataMap);
//            gm_isOpenActivityTerm(conf);
        }

        for (ActivityRoundData roundData : snRountDataMap.values()) {
            int actType = roundData.getActivityType();
            if (roundData.getOpenTime() <= timeNow && timeNow < roundData.getCloseTime()) {
                if (!openSnList.contains(roundData.getActivitySn())) {
                    openSnList.add(roundData.getActivitySn());
                    snTypeMap.put(roundData.getActivitySn(), actType);
                }
            }
        }
    }

    /**
     * gm重新生成全服活动数据
     */
    private void gmRegenerateServerData(String[] param) {
        int sn = Utils.intValue(param[2]);
        ConfActivityControl conf = ConfActivityControl.get(sn);
        IActivityControl control = ActivityControlTypeFactory.getTypeData(conf.type);
        if (conf.is_round == 0) {
            ActivityData data = typeDataMap.get(conf.type);
            if (data == null) {
                Log.temp.error("===获取服务器活动数据失败，sn={}", sn);
                return;
            }
            IServerActData serverActData = control.initServerActData(data.getRound());
            if (serverActData == null) {
                Log.temp.error("===重新生成全服活动数据失败, sn={}, round={}", sn, data.getRound());
                return;
            }
            snServerDataMap.put(sn, serverActData);
            data.setActData(serverActData.toJSONString());
        } else {
            ActivityRoundData roundData = snRountDataMap.get(sn);
            if (roundData == null) {
                Log.temp.error("===获取服务器活动数据失败，sn={}", sn);
                return;
            }
            IServerActData serverActData = control.initServerActData(roundData.getRound());
            if (serverActData == null) {
                Log.temp.error("===重新生成全服活动数据失败, sn={}, round={}", sn, roundData.getRound());
                return;
            }
            snServerDataMap.put(sn, serverActData);
            roundData.setActData(serverActData.toJSONString());
        }
    }

    private boolean gm_isOpenActivityTerm(ConfActivityControl conf){
        if(conf.is_round != 1){
            Log.temp.error("===ConfActivityControl配表错误，is_round={}, timeType={}, sn={}", conf.is_round, conf.timeType, conf.sn);
            return false;
        }
        int[] timeArr = Utils.arrayStrToInt(conf.time);
        if(timeArr.length < 3){
            Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
            return false;
        }
        long timeNow = Port.getTime();
        long openServerTime = Util.getOpenServerTime(serverId);
        long restrictTime = Utils.getOffDayTime(openServerTime, conf.openTime, 0);
        if(restrictTime > timeNow){ // 限制时间未到，走开服活动期数
            int week = Utils.getDayOfWeek(openServerTime);
            ConfActivityAdjust confActivityAdjust = ConfActivityAdjust.get(GlobalConfVal.getConfActivityAdjustSn(conf.type, week));
            if(confActivityAdjust == null){
                Log.temp.error("===ConfActivityControl配表错误，未找到ConfActivityAdjust数据。type={}, week={},sn={}", conf.type, week, conf.sn);
                return false;
            }
            for(int i = 0; i < confActivityAdjust.day.length; i++){
                ActivityRoundData roundData = snRountDataMap.get(conf.sn);
                // 策划要求当天就要生效，所以要减一
                long openTimeTemp = Utils.getOffDayTime(openServerTime, confActivityAdjust.day[i][0]-1, 0);
                // 策划要求所有配置时间包含当天23：59.59，又因为取0点时间所以这里需要并减去1秒
                long closeTimeTemp = Utils.getOffDayTime(openServerTime, confActivityAdjust.day[i][1], 0) - Time.SEC;
                if(openTimeTemp <= timeNow && closeTimeTemp >= timeNow){
                    if(roundData == null){
                        Log.temp.error("===循环活动开启， activitySn={}, round={}, openTime={}, closeTime={}", conf.sn, i+1, openTimeTemp, closeTimeTemp);
                        createActivityRoundData(conf.sn,conf.type, i+1, openTimeTemp, closeTimeTemp);
                    } else {
                        Log.temp.error("===循环活动开启， activitySn={}, round={}, num={}, confRound={} openTime={}, closeTime={}", conf.sn,
                                roundData.getRound() + 1, confActivityAdjust.day.length, i+1, openTimeTemp, closeTimeTemp);
                        updateActivityRoundData(roundData , roundData.getRound() > confActivityAdjust.day.length ? roundData.getRound() + 1 : i+1, openTimeTemp, closeTimeTemp);
                    }
                    checkActivityData(conf, openTimeTemp, closeTimeTemp);
                    return true;
                }
                if(closeTimeTemp < timeNow && roundData == null){
                    createActivityRoundData(conf.sn,conf.type, i+1, openTimeTemp, closeTimeTemp);
                }else if(closeTimeTemp <= timeNow){
                    updateActivityRoundData(roundData, roundData.getRound() + 1, openTimeTemp, closeTimeTemp);
                }
            }
            return false;
        }
        int week = Utils.getDayOfWeek(openServerTime);
        ConfActivityAdjust confActivityAdjust = ConfActivityAdjust.get(GlobalConfVal.getConfActivityAdjustSn(conf.type, week));
        if(confActivityAdjust == null){
            Log.temp.error("===ConfActivityControl配表错误，未找到ConfActivityAdjust数据。type={}, week={},sn={}", conf.type, week, conf.sn);
            return false;
        }
        int round = confActivityAdjust.day.length;
        ActivityRoundData roundData = snRountDataMap.get(conf.sn);
        if(roundData == null || roundData.getRound() < round){
            for(int i = 0; i < confActivityAdjust.day.length; i++){
                roundData = snRountDataMap.get(conf.sn);
                // 策划要求当天就要生效，所以要减一
                long openTimeTemp = Utils.getOffDayTime(openServerTime, confActivityAdjust.day[i][0]-1, 0);
                // 策划要求所有配置时间包含当天23：59.59，又因为取0点时间所以这里需要并减去1秒
                long closeTimeTemp = Utils.getOffDayTime(openServerTime, confActivityAdjust.day[i][1], 0) - Time.SEC;
                if(openTimeTemp <= timeNow && closeTimeTemp >= timeNow){
                    if(roundData == null){
                        Log.temp.error("===循环活动开启， activitySn={}, round={}, openTime={}, closeTime={}", conf.sn, i+1, openTimeTemp, closeTimeTemp);
                        createActivityRoundData(conf.sn,conf.type, i+1, openTimeTemp, closeTimeTemp);
                    } else {
                        Log.temp.error("===循环活动开启， activitySn={}, round={}, num={}, confRound={} openTime={}, closeTime={}", conf.sn,
                                roundData.getRound() + 1, confActivityAdjust.day.length, i+1, openTimeTemp, closeTimeTemp);
                        updateActivityRoundData(roundData , roundData.getRound() > confActivityAdjust.day.length ? roundData.getRound() + 1 : i+1, openTimeTemp, closeTimeTemp);
                    }
                    checkActivityData(conf, openTimeTemp, closeTimeTemp);
                    return true;
                }
                if(closeTimeTemp < timeNow && roundData == null){
                    createActivityRoundData(conf.sn,conf.type, i+1, openTimeTemp, closeTimeTemp);
                }else if(closeTimeTemp <= timeNow){
                    updateActivityRoundData(roundData, roundData.getRound() + 1, openTimeTemp, closeTimeTemp);
                }
            }
            return false;
        }

        if(confActivityAdjust.day.length == 0){
            Log.temp.error("===ConfActivityControl配表错误，未找到ConfActivityAdjust数据。type={}, week={},sn={}", conf.type, week, conf.sn);
            return false;
        }
        int day = confActivityAdjust.day[confActivityAdjust.day.length-1][1] - confActivityAdjust.day[confActivityAdjust.day.length-1][0] + 1;
        if(day <= 0){
            return false;
        }
        int openWeek = Utils.intValue(timeArr[0]);
        int openHour = Utils.intValue(timeArr[1]);
        int openMin = Utils.intValue(timeArr[2]);
        long openTime = Utils.getTimeOfWeek(timeNow, openWeek == 1 ? 7 : openWeek - 1, openHour) + openMin * Time.MIN;
        if(openTime < restrictTime){
            return false;
        }
        if (roundData.getCloseTime() >= openTime) {
            return false;
        }
        long cdTime = roundData.getOpenTime() + conf.round_day * Time.DAY - 5 * Time.SEC;;
        if(cdTime > timeNow || cdTime > openTime){
            return false;
        }
        long closeTime = Utils.getOffDayTime(openTime, day, 0) - Time.SEC;
        if(openTime <= timeNow && timeNow < closeTime){
            updateActivityRoundData(roundData, roundData.getRound() + 1, openTime, closeTime);
            return true;
        }
        return false;
    }



    public void gm_timeType_3_init(ConfActivityControl conf, int serverId, long timeNow, Map<Integer, ActivityRoundData> snRountDataMap){
        if(conf.is_round != 1){
            Log.temp.error("===ConfActivityControl配表错误，is_round={}, timeType={}, sn={}", conf.is_round, conf.timeType, conf.sn);
            return;
        }
        int[] timeArr = Utils.arrayStrToInt(conf.time);
        if(timeArr.length < 3){
            Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
            return;
        }
        long openServerTime = Util.getOpenServerTime(serverId);
        Log.temp.info("===gm 初始化活动循环：serverId={} , sn={} , openServerTime={}", serverId, conf.sn, openServerTime);
        long restrictTime = Utils.getOffDayTime(openServerTime, conf.openTime, 0);
        int week = Utils.getDayOfWeek(openServerTime);
        ConfActivityAdjust confActivityAdjust = ConfActivityAdjust.get(GlobalConfVal.getConfActivityAdjustSn(conf.type, week));
        if(confActivityAdjust == null){
            Log.temp.error("===ConfActivityControl配表错误，未找到ConfActivityAdjust数据。type={}, week={},sn={}", conf.type, week, conf.sn);
            return;
        }

        int daySum = Utils.getDaysBetween(openServerTime, restrictTime) + 1;
        if(daySum <= 0){
            return;
        }

        int roundNum = 0;
        long openTime = 0;
        long closeTime = 0;
        for(int i = 0; i < confActivityAdjust.day.length; i++){
            // 策划要求当天就要生效，所以要减一
            long openTimeTemp = Utils.getOffDayTime(openServerTime, confActivityAdjust.day[i][0]-1, 0);
            // 策划要求所有配置时间包含当天23：59.59，又因为取0点时间所以这里需要并减去1秒
            long closeTimeTemp = Utils.getOffDayTime(openServerTime, confActivityAdjust.day[i][1], 0) - Time.SEC;
            if(timeNow >= closeTimeTemp){
                openTime = openTimeTemp;
                closeTime = closeTimeTemp;
                roundNum++;
                Log.temp.info("===gm 活动循环：serverId={} , sn={} , round={}， openTime={}， closeTime={}", serverId, conf.sn, roundNum, openTime, closeTime);
            }
        }
        // 限制时间未到，走开服活动期数
        if(restrictTime > timeNow){
//            return isOpenActivityTerm(conf);
            if(roundNum > 0 && openTime > 0 && closeTime > 0){
                Log.temp.info("===gm 初始化活动循环：serverId={} , sn={} , round={}， openTime={}， closeTime={}", serverId, conf.sn, roundNum, openTime, closeTime);
                ActivityRoundData roundData = snRountDataMap.get(conf.sn);
                if(roundData == null){
                    gm_createActivityRoundData(conf.sn, conf.type, roundNum, openTime, closeTime, snRountDataMap, serverId);
                } else {
                    if(roundData.getRound() != roundNum){
                        Log.temp.info("===gm 活动循环{}，活动配置发生变化，更新数据。serverId={} , sn={} , round={}， openTime={}， closeTime={}",
                                roundData.getId(), serverId, conf.sn, roundNum, openTime, closeTime);
                    }
                    roundData.setRound(roundNum);
                    roundData.setOpenTime(openTime);
                    roundData.setCloseTime(closeTime);
                    roundData.setUpdateTime(timeNow);
                    roundData.update();
                }
            }
            return;
        }

        if(confActivityAdjust.day.length == 0){
            Log.temp.error("===ConfActivityControl配表错误，未找到ConfActivityAdjust数据。type={}, week={},sn={}", conf.type, week, conf.sn);
            return;
        }
        int day = confActivityAdjust.day[confActivityAdjust.day.length-1][1] - confActivityAdjust.day[confActivityAdjust.day.length-1][0] + 1;
        if(day <= 0){
            return;
        }

        long timeTemp = closeTime;

        int openWeek = Utils.intValue(timeArr[0]);
        int openHour = Utils.intValue(timeArr[1]);
        int openMin = Utils.intValue(timeArr[2]);


        long timeOldOpen = openTime;
        long timeOldEnd = closeTime;

        Log.temp.info("===gm 打印日志：serverId={} , sn={} , round={}， openTime={}， closeTime={}",
                serverId, conf.sn, roundNum, timeOldOpen, timeOldEnd);

        int loopNum = 5000; // 限制循环次数
        while(timeTemp <= timeNow){
            loopNum--;
            if(loopNum <= 0){
                Log.temp.info("======查bug,活动sn={} , loopNum={}, round={}， openTime={}， closeTime={}, timeTemp={}, timeNow={}",
                        conf.sn, loopNum, roundNum, openTime, closeTime, timeTemp, timeNow);
                break;
            }
            timeTemp += Time.DAY;
            long openTimeTemp = Utils.getTimeOfWeek(timeTemp, openWeek == 1 ? 7 : openWeek - 1, openHour) + openMin * Time.MIN;
            if(openTimeTemp < restrictTime){
                Log.temp.info("======查bug,活动sn={} , loopNum={}, round={}， openTime={}， closeTime={}, timeTemp={}, timeNow={}， openTimeTemp={}",
                        conf.sn, loopNum, roundNum, openTime, closeTime, timeTemp, timeNow, openTimeTemp );
                continue;
            }
            long cdTime = timeOldOpen + conf.round_day * Time.DAY - 5 * Time.SEC;
            if(cdTime > timeTemp || cdTime > openTimeTemp){
                Log.temp.info("===gm 打印日志：serverId={} , sn={} , round={}， openTime={}， openTimeTemp={}, cdTime={}",
                        serverId, conf.sn, roundNum, timeOldOpen, openTimeTemp, cdTime);
                continue;
            }
            long closeTimeTemp = Utils.getOffDayTime(openTimeTemp, day, 0) - Time.SEC;
            Log.temp.info("===gm 打印日志：serverId={} , sn={} , round={}， openTime={}， closeTime={}",
                    serverId, conf.sn, roundNum, openTimeTemp, closeTimeTemp);
            if(openTimeTemp <= timeTemp && timeTemp <= closeTimeTemp){
                // 避免超出时间
                if(timeTemp > timeNow){
                    Log.temp.info("===gm 打印日志：serverId={} , sn={} , round={}， openTimeTemp={}， closeTimeTemp={}, cdTime={}, timeNow={}",
                            serverId, conf.sn, roundNum, openTimeTemp, closeTimeTemp, cdTime, timeNow);
                    break;
                }
                roundNum++;
                timeOldOpen = openTimeTemp;
                timeOldEnd = closeTimeTemp;

                Log.temp.info("===gm 打印日志活动循环：serverId={} , sn={} , round={}， openTime={}， closeTime={}", serverId, conf.sn, roundNum, timeOldOpen, timeOldEnd);
                if(closeTimeTemp > timeNow){
                    Log.temp.info("===gm 跳出循环：serverId={} , sn={} , round={}， openTime={}， closeTime={}, closeTimeTemp={}, timeNow={}",
                            serverId, conf.sn, roundNum, timeOldOpen, timeOldEnd, closeTimeTemp, timeNow);
                    break;
                }
            }
        }
        if(roundNum > 0 && timeOldOpen > 0 && timeOldEnd > 0){
            Log.temp.info("===gm 初始化活动循环：serverId={} , sn={} , round={}， openTime={}， closeTime={}, {}", serverId, conf.sn, roundNum, timeOldOpen, timeOldEnd, timeTemp);
            ActivityRoundData roundData = snRountDataMap.get(conf.sn);
            if(roundData == null){
                gm_createActivityRoundData(conf.sn, conf.type, roundNum, timeOldOpen, timeOldEnd, snRountDataMap, serverId);
            } else {
                if(roundData.getRound() != roundNum){
                    Log.temp.info("===活动循环{}，活动配置发生变化，更新数据。serverId={} , sn={} , round={}， openTime={}， closeTime={}",
                            roundData.getId(), serverId, conf.sn, roundNum, timeOldOpen, timeOldEnd);
                }
                roundData.setRound(roundNum);
                roundData.setOpenTime(timeOldOpen);
                roundData.setCloseTime(timeOldEnd);
                roundData.setUpdateTime(timeNow);
                roundData.update();
            }
        }
    }



    private void gm_createActivityRoundData(int activitySn, int type, int round, long openTime, long closeTime, Map<Integer, ActivityRoundData> snRountDataMap, int serverId){
        if (snRountDataMap.containsKey(activitySn)) {
            ActivityRoundData roundData = snRountDataMap.get(activitySn);
            if(roundData.getRound() != round){
                Log.temp.info("===活动循环{}，活动配置发生变化，更新数据。serverId={} , sn={} , round={}， openTime={}， closeTime={}, oldOpenTime={}， oldCloseTime={}",
                        roundData.getId(), serverId, activitySn, round, openTime, closeTime, roundData.getOpenTime(), roundData.getCloseTime());
                roundData.setRound(round);
                roundData.setOpenTime(openTime);
                roundData.setCloseTime(closeTime);
                roundData.setUpdateTime(Port.getTime());
                roundData.update();
            }
            return;
        }
        ActivityRoundData roundData = new ActivityRoundData();
        roundData.setId(Port.applyId());
        roundData.setActivitySn(activitySn);
        roundData.setActivityType(type);
        roundData.setRound(round);
        roundData.setServerId(serverId);
        roundData.setOpenTime(openTime);
        roundData.setCloseTime(closeTime);
        roundData.setUpdateTime(Port.getTime());
        IActivityControl control = ActivityControlTypeFactory.getTypeData(type);
        if (control != null) {
            IServerActData serverActData = control.initServerActData(round);
            if (serverActData != null) {
                roundData.setActData(serverActData.toJSONString());
            }
        }
        roundData.persist();
        snRountDataMap.put(activitySn, roundData);
    }

}
