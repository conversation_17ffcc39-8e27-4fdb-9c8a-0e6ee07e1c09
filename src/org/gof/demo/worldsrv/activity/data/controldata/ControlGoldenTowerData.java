package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GoldenTower活动数据
 */
public class ControlGoldenTowerData implements IControlData {

    public int total = 0;                                  // 总消耗次数
    public int layer = 1;                                  // 当前层级 (1-6)
    public int layerDrawNum = 0;                           // 当前层抽取次数
    public int round = 1;                                  // 当前轮数
    public List<Integer> sumRewards = new ArrayList<>();   // 已领取的累计奖励ID
    public Map<Integer, Integer> chooseReward = new HashMap<>(); // 自选奖励信息Map<layer, index>
    public Map<Integer, Integer> posRewardMap = new HashMap<>(); // 已抽取位置对应的奖励配置SN

    public ControlGoldenTowerData(String json) {
        JSONObject jo = Utils.toJSONObject(json);
        total = jo.getIntValue("t");
        layer = jo.getIntValue("l");
        if (layer <= 0) {
            layer = 1;
        }
        round = jo.getIntValue("r");
        if (round <= 0) {
            round = 1;
        }
        layerDrawNum = jo.getIntValue("lDNum");
        sumRewards = Utils.strToIntList(jo.getString("sumR"));
        chooseReward = Utils.jsonToMapIntInt(jo.getString("chR"));
        posRewardMap = Utils.jsonToMapIntInt(jo.getString("posRMap"));
    }

    public void addLayer(){
        if(layer < 6){
            layer++;
        }else{
            round++;
            layer = 1;
        }
        layerDrawNum = 0;
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
        // 初始化数据
        layer = 1;
        total = 0;
        round = 1;
        layerDrawNum = 0;
        sumRewards.clear();
        chooseReward.clear();
        posRewardMap.clear();
    }

    @Override
    public String toJSON() {
        JSONObject jo = new JSONObject();
        jo.put("t", total);
        jo.put("l", layer);
        jo.put("r", round);
        jo.put("lDNum", layerDrawNum);
        jo.put("sumR", Utils.listToString(sumRewards));
        jo.put("chR", Utils.mapIntIntToJSON(chooseReward));
        jo.put("posRMap", Utils.mapIntIntToJSON(posRewardMap));
        return jo.toJSONString();
    }
}
