// 合成大西瓜请求活动信息
message act_fruit_merge_info_c2s {
	 option (msgid) = 6571;
	 uint32 act_type = 1;
}

message act_fruit_merge_info_s2c {
	option (msgid) = 6572;
	uint32 act_type = 1;
	uint32 curr_score = 2;		//当前分数
	uint32 max_score = 3;		//最大局内分数
	uint32 rank = 4;			//当前排名
	uint64 total_score = 5;		//累计分数
	uint32 total_cost_stamina = 6;		//总消耗体力
	p_fruit_merge_state state = 7;		//当前状态
	repeated uint32 score_list = 8;			//排行前100分数并且大于自己的分数
}

//结束游戏
message act_fruit_merge_end_c2s {
	 option (msgid) = 6573;
	 uint32 act_type = 1;
	 uint32 curr_score = 2;		//当前分数
}

message act_fruit_merge_end_s2c {
	option (msgid) = 6574;
	uint32 act_type = 1;
	uint32 curr_score = 2;
	uint64 total_score = 3;
}

//消耗体力
message act_fruit_merge_stamina_cost_c2s {
	 option (msgid) = 6575;
	 uint32 act_type = 1;
	 uint32 total_cost = 2;		//当局总共使用的体力
}

//使用道具
message act_fruit_merge_use_item_c2s {
	option (msgid) = 6576;
	uint32 act_type = 1;
	uint32 item_sn = 2;			//使用道具
}

//上报状态
message act_fruit_merge_state_c2s {
	 option (msgid) = 6577;
	 uint32 act_type = 1;
	 repeated uint32 merge_fruit = 2;
	 p_fruit_merge_state state = 3;
}

// 单个水果的数据
message p_fruit {
    int32 sn = 1; // 水果sn
    float pos_x = 2;   // x轴坐标
    float pos_y = 3;   // y轴坐标
}

// 合成大西瓜数据
message p_fruit_merge_state {
	uint32 current_score = 1;               // 当前对局的实时分数
    repeated p_fruit fruits_on_board = 2;   // 场上所有水果的集合
    int32 next_fruit_sn = 3;           		// 准备区下一个将要下落的水果类型
    repeated p_key_value item_use = 4;      // 道具使用次数
}

帮我实现合成大西瓜活动,活动类型5015,这个活动是两个一样的碰在一起可以合成另一个,逻辑在客户端,服务器处理资源的扣除,上榜,和任务
都是客户端计算,分数也是客户端告知,客户端发来有用的数据有要消耗的道具,服务器要扣除道具
和消耗的体力,服务器要对比当前发来消耗的体力和客户端发来的总消耗体力进行对比,扣除差值的体力,体力用ControlStaminaManager这个类,体力相关的配置新加了ActivityStamina_0_活动体力表
客户端告知的合成了哪些水果要用来更新任务进度  内存数据的参考ControlCastleData, p_fruit_merge_state可以整个是一个成员变量,排行榜可以参考RankManager类,
一些调用可以参考ActivityControlCastle类
