package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityControlCrossService;
import org.gof.demo.worldsrv.activity.ActivityControlServiceProxy;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.serverdata.IServerActData;
import org.gof.demo.worldsrv.activity.data.serverdata.ServerActDataLuckyLottery;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.confirm.IConfirm;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.entity.ActivityData;
import org.gof.demo.worldsrv.human.HumanSettingConstants;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

import java.util.Calendar;
import java.util.List;
import java.util.Map;

import static org.gof.demo.worldsrv.config.ConfFlyEvolutionPro.K.times;

public class ActivityControlLuckyLottery extends AbstractActivityControl {
    public ActivityControlLuckyLottery(int type) {
        super(type);
    }

    @Override
    public IServerActData initServerActData(int round, ActivityData activityData) {
        Log.activity.info("[福签抽奖]活动开启");
        ActivityControlServiceProxy.newInstance().openLuckyLottery(activityData.getServerId(), type, round);
        return new ServerActDataLuckyLottery();
    }

    @Override
    public IServerActData parseServerActData(String json, int round, int serverId) {
        Log.activity.info("[福签抽奖]活动起服加载数据");
        ActivityControlServiceProxy.newInstance().openLuckyLottery(serverId, type, round);
        ServerActDataLuckyLottery serverActData = new ServerActDataLuckyLottery();
        serverActData.fillFromJSONString(json);
        return serverActData;
    }

    @Override
    public void endShowServerData(int serverId, int actSn) {
        ActivityControlServiceProxy.newInstance().closeLuckyLottery(serverId);
    }

    @Override
    public void on_act_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            Log.activity.error("[福签抽奖]活动数据不存在");
            return;
        }
        ActControlData actData = controlData.getActControlData();
        long time1 = Math.min(Port.getTime(), actData.getCloseTime() * Time.SEC);
        long time2 = actData.getOpenTime() * Time.SEC;
        int turn = Utils.getDaysBetween(time2, time1) + 1;
        on_act_lucky_lottery_info_c2s(humanObj, turn);
    }

    public void on_act_lucky_lottery_info_c2s(HumanObject humanObj, int turn) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            Log.activity.error("[福签抽奖]活动数据不存在");
            return;
        }
        ActControlData actControlData = controlData.getActControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, actControlData.getRound());
        if (confTerm == null) {
            Log.activity.error("[福签抽奖]活动时间配置不存在, type={}, round={}", type, actControlData.getRound());
            return;
        }
        ActivityControlServiceProxy prx = ActivityControlServiceProxy.newInstance();
        prx.getLuckyLottery(humanObj.getHumanId(), Utils.getServerIdByHumanId(humanObj.getHumanId()), type, turn);
        prx.listenResult((result, context) -> {
            ReasonResult rr = result.get("result");
            if (!rr.success) {
                Inform.user(humanObj.getHumanId(), Inform.提示操作, rr);
                return;
            }
            int[] times = Utils.arrayStrToInt(confTerm.parameter2);
            long openTime = actControlData.getOpenTime() * Time.SEC + (turn - 1) * Time.DAY + times[0] * Time.HOUR + times[1] * Time.MIN;

            List<Define.p_lucky_lottery> codeList = result.get("codeList");
            String code = result.get("code");
            MsgAct.act_lucky_lottery_info_s2c.Builder msg = MsgAct.act_lucky_lottery_info_s2c.newBuilder();
            msg.setActType(type);
            msg.setTurn(turn);
            msg.addAllLotteryList(codeList);
            msg.setCode(code);
            msg.setOpenTime(openTime);
            humanObj.sendMsg(msg.build());
        });
    }

    public void on_act_lucky_lottery_draw_c2s(HumanObject humanObj, int drawType) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            Log.activity.error("[福签抽奖]活动数据不存在");
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, controlData.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("[福签抽奖]活动时间配置不存在, type={}, round={}", type, controlData.getActControlData().getRound());
            return;
        }
        int[] times = Utils.strToIntArray(confTerm.parameter2);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, times[0]);
        calendar.set(Calendar.MINUTE, times[1]);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long openTime = calendar.getTimeInMillis();
        if (Port.getTime() >= openTime) {
            Log.activity.error("[福签抽奖]今日已开奖，不能在抽福签");
            return;
        }

        int num = drawType == 1 ? 10 : 1;
        ReasonResult rr = ProduceManager.inst().canCostIntArr(humanObj, new int[] { confTerm.cost[0], confTerm.cost[1] * num});
        if (!rr.success) {
            Inform.sendMsg_error(humanObj, ErrorTip.ItemNotEnough);
            return;
        }

        int turn = Utils.getDaysBetween(Port.getTime(), controlData.getActControlData().getOpenTime() * Time.SEC) + 1;
        int serverId = Utils.getServerIdByHumanId(humanObj.getHumanId());
        String name = humanObj.name;
        Map<Integer, Long> settingMap = Utils.jsonToMapIntLong(humanObj.getHumanExtInfo().getSettingMap());
        boolean isHide = settingMap.getOrDefault(HumanSettingConstants.BroadCastHideName, 0L) == 1L;
        ActivityControlServiceProxy prx = ActivityControlServiceProxy.newInstance();
        prx.drawLuckyLottery(humanObj.getHumanId(), name, isHide, serverId, type, controlData.getActControlData().getRound(), turn, num);
        prx.listenResult((result, context) -> {
            ReasonResult rr1 = result.get("result");
            if (!rr1.success) {
                Inform.user(humanObj.getHumanId(), Inform.提示操作, rr1);
                return;
            }
            ReasonResult rr2 = ProduceManager.inst().checkAndCostItem(humanObj, confTerm.cost[0], confTerm.cost[1] * num, MoneyItemLogKey.福签);
            if (!rr2.success) {
                Log.activity.error("[福签抽奖]消耗道具失败, humanId={}, sn={}, num={}", humanObj.id, confTerm.cost[0], confTerm.cost[1] * num);
            }
            List<Define.p_lucky_lottery> codeList = result.get("codeList");
            String serverCode = result.get("code");
            MsgAct.act_lucky_lottery_draw_s2c.Builder msg = MsgAct.act_lucky_lottery_draw_s2c.newBuilder();
            msg.setActType(type);
            msg.setTurn(turn);
            msg.setCode(serverCode);
            msg.setOpenTime(openTime);
            msg.addAllLotteryList(codeList);
            humanObj.sendMsg(msg);
        });
    }

    public void on_act_lucky_lottery_report_c2s(HumanObject humanObj, int turn, int sn, int page) {
        ActivityControlServiceProxy prx = ActivityControlServiceProxy.newInstance();
        prx.getLuckyLotteryReport(Utils.getServerIdByHumanId(humanObj.getHumanId()), turn, sn, page);
        prx.listenResult((result, context) -> {
            ReasonResult rr = result.get("result");
            if (!rr.success) {
                Inform.user(humanObj.getHumanId(), Inform.提示操作, rr);
                return;
            }
            int pageNum = result.get("pageNum");
            int allNum = result.get("allNum");
            List<Define.p_key_value_string> reportList = result.get("reportList");
            MsgAct.act_lucky_lottery_report_s2c.Builder msg = MsgAct.act_lucky_lottery_report_s2c.newBuilder();
            msg.setActType(type);
            msg.setTurn(turn);
            msg.setPage(page);
            msg.setSn(sn);
            msg.setPageNum(pageNum);
            msg.setAllNum(allNum);
            msg.addAllReportList(reportList);
            humanObj.sendMsg(msg);
        });
    }

}



