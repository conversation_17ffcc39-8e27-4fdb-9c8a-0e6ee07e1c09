package org.gof.demo.worldsrv.activity.calculator;

import java.util.*;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.EActivityType;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlCardEliminateData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.mall.PayMallTypeKey;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.TokenItemType;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;


/**
 * 翻牌活动控制类
 */
public class ActivityControlCardEliminate extends AbstractActivityControl {
    private static final int CARD_STATUS_UNFLIPPED = 0;  // 未翻开
    private static final int CARD_STATUS_FLIPPED = 1;    // 已翻开
    private static final int CARD_STATUS_ELIMINATED = 2; // 已消除
    private static final int CARD_STATUS_XRAY = 3;       // 透视

    // Buff效果ID
    private static final int BUFF_FLIP_ADJACENT = 1;     // 翻牌时，有概率将相连的牌翻开
    private static final int BUFF_ELIMINATE_FLIP = 2;    // 卡牌消除时，有概率将任意牌翻开
    private static final int BUFF_NPC_PATIENCE = 3;      // NPC初始耐心值上限+1
    private static final int BUFF_FLIP_PATIENCE = 4;     // 翻牌时，有概率使当前NPC耐心+1
    private static final int BUFF_MAX_FLIP_CARDS = 5;    // 场上最多可同时存在3张翻开的牌
    private static final int BUFF_REWARD_XRAY = 6;       // 获得NPC奖励时，会随机透视牌
    private static final int BUFF_FLIP_XRAY = 7;         // 翻牌时，有概率随机透视牌
    private static final int BUFF_RESET_STAMINA = 8;     // 盖牌时，有概率触发NPC额外赠送体力
    private static final int BUFF_SCORE_BONUS = 9;       // 消除积分+5

    //特权效果
    private static final int BUY_MIDDLE_VALUE = 10;
    private static final int BUY_HIGH_VALUE = 50;


    private static ActivityControlCardEliminate instance = new ActivityControlCardEliminate(0);

    public static ActivityControlCardEliminate getInstance(int type) {
        instance.type = type;
        return instance;
    }

    public ActivityControlCardEliminate(int type) {
        super(type);
    }

    /**
     * 处理活动信息请求
     */
    public void on_act_card_eliminate_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}信息时，活动数据不存在", humanObj.id, type);
            return;
        }
        boolean isEndShow = data.getActControlData().getState() == EActivityType.STATE_ENDSHOW;
        ControlCardEliminateData controlData = (ControlCardEliminateData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id, type);
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if(controlData.cards.isEmpty() && controlData.round > 0) {
            Log.activity.error("玩家{}的活动{}数据为空，重新生成游戏信息", humanObj.id, type);
            generateCards(controlData, confTerm);
            generateTasks(controlData, confTerm);
        }

        // 构建响应消息
        controlData.ticketMgr.tryRecoverStamina();
        MsgAct2.act_card_eliminate_info_s2c.Builder builder = MsgAct2.act_card_eliminate_info_s2c.newBuilder();
        builder.setActType(type);
        builder.setMaxScore(controlData.maxScore);
        builder.setSumScore(controlData.sumScore);
        builder.setTicket(controlData.getTicket());
        builder.setNextRecoverTime(controlData.getNextRecoverTime());
        //添加可以到达的最大轮次
        int maxRound = getMaxRound(controlData, confTerm);
        builder.setTotalRound(maxRound);
        // 仅在非新手引导且非游戏进行中时查询排名
        if (controlData.maxScore > 0) {
            if (confTerm != null) {
                RankManager.inst().getMyRankAndScore(confTerm.rank_id,
                    humanObj.getHuman().getServerId(),
                    humanObj.id,
                    ret -> {
                        if (ret.failed()) {
                            Log.activity.error("排行榜查询失败, rankId={}, serverId={}, humanId={}, e={}",
                                confTerm.rank_id,
                                humanObj.getHuman().getServerId(),
                                humanObj.id,
                                ret.cause().getMessage(),
                                ret.cause());
                            sendInfoResponse(humanObj, builder, controlData, isEndShow);
                            return;
                        }

                        builder.setRank(Utils.intValue(ret.result().get(0)));
                        sendInfoResponse(humanObj, builder, controlData, isEndShow);
                    });
            } else {
                sendInfoResponse(humanObj, builder, controlData, isEndShow);
            }
        } else {
            sendInfoResponse(humanObj, builder, controlData, isEndShow);
        }
    }

    private int getMaxRound(ControlCardEliminateData controlData, ConfActivityTerm confTerm) {
        int maxRound = 0;
        int roundSize = ConfCardEliminateDungeon_0.findAll().size();
        for(int i = 1; i <= roundSize; i++) {
            ConfCardEliminateDungeon_0 confDungeon = ConfCardEliminateDungeon_0.get(type, confTerm.group_id, i);
            if (confDungeon == null) {
                break;
            }
            if(controlData.openCount >= confDungeon.unlock){
                maxRound = i;
            }else {
                break;
            }
        }
        return maxRound;
    }

    private void sendInfoResponse(HumanObject humanObj, MsgAct2.act_card_eliminate_info_s2c.Builder builder,
            ControlCardEliminateData controlData, boolean isEndShow) {
        // 添加游戏进行中的数据
        if (controlData.stamina > 0 && !isEndShow) {
            builder.setStamina(controlData.stamina);
            builder.setRound(controlData.round);
            builder.setScore(controlData.score);
            builder.setTotalStep(controlData.totalStep);
            builder.setCombo(controlData.combo);
            builder.setRefreshCount(controlData.refreshCount);

            // 添加任务信息
            Define.p_card_eliminate_task.Builder taskBuilder = Define.p_card_eliminate_task.newBuilder();
            taskBuilder.setElementId(controlData.taskElementId);
            taskBuilder.setPatience(controlData.taskPatience);
            taskBuilder.setMaxPatience(controlData.taskMaxPatience);
            taskBuilder.setNextElementId(controlData.nextTaskElementId);
            taskBuilder.setRewardStamina(controlData.taskRewardStamina);
            builder.setTask(taskBuilder);

            // 添加卡牌信息
            for (int i = 0; i < controlData.cards.size(); i++) {
                if(controlData.cardStatus.get(i) == 0){
                    continue;
                }
                Define.p_card_eliminate_card.Builder cardBuilder = Define.p_card_eliminate_card.newBuilder();
                cardBuilder.setPos(i + 1);
                cardBuilder.setElementId(controlData.cards.get(i));
                cardBuilder.setStatus(controlData.cardStatus.get(i));
                builder.addCards(cardBuilder);
            }

            // 添加buff信息
            for (Map.Entry<Integer, Integer> entry : controlData.buffs.entrySet()) {
                builder.addBuffs(HumanManager.inst().to_p_key_value(entry.getKey(), entry.getValue()));
            }
        }
        // buff选择阶段
        if (!controlData.buffChoices.isEmpty() && !isEndShow) {
            for (int buffId : controlData.buffChoices) {
                builder.addRandomBuffs(buffId);
            }
        }

        humanObj.sendMsg(builder.build());
    }

    @Override
    public void itemUse(HumanObject humanObj, int goodsId, int num) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ActControlData actData = controlData.getActControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, actData.getRound());
        if (confTerm == null) {
            Log.activity.error("活动找不到活动期数表ConfActivityTerm, type={}, round={}", type, actData.getRound());
            return;
        }

        ControlCardEliminateData data = (ControlCardEliminateData) controlData.getControlData();
        if (data == null) {
            Log.activity.error("活动数据为空, type={}, round={}", type, actData.getRound());
            return;
        }

        data.ticketMgr.addStamina(num);
        controlData.updateControlData();

        MsgAct2.act_stamina_refresh_s2c.Builder builder = MsgAct2.act_stamina_refresh_s2c.newBuilder();
        builder.setActType(type);
        builder.setStamina(data.ticketMgr.getStamina());
        builder.setNextRecoverTime(data.ticketMgr.nextRecoverTime);
        humanObj.sendMsg(builder);
    }

    public void payWarToken(HumanObject humanObj, int payType) {
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            Log.activity.error("玩家{}请求活动{}支付时，活动数据不存在", humanObj.id, type);
            return;
        }

        ControlCardEliminateData data = (ControlCardEliminateData) controlData.getControlData();
        if (data == null) {
            Log.activity.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id, type);
            return;
        }

        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, controlData.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动{}找不到活动期数表ConfActivityTerm, round={}", type, controlData.getActControlData().getRound());
            return;
        }
        if(payType == PayMallTypeKey.wartoken_type_1){
            // 中级特权购买
            if (data.isBuyMiddle) {
                Log.activity.error("玩家{}已经购买了中级特权，无法重复购买", humanObj.id);
                return;
            }
            data.isBuyMiddle = true;
            Log.activity.error("玩家{}购买了中级特权，增加初始体力", humanObj.id);
        } else if (payType == PayMallTypeKey.wartoken_type_2) {
            // 高级特权购买
            if (data.isBuyHigh) {
                Log.activity.error("玩家{}已经购买了高级特权，无法重复购买", humanObj.id);
                return;
            }
            data.isBuyHigh = true;
            Log.activity.error("玩家{}购买了高级特权，增加消除基础分数", humanObj.id);
        }
        controlData.updateControlData();
    }

    /**
     * 处理开始游戏请求
     */
    public void on_act_card_eliminate_start_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}开始时，活动数据不存在", humanObj.id, type);
            return;
        }

        ControlCardEliminateData controlData = (ControlCardEliminateData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id, type);
            return;
        }

        // 消耗门票
        if (!controlData.costTicket()) {
            return;
        }

        MsgAct2.act_stamina_refresh_s2c.Builder msg = MsgAct2.act_stamina_refresh_s2c.newBuilder();
        msg.setActType(type);
        msg.setStamina(controlData.ticketMgr.stamina);
        msg.setNextRecoverTime(controlData.getNextRecoverTime());
        humanObj.sendMsg(msg);

        // 重置游戏数据
        controlData.resetGameData();

        // 初始化游戏
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            return;
        }

        controlData.round = 1;


        // 设置初始体力
        ConfGlobal staminaConf = ConfGlobal.get("Card_stamina_initial");
        if (staminaConf != null && staminaConf.intArray != null && staminaConf.intArray.length > 0) {
            controlData.stamina = staminaConf.intArray[0];
        } else {
            controlData.stamina = 10; // 默认值
            Log.activity.error("活动{}找不到初始体力配置，使用默认值10", type);
        }
        if(controlData.isBuyMiddle){
            ConfGlobal confGlobal = ConfGlobal.get("Card_privilege");
            if( confGlobal == null || confGlobal.strValue == null || confGlobal.strValue.isEmpty()) {
                Log.activity.error("活动{}找不到特权配置", type);
                return;
            }else {
                Map<Integer, Integer> privilegeMap = Utils.strToMapIntInt(confGlobal.strValue);
                controlData.stamina += privilegeMap.get(PayMallTypeKey.wartoken_type_1);
            }
        }

        // 生成卡牌
        generateCards(controlData, confTerm);

        // 生成任务
        generateTasks(controlData, confTerm);

        // 更新数据
        data.updateControlData();

        int maxRound = getMaxRound(controlData, confTerm);

        // 构建响应消息
        MsgAct2.act_card_eliminate_start_s2c.Builder builder = MsgAct2.act_card_eliminate_start_s2c.newBuilder();
        builder.setActType(type);
        builder.setRound(controlData.round);
        builder.setStamina(controlData.stamina);
        builder.setTotalRound(maxRound);

        // 添加任务信息
        Define.p_card_eliminate_task.Builder taskBuilder = Define.p_card_eliminate_task.newBuilder();
        taskBuilder.setElementId(controlData.taskElementId);
        taskBuilder.setPatience(controlData.taskPatience);
        taskBuilder.setMaxPatience(controlData.taskMaxPatience);
        taskBuilder.setNextElementId(controlData.nextTaskElementId);
        taskBuilder.setRewardStamina(controlData.taskRewardStamina);
        builder.setTask(taskBuilder);

        humanObj.sendMsg(builder.build());
    }

    /**
      * 生成卡牌
      */
     private void generateCards(ControlCardEliminateData controlData, ConfActivityTerm confTerm) {
         ConfCardEliminateDungeon_0 confDungeon = ConfCardEliminateDungeon_0.get(type, confTerm.group_id, controlData.round);
         if (confDungeon == null) {
             Log.activity.error("找不到翻牌关卡配置, actType={}, groupId={}, round={}", type, confTerm.group_id, controlData.round);
             return;
         }
         controlData.cards.clear();
         controlData.cardStatus.clear();
         controlData.xRayCardMap.clear();

         // 获取本轮卡牌总数和元素种类数
         int totalCards = confDungeon.card_num;
         int typeNum = confDungeon.type_num;

         // 生成可用的元素ID列表
         List<Integer> availableElements = new ArrayList<>();
         for (int i = 1; i <= 10; i++) { // 最多10种元素
             availableElements.add(i);
         }

         // 随机选择指定数量的元素类型
//         Collections.shuffle(availableElements);
         List<Integer> selectedElements = availableElements.subList(0, Math.min(typeNum, availableElements.size()));

         // 生成卡牌
         List<Integer> cardElements = new ArrayList<>();

         // 确保每种元素至少有一组（2张）
         for (int elementId : selectedElements) {
             cardElements.add(elementId);
             cardElements.add(elementId);
         }

         // 填充剩余卡牌
         int remainingCards = totalCards - cardElements.size();
         for (int i = 0; i < remainingCards; i += 2) {
             int randomElement = selectedElements.get(Utils.random(0, selectedElements.size()));
             cardElements.add(randomElement);
             cardElements.add(randomElement);
         }

         // 随机打乱卡牌顺序
//         Collections.shuffle(cardElements);

         // 如果是新手引导，确保前两张卡牌可以配对
         if (controlData.maxScore == 0) {
             int firstElement = cardElements.get(0);
             // 找到相同元素的位置
             for (int i = 1; i < cardElements.size(); i++) {
                 if (cardElements.get(i) == firstElement) {
                     // 将第二张相同的卡牌交换到第二个位置
                     if (i != 1) {
                         int temp = cardElements.get(1);
                         cardElements.set(1, cardElements.get(i));
                         cardElements.set(i, temp);
                     }
                     break;
                 }
             }
         }
         Log.activity.debug("生成卡牌元素列表: {}", cardElements);
         // 设置卡牌和状态
         for (int element : cardElements) {
             controlData.cards.add(element);
             controlData.cardStatus.add(CARD_STATUS_UNFLIPPED);
         }
     }

     /**
      * 生成任务
      */
     private void generateTasks(ControlCardEliminateData controlData, ConfActivityTerm confTerm) {
         ConfCardEliminateDungeon_0 confDungeon = ConfCardEliminateDungeon_0.get(type, confTerm.group_id, controlData.round);
         if (confDungeon == null) {
             return;
         }

         // 获取本轮任务数量和耐心值
         int taskNum = confDungeon.task_num;
         int taskPatience = confDungeon.task_patience;

         // 获取场上的所有元素类型
         Set<Integer> elementTypes = new HashSet<>(controlData.cards);
         List<Integer> availableElements = new ArrayList<>(elementTypes);
//         for (int i = 0; i < availableElements.size(); i++) { // 最多10种元素
//             availableElements.set(i, i%2 + 1);
//         }
//         Collections.shuffle(availableElements);

         // 计算实际任务数量（不能超过可用元素数量）
         int actualTaskNum = Math.min(taskNum, availableElements.size());

         // 生成任务队列
         controlData.taskElementIds = new ArrayList<>();
         for (int i = 0; i < actualTaskNum; i++) {
             controlData.taskElementIds.add(availableElements.get(i));
         }

         // 设置第一个任务
         if (!controlData.taskElementIds.isEmpty()) {
             controlData.taskElementId = controlData.taskElementIds.get(0);
             controlData.taskPatience = taskPatience;
             controlData.taskMaxPatience = controlData.taskPatience;
             controlData.taskRewardStamina = confDungeon.reward;
             ConfCardEliminateBuff buffConf = controlData.getBuffConfigBy(BUFF_NPC_PATIENCE);
             if (buffConf != null) {
                 int star = controlData.buffs.get(buffConf.sn);
                 controlData.taskMaxPatience = confDungeon.task_patience + buffConf.parameter[star - 1][0];
                 controlData.taskPatience = controlData.taskMaxPatience;
             }

             // 设置下一个任务元素
             controlData.nextTaskElementId = controlData.taskElementIds.size() > 1 ?
                 controlData.taskElementIds.get(1) : 0;
         }
     }

    /**
     * 处理翻牌请求
     */
    public void on_act_card_eliminate_flip_c2s(HumanObject humanObj, int pos) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}翻牌时，活动数据不存在", humanObj.id, type);
            return;
        }

        ControlCardEliminateData controlData = (ControlCardEliminateData) data.getControlData();
        if (controlData == null || controlData.stamina <= 0) {
            Log.activity.error("玩家{}的活动{}数据为空或体力不足，无法翻牌", humanObj.id, type);
            return;
        }

        // 检查位置是否有效
        if (pos < 1 || pos > controlData.cards.size()) {
            Log.activity.error("玩家{}请求翻牌位置{}超出范围，活动{}数据错误", humanObj.id, pos, type);
            return;
        }

        // 是否选择buff阶段
        if (controlData.buffChoices != null && !controlData.buffChoices.isEmpty()) {
            Log.activity.error("玩家{}请求翻牌时，当前处于buff选择阶段，无法翻牌", humanObj.id);
            return;
        }

        // 检查卡牌是否已翻开或已消除
        int cardIndex = pos - 1;
        if (controlData.cardStatus.get(cardIndex) != CARD_STATUS_UNFLIPPED &&
            controlData.cardStatus.get(cardIndex) != CARD_STATUS_XRAY) {
            Log.activity.error("玩家{}请求翻牌位置{}的卡牌状态{}不正确，活动{}数据错误", humanObj.id, pos, controlData.cardStatus.get(cardIndex), type);
            return;
        }

        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动{}找不到活动期数表ConfActivityTerm, round={}", type, data.getActControlData().getRound());
            return;
        }
        //记录npc任务数量，一次翻牌只能促发一个npc任务
        int taskSize = controlData.taskElementIds.size();

        // 消耗体力
        controlData.stamina--;
        controlData.totalStep++;

        // 构建响应消息
        MsgAct2.act_card_eliminate_flip_s2c.Builder builder = MsgAct2.act_card_eliminate_flip_s2c.newBuilder();
        builder.setActType(type);
        List<Integer> removeXRayList = controlData.updateXRayStatus();

        List<Integer> unFlippedList = null;
        // 添加翻开的卡牌
        controlData.setCardStatus(cardIndex, CARD_STATUS_FLIPPED);
        builder.addFlipPos(HumanManager.inst().to_p_key_value(pos, controlData.cards.get(cardIndex)));

        // 记录所有buff效果
        List<Define.p_card_eliminate_buff_effect.Builder> buffEffects = new ArrayList<>();
        // 透视效果
        ConfCardEliminateBuff buffConf = controlData.getBuffConfigBy(BUFF_FLIP_XRAY);
        if (buffConf != null) {
            Define.p_card_eliminate_buff_effect.Builder xrayEffect = Define.p_card_eliminate_buff_effect.newBuilder();
            xrayEffect.setBuffSn(buffConf.sn);
            int star = controlData.buffs.get(buffConf.sn);
            if (Utils.random(0, 10001) < buffConf.parameter[star - 1][0]) {
                int xrayCount = buffConf.parameter[star - 1][1];
                int xraySteps = buffConf.parameter[star - 1][2];
                controlData.addXRayCards(xrayCount, xraySteps);
                // 添加透视效果
                for (Map.Entry<Integer, Integer> entry : controlData.xRayCardMap.entrySet()) {
                    xrayEffect.addXRayPos(HumanManager.inst().to_p_key_value(entry.getKey(),
                            controlData.cards.get(entry.getKey() - 1)));
                }
                buffEffects.add(xrayEffect);
            }
        }

        Define.p_card_eliminate_buff_effect.Builder patienceEffect = null;
        int patienceEffectEliminateId = 0;
        // 耐心buff.0
        if (controlData.taskPatience > 0) {
            // 减少NPC耐心值
            controlData.taskPatience--;
        }
        // 3. 检查手动翻牌是否可以消除
        List<Integer> manualEliminatedPos = checkCardElimination(controlData, taskSize, confTerm, buffEffects, builder);
        builder.addAllFeliminatedPos(manualEliminatedPos);
        // 4. 翻牌时，有概率将相连的牌翻开
        buffConf = controlData.getBuffConfigBy(BUFF_FLIP_ADJACENT);
        if (buffConf != null) {
            Define.p_card_eliminate_buff_effect.Builder adjacentEffect = Define.p_card_eliminate_buff_effect.newBuilder();
            adjacentEffect.setBuffSn(buffConf.sn);
            int star = controlData.buffs.get(buffConf.sn);
            if (Utils.random(0, 10001) < buffConf.parameter[star - 1][0]) {
                // 翻开相连的卡牌
                List<Integer> adjacentFlips = flipAdjacentCards(controlData, cardIndex, buffConf.parameter[star - 1][1]);
                for (int flipPos : adjacentFlips) {
                    adjacentEffect.addFlipPos(HumanManager.inst().to_p_key_value(flipPos, controlData.cards.get(flipPos - 1)));
                    controlData.setCardStatus(flipPos - 1, CARD_STATUS_FLIPPED);
                }
                // 检查相连翻开的卡牌是否可以消除
                List<Integer> adjacentEliminatedPos = checkCardElimination(controlData, taskSize, confTerm, buffEffects, builder);
                if (!adjacentEliminatedPos.isEmpty()) {
                    adjacentEffect.addAllFeliminatedPos(adjacentEliminatedPos);
                    manualEliminatedPos.addAll(adjacentEliminatedPos);
                }
                // 4. 处理盖牌逻辑
                unFlippedList = unFlippedCards(controlData, buffEffects);
                adjacentEffect.addAllUnflippedPos(unFlippedList);
                buffEffects.add(adjacentEffect);
            }
        }
        if( unFlippedList == null) {
            unFlippedList = unFlippedCards(controlData, buffEffects);
            builder.addAllUnflippedPos(unFlippedList);
        }
        // 4. 如果有手动消除，添加到响应中
        if (!manualEliminatedPos.isEmpty()) {

            // 5. 处理消除触发的buff效果

            // 随机翻牌效果
            buffConf = controlData.getBuffConfigBy(BUFF_ELIMINATE_FLIP);
            if (buffConf != null) {
                Define.p_card_eliminate_buff_effect.Builder buffEffect = Define.p_card_eliminate_buff_effect.newBuilder();
                buffEffect.setBuffSn(buffConf.sn);
                int star = controlData.buffs.get(buffConf.sn);
                if (Utils.random(0, 10001) < buffConf.parameter[star - 1][0]) {
                    // 随机翻开卡牌
                    List<Integer> buffFlipPos = flipRandomCards(controlData, buffConf.parameter[star - 1][1]);
                    if (!buffFlipPos.isEmpty()) {
                        // 记录buff翻开的卡牌
                        for (int flipPos : buffFlipPos) {
                            buffEffect.addFlipPos(HumanManager.inst().to_p_key_value(flipPos,
                                    controlData.cards.get(flipPos - 1)));
                            controlData.setCardStatus(flipPos - 1, CARD_STATUS_FLIPPED);
                        }
                        // 检查buff翻开的卡牌是否可以消除
                        List<Integer> buffEliminatedPos = checkCardElimination(controlData, taskSize, confTerm, buffEffects, builder);
                        if (!buffEliminatedPos.isEmpty()) {
                            buffEffect.addAllFeliminatedPos(buffEliminatedPos);
                        }
                    }
                    unFlippedList = unFlippedCards(controlData, buffEffects);
                    buffEffect.addAllUnflippedPos(unFlippedList);
                    buffEffects.add(buffEffect);
                }
            }
        }

        // 更新任务状态
        if (controlData.taskElementId > 0) {
            if( controlData.taskPatience <= 0) {
                // 任务耐心值耗尽，重置任务
                updateTask(controlData, confTerm, taskSize);
            }
            if(controlData.taskElementId > 0 && controlData.taskPatience < controlData.taskMaxPatience) {
                // 检查Buff效果：翻牌时，有概率使当前NPC耐心+1
                buffConf = controlData.getBuffConfigBy(BUFF_FLIP_PATIENCE);
                if (buffConf != null) {
                    int star = controlData.buffs.get(buffConf.sn);
                    int probability = buffConf.parameter[star - 1][0];
                    int patienceAdd = buffConf.parameter[star - 1][1];
                    int canPatience = controlData.taskMaxPatience - controlData.taskPatience;
                    patienceAdd = Math.min(patienceAdd, canPatience);
                    if (Utils.random(1, 10001) <= probability) {
                        controlData.taskPatience = controlData.taskPatience + patienceAdd;
                        patienceEffect = Define.p_card_eliminate_buff_effect.newBuilder();
                        patienceEffect.setBuffSn(buffConf.sn);
                        patienceEffect.setPatienceChange(patienceAdd);
                        patienceEffectEliminateId = controlData.taskElementId;
                    }
                }
            }
            Define.p_card_eliminate_task.Builder taskBuilder = Define.p_card_eliminate_task.newBuilder();
            taskBuilder.setElementId(controlData.taskElementId);
            taskBuilder.setPatience(controlData.taskPatience);
            taskBuilder.setMaxPatience(controlData.taskMaxPatience);
            taskBuilder.setNextElementId(controlData.nextTaskElementId);
            taskBuilder.setRewardStamina(controlData.taskRewardStamina);
            builder.setTask(taskBuilder);
            if( patienceEffect != null && patienceEffectEliminateId == controlData.taskElementId) {
                // 添加耐心值变化效果
                buffEffects.add(patienceEffect);
            }
        }else {
            // 没有任务时
            builder.setTask(Define.p_card_eliminate_task.newBuilder());
        }

        // 将所有buff效果添加到响应中
        for (Define.p_card_eliminate_buff_effect.Builder effect : buffEffects) {
            builder.addBuffEffects(effect);
        }


        // 更新其他状态
        builder.setStamina(controlData.stamina);
        builder.setScore(controlData.score);
        builder.setCombo(controlData.combo);

        //遍历removeXRayList 删除掉状态不是未翻开的透视牌
        // 删除不符合条件的透视牌位置
        removeXRayList.removeIf(removePos -> controlData.cardStatus.get(removePos - 1) != CARD_STATUS_UNFLIPPED);

        builder.addAllDelXRayPos(removeXRayList);

        // 如果所有牌都已消除,进入下一轮
        // 检查游戏是否结束或进入下一轮

        if (controlData.isAllCardsEliminated()) {
            // 进入下一轮
            controlData.round++;
            controlData.cards.clear();
            controlData.cardStatus.clear();
            controlData.xRayCardMap.clear();
            // 获取下一轮配置
            ConfCardEliminateDungeon_0 nextConfDungeon = ConfCardEliminateDungeon_0.get(
                    type, confTerm.group_id, controlData.round);
            if (nextConfDungeon != null && controlData.openCount >= nextConfDungeon.unlock) {
                // 增加体力值
                ConfCardEliminateDungeon_0 currentConfDungeon = ConfCardEliminateDungeon_0.get(
                        type, confTerm.group_id, controlData.round - 1);
                if (currentConfDungeon != null) {
                    controlData.stamina += currentConfDungeon.spirit_reward; // 获取每轮增加的体力值
                    builder.setStamina(controlData.stamina);
                    builder.setRoundAddStamina(currentConfDungeon.spirit_reward);
                }

               // 清空buff选择列表
                refreshBuff(controlData);
                // 等待客户端选择buff后再生成新一轮的卡牌和任务
            } else {
                ConfGlobal confGlobal = ConfGlobal.get("Card_reward");
                //体力转化为积分
                if (confGlobal != null) {
                    controlData.score += confGlobal.value * controlData.stamina;
                } else {
                    Log.activity.error("活动{}找不到体力转化积分配置", type);
                }
                controlData.stamina = 0; // 重置体力
            }
        }
        if (controlData.stamina <= 0) {
            // 发送响应
            humanObj.sendMsg(builder.build());
            // 体力耗尽，游戏结束
            sendGameOverResponse(humanObj, confTerm, controlData);
        }else {
            // 发送响应
            humanObj.sendMsg(builder.build());
        }
        data.updateControlData();
    }

    private static List<Integer> unFlippedCards(ControlCardEliminateData controlData, List<Define.p_card_eliminate_buff_effect.Builder> buffEffects) {
        List<Integer> flippedCards = controlData.getFlippedCards();

        // 获取最大翻牌数配置
        int maxFlipCards = 2; // 默认值
        ConfCardEliminateBuff buffConf = controlData.getBuffConfigBy(BUFF_MAX_FLIP_CARDS);
        if (buffConf != null) {
            if (buffConf.parameter != null && buffConf.parameter.length > 0) {
                int star = controlData.buffs.get(buffConf.sn);
                maxFlipCards = buffConf.parameter[star - 1][0];
            }
        }

        if (flippedCards.size() >= maxFlipCards) {
            // 盖回所有未消除的翻开卡牌
            for (int flippedPos : flippedCards) {
                if (controlData.cardStatus.get(flippedPos - 1) == CARD_STATUS_FLIPPED) {
                    controlData.setCardStatus(flippedPos - 1, CARD_STATUS_UNFLIPPED);
                }
            }
            // 处理盖牌时的buff效果
            buffConf = controlData.getBuffConfigBy(BUFF_RESET_STAMINA);
            if (buffConf != null) {
                int star = controlData.buffs.get(buffConf.sn);
                if (Utils.random(0, 10001) < buffConf.parameter[star - 1][0]) {
                    if(controlData.taskElementId > 0) {
                        // 如果有任务元素，重置耐心值
                        controlData.taskRewardStamina += buffConf.parameter[star - 1][1];
                    }
                    Define.p_card_eliminate_buff_effect.Builder resetEffect =
                            Define.p_card_eliminate_buff_effect.newBuilder();
                    resetEffect.setBuffSn(buffConf.sn);
                    resetEffect.setStaminaChange(buffConf.parameter[star - 1][1]);
                    buffEffects.add(resetEffect);
                }
            }
            // 重置连击
            controlData.combo = 0;
            return flippedCards;
        }
        return new ArrayList<>();
    }

    /**
     * 检查翻开的卡牌是否可以消除
     */
    private List<Integer> checkCardElimination(ControlCardEliminateData controlData, int taskSize, ConfActivityTerm confTerm,
                                               List<Define.p_card_eliminate_buff_effect.Builder> buffEffects,
                                               MsgAct2.act_card_eliminate_flip_s2c.Builder builder) {
        List<Integer> flippedCards = controlData.getFlippedCards();
        List<Integer> eliminatedPos = new ArrayList<>();
        Map<Integer, List<Integer>> elementGroups = new HashMap<>();

        // 对翻开的卡牌按元素分组
        for (int pos : flippedCards) {
            int index = pos - 1;
            int elementId = controlData.cards.get(index);
            elementGroups.computeIfAbsent(elementId, k -> new ArrayList<>()).add(pos);
        }

        // 检查每组是否可以消除
        for (Map.Entry<Integer, List<Integer>> entry : elementGroups.entrySet()) {
            List<Integer> sameElements = entry.getValue();
            int elementId = entry.getKey();
            // 确保只消除成对的卡牌
            int pairCount = sameElements.size() / 2;
            if (pairCount > 0) {
                // 只取偶数张卡牌进行消除
                for (int i = 0; i < pairCount * 2; i++) {
                    eliminatedPos.add(sameElements.get(i));
                    controlData.setCardStatus(sameElements.get(i) - 1, CARD_STATUS_ELIMINATED);
                }
                controlData.combo++;
                addScore(controlData);

                // 消除对应的元素完成任务时，获得体力奖励
                if (elementId == controlData.taskElementId) {
                    controlData.stamina += controlData.taskRewardStamina;
                    builder.setTaskAddStamina(controlData.taskRewardStamina);
                    // 完成当前任务，更新下一个任务
                    updateTask(controlData, confTerm, taskSize);
                    // 检查是否有透视buff效果
                    ConfCardEliminateBuff buffConf = controlData.getBuffConfigBy(BUFF_REWARD_XRAY);
                    if (buffConf != null) {
                        Define.p_card_eliminate_buff_effect.Builder xrayEffect = Define.p_card_eliminate_buff_effect.newBuilder();
                        xrayEffect.setBuffSn(buffConf.sn);
                        int star = controlData.buffs.get(buffConf.sn);

                        // 获取透视牌数量和持续步数
                        if (buffConf.parameter != null && star <= buffConf.parameter.length) {
                            int xrayCount = buffConf.parameter[star - 1][0];  // 透视牌数量
                            int xraySteps = buffConf.parameter[star - 1][1];  // 持续步数
                            controlData.addXRayCards(xrayCount, xraySteps);
                            for (Map.Entry<Integer, Integer> entryCard : controlData.xRayCardMap.entrySet()) {
                                xrayEffect.addXRayPos(HumanManager.inst().to_p_key_value(entryCard.getKey(),
                                        controlData.cards.get(entryCard.getKey() - 1)));
                            }
                            // 添加到buff效果列表
                            buffEffects.add(xrayEffect);
                        }
                    }
                }
            }
        }

        return eliminatedPos;
    }

    private void updateTask(ControlCardEliminateData controlData, ConfActivityTerm confTerm, int npcTaskSize) {
        // 检查是否还有未完成的任务
        if (controlData.taskElementIds == null || controlData.taskElementIds.isEmpty()) {
            // 所有任务已完成，重置任务
            controlData.taskElementId = 0;
            controlData.taskPatience = 0;
            controlData.taskMaxPatience = 0;
            controlData.nextTaskElementId = 0;
            controlData.taskRewardStamina = 0;
        } else if(npcTaskSize == controlData.taskElementIds.size()) {
            // 获取下一个任务元素
            controlData.taskElementIds.remove(0);
            if (!controlData.taskElementIds.isEmpty()) {
                ConfCardEliminateDungeon_0 confDungeon = ConfCardEliminateDungeon_0.get(
                        type, confTerm.group_id, controlData.round);
                controlData.taskElementId = controlData.taskElementIds.get(0);
                controlData.taskPatience = controlData.taskMaxPatience; // 重置耐心值
                controlData.taskRewardStamina = confDungeon.reward;
                controlData.nextTaskElementId = controlData.taskElementIds.size() > 1 ? controlData.taskElementIds.get(1) : 0;
                // 考虑buff效果：NPC初始耐心值上限+1
                ConfCardEliminateBuff buffConf = controlData.getBuffConfigBy(BUFF_NPC_PATIENCE);
                if (buffConf != null) {
                    controlData.taskPatience = controlData.taskMaxPatience; // 重置耐心值为上限
                }
            } else {
                // 没有更多任务了
                controlData.taskElementId = 0;
                controlData.taskPatience = 0;
                controlData.taskMaxPatience = 0;
                controlData.nextTaskElementId = 0;
                controlData.taskRewardStamina = 0;
            }
        }
    }

    private void addScore(ControlCardEliminateData controlData) {
        // 根据连消次数获取积分加成
        ConfCardEliminateCombo comboConf = ConfCardEliminateCombo.get(controlData.combo);
        if (comboConf == null) {
            // 如果找不到当前连消次数的配置，获取最大连消次数的配置
            int maxCombo = 0;
            for (ConfCardEliminateCombo conf : ConfCardEliminateCombo.findAll()) {
                if (conf.sn > maxCombo) {
                    maxCombo = conf.sn;
                    comboConf = conf;
                }
            }
        }

        // 基础消除积分
        ConfCardEliminateCombo comboConfFirst = ConfCardEliminateCombo.get(1);
        int baseScore = 100;
        if( comboConfFirst != null) {
            baseScore = comboConfFirst.score; // 获取基础积分
        }
        if(controlData.isBuyHigh){
            ConfGlobal confGlobal = ConfGlobal.get("Card_privilege");
            if( confGlobal == null || confGlobal.strValue == null || confGlobal.strValue.isEmpty()) {
                Log.activity.error("活动{}找不到特权配置", type);
            }else {
                Map<Integer, Integer> privilegeMap = Utils.strToMapIntInt(confGlobal.strValue);
                int buyHighValue = privilegeMap.getOrDefault(PayMallTypeKey.wartoken_type_2, 0);
                if (buyHighValue > 0) {
                    baseScore += buyHighValue; // 如果购买了高额积分，增加基础积分
                }else{
                    Log.activity.error("活动{}特权配置中没有找到高额积分的值", type);
                }
            }
        }
        // 连消加成积分
        int bonus = comboConf.bonus;

        // 检查是否有积分加成buff
        ConfCardEliminateBuff buffConf = controlData.getBuffConfigBy(BUFF_SCORE_BONUS);
        if (buffConf != null) {
            int star = controlData.buffs.get(buffConf.sn);
            if (buffConf.parameter != null && star <= buffConf.parameter.length) {
                // 额外增加固定积分
                baseScore += buffConf.parameter[star - 1][0];
            }
        }
        // 计算最终积分 = 基础积分 x 连消加成 x 2张牌
        int finalScore = (int)(baseScore * bonus/10000.0f * 2);
        // 更新当前局内积分
        controlData.score += finalScore;
    }

    //翻开周围卡牌
    private List<Integer> flipAdjacentCards(ControlCardEliminateData controlData, int pos, int flipCount) {
        List<Integer> adjacentFlipPos = new ArrayList<>();
        int cardNum = controlData.cards.size();

        // 根据卡牌总数确定行列数
        int rows, cols;
        if (cardNum == 6) {
            rows = 2; cols = 3;
        } else if (cardNum == 12) {
            rows = 3; cols = 4;
        } else if (cardNum == 16) {
            rows = 4; cols = 4;
        } else if (cardNum == 20) {
            rows = 4; cols = 5;
        } else {
            return adjacentFlipPos;
        }

        // 将位置转换为行列坐标
        int row = pos / cols;
        int col = pos % cols;

        // 检查周围8个方向的相邻卡牌
        int[][] directions = {
            {-1, -1}, {-1, 0}, {-1, 1},  // 左上、上、右上
            {0, -1},           {0, 1},    // 左、右
            {1, -1},  {1, 0},  {1, 1}     // 左下、下、右下
        };

        // 收集所有可翻开的相邻位置
        List<Integer> availablePos = new ArrayList<>();
        for (int[] dir : directions) {
            int newRow = row + dir[0];
            int newCol = col + dir[1];

            // 检查位置是否有效
            if (newRow >= 0 && newRow < rows && newCol >= 0 && newCol < cols) {
                int newPos = newRow * cols + newCol + 1;
                // 检查卡牌状态是否未翻开或透视状态
                if (controlData.cardStatus.get(newPos - 1) == CARD_STATUS_UNFLIPPED ||
                    controlData.cardStatus.get(newPos - 1) == CARD_STATUS_XRAY) {
                    availablePos.add(newPos);
                }
            }
        }

        // 随机选择指定数量的周围卡牌翻开
        int actualFlipCount = Math.min(flipCount, availablePos.size());
        int consumedFlips = 0; // 记录已使用的翻牌次数
        while (consumedFlips < actualFlipCount && !availablePos.isEmpty()) {
            int randomIndex = Utils.random(0, availablePos.size());
            int flipPos = availablePos.remove(randomIndex);
            consumedFlips++; // 无论卡片状态如何，都计入次数

            // 只有未翻开或透视状态的卡片才添加到结果列表
            int cardStatus = controlData.cardStatus.get(flipPos - 1);
            if (cardStatus == CARD_STATUS_UNFLIPPED || cardStatus == CARD_STATUS_XRAY) {
                controlData.setCardStatus(flipPos - 1, CARD_STATUS_FLIPPED);
                adjacentFlipPos.add(flipPos);
            }
        }

        return adjacentFlipPos;
    }


    /**
     * 随机翻开卡牌
     */
    private List<Integer> flipRandomCards(ControlCardEliminateData controlData, int count) {
        List<Integer> randomFlipPos = new ArrayList<>();

        // 获取未翻开的卡牌
        List<Integer> unflippedCards = controlData.getUnflippedCards();
        if (unflippedCards.isEmpty()) {
            return randomFlipPos;
        }

        // 随机选择指定数量的卡牌翻开
//        Collections.shuffle(unflippedCards);
        int flipCount = Math.min(count, unflippedCards.size());

        for (int i = 0; i < flipCount; i++) {
            int pos = unflippedCards.get(i);
            int index = pos - 1;

            // 翻开卡牌
            controlData.setCardStatus(index, CARD_STATUS_FLIPPED);
            randomFlipPos.add(pos);
        }

        return randomFlipPos;
    }

    public void on_act_card_eliminate_round_complete_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}轮次完成时，活动数据不存在", humanObj.id, type);
            return;
        }
        ControlCardEliminateData controlData = (ControlCardEliminateData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动{}数据为空", humanObj.id, type);
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动{}找不到活动期数表ConfActivityTerm, round={}", type, data.getActControlData().getRound());
            return;
        }
        if(controlData.isAllCardsEliminated() && !controlData.cards.isEmpty()){
            Log.activity.error("玩家{}请求活动{}轮次完成时，卡牌未全部消除或数据错误", humanObj.id, type);
            return;
        }
        generateCards(controlData, confTerm);
        generateTasks(controlData, confTerm);

        // 发送轮次完成响应
        MsgAct2.act_card_eliminate_round_complete_s2c.Builder roundCompleteBuilder =
                MsgAct2.act_card_eliminate_round_complete_s2c.newBuilder();
        roundCompleteBuilder.setActType(type);
        roundCompleteBuilder.setRound(controlData.round); // 发送完成的轮次
        roundCompleteBuilder.setStamina(controlData.stamina);
        roundCompleteBuilder.addAllBuffChoices(controlData.buffChoices);
        roundCompleteBuilder.setRefreshCount(controlData.refreshCount);
        Define.p_card_eliminate_task.Builder taskBuilder = Define.p_card_eliminate_task.newBuilder();
        taskBuilder.setElementId(controlData.taskElementId);
        taskBuilder.setPatience(controlData.taskPatience);
        taskBuilder.setMaxPatience(controlData.taskMaxPatience);
        taskBuilder.setNextElementId(controlData.nextTaskElementId);
        taskBuilder.setRewardStamina(controlData.taskRewardStamina);
        roundCompleteBuilder.setTask(taskBuilder);
        // 更新数据并发送响应
        humanObj.sendMsg(roundCompleteBuilder.build());

//        sendAfterSelectBuffResponse(humanObj, confTerm, controlData, 0);
    }

    public void on_act_card_eliminate_select_buff_c2s(HumanObject humanObj, int buffId) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}选择buff时，活动{}数据不存在", humanObj.id, type);
            return;
        }

        ControlCardEliminateData controlData = (ControlCardEliminateData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动{}数据为空", humanObj.id, type);
            return;
        }

        // 检查是否是可选的buff
        if (!controlData.buffChoices.contains(buffId)) {
            Log.activity.error("玩家{}选择的buff{}不在可选列表中", humanObj.id, buffId);
            return;
        }

        // 获取buff配置
        ConfCardEliminateBuff buffConf = ConfCardEliminateBuff.get(buffId);
        if (buffConf == null) {
            Log.activity.error("找不到buff配置，buffId={}", buffId);
            return;
        }

        // 更新buff数据
        int currentStar = controlData.buffs.getOrDefault(buffId, 0);
        if (currentStar < buffConf.star) {
            currentStar += 1;
            controlData.buffs.put(buffId, currentStar);
        }

        // 清空buff选择列表
        controlData.buffChoices.clear();

        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        // 考虑buff效果：NPC初始耐心值上限+1
        ConfCardEliminateBuff confBuff = controlData.getBuffConfigBy(BUFF_NPC_PATIENCE);
        if (confBuff != null) {
            int patienceBuffStar = controlData.buffs.getOrDefault(confBuff.sn, 0);
            ConfCardEliminateDungeon_0 confDungeon = ConfCardEliminateDungeon_0.get(
                    type, confTerm.group_id, controlData.round);
            controlData.taskMaxPatience =  confDungeon.task_patience + confBuff.parameter[patienceBuffStar-1][0]; // 获取当前星级的耐心值上限
            controlData.taskPatience = controlData.taskMaxPatience; // 重置耐心值为上限
        }

        sendAfterSelectBuffResponse(humanObj, confTerm, controlData, buffId);
        data.updateControlData();
    }

    private void sendAfterSelectBuffResponse(HumanObject humanObj, ConfActivityTerm confTerm, ControlCardEliminateData controlData, int buffId) {
        // 获取活动期数配置
        if (confTerm == null) {
            Log.activity.error("活动{}的期数配置不存在", type);
            return;
        }

        // 生成新一轮的卡牌和任务
//        generateCards(controlData, confTerm);
//        generateTasks(controlData, confTerm);

        // 构建响应消息
        MsgAct2.act_card_eliminate_select_buff_s2c.Builder builder =
                MsgAct2.act_card_eliminate_select_buff_s2c.newBuilder();
        builder.setActType(type);
        builder.setBuffId(buffId);

        // 添加所有buff信息
        for (Map.Entry<Integer, Integer> entry : controlData.buffs.entrySet()) {
            builder.addAllBuffs(HumanManager.inst().to_p_key_value(entry.getKey(), entry.getValue()));
        }

        // 添加当前轮数和体力值
        builder.setRound(controlData.round);
        builder.setStamina(controlData.stamina);

        // 添加任务信息
        Define.p_card_eliminate_task.Builder taskBuilder = Define.p_card_eliminate_task.newBuilder();
        taskBuilder.setElementId(controlData.taskElementId);
        taskBuilder.setPatience(controlData.taskPatience);
        taskBuilder.setMaxPatience(controlData.taskMaxPatience);
        taskBuilder.setNextElementId(controlData.nextTaskElementId);
        taskBuilder.setRewardStamina(controlData.taskRewardStamina);
        builder.setTask(taskBuilder);

        // 发送响应
        humanObj.sendMsg(builder.build());
    }

    public void on_act_card_eliminate_game_over_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}游戏结束时，活动数据不存在", humanObj.id, type);
            return;
        }

        ControlCardEliminateData controlData = (ControlCardEliminateData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id, type);
            return;
        }

        // 获取活动期数配置
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            return;
        }


        // 发送响应
        sendGameOverResponse(humanObj, confTerm, controlData);
        // 构建响应消息

        // 更新数据
        data.updateControlData();
    }

    private void sendGameOverResponse(HumanObject humanObj, ConfActivityTerm confTerm, ControlCardEliminateData controlData) {
        int passRound = controlData.round - 1;
        List<Define.p_reward> reward = new ArrayList<>();
        if(passRound > 0){
            ConfCardEliminateDungeon_0 confDungeon = ConfCardEliminateDungeon_0.get(type, confTerm.group_id, passRound);
            if (confDungeon == null) {
                Log.activity.error("玩家:{}活动{}的轮次配置不存在, round={}", humanObj.id, type, passRound);
            }else {
                ProduceManager.inst().produceAdd(humanObj, confDungeon.round_reward, MoneyItemLogKey.翻牌);
                reward = InstanceManager.inst().to_p_rewardList(confDungeon.round_reward);
            }
        }
        controlData.sumScore += controlData.score; // 累加当前局的积分到最高分
        if(controlData.score > controlData.maxScore) {
            long oldMaxScore = controlData.maxScore; // 记录旧的最高分
            controlData.maxScore = controlData.score;
            if (controlData.maxScore > oldMaxScore) {
                addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2007, controlData.maxScore, false, new Object[]{type,controlData.maxScore});
                IActivityControl control = ActivityControlTypeFactory.getTypeData(confTerm.parameter);
                if(control == null || !(control instanceof ActivityControlWarToken)) {
                    Log.activity.error("翻牌活动{}找不到对应的战力{}", type, confTerm.parameter);
                }else {
                    ActivityControlWarToken activityControlWarToken = (ActivityControlWarToken) control;
                    activityControlWarToken.addWartokenExp(humanObj, (int)(controlData.maxScore - oldMaxScore));
                }
            }
        }
        addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2008, controlData.score, true, new Object[]{type,controlData.score});
        MsgAct2.act_card_eliminate_game_over_s2c.Builder builder = MsgAct2.act_card_eliminate_game_over_s2c.newBuilder();
        builder.setActType(type);
        builder.setFinalScore(controlData.score);
        builder.setHighestScore((int) controlData.maxScore);
        builder.setSumScore(controlData.sumScore);
        builder.setTotalStep(controlData.totalStep);
        builder.addAllRewards(reward);

        // 获取排名
        RankManager.inst().getMyRankAndScore(
            confTerm.rank_id,
            humanObj.getHuman().getServerId(),
            humanObj.id,
            ret -> {
                if (ret.failed()) {
                    Log.activity.error("排行榜查询失败, rankId={}, serverId={}, humanId={}, e={}",
                        confTerm.rank_id,
                        humanObj.getHuman().getServerId(),
                        humanObj.id,
                        ret.cause().getMessage(),
                        ret.cause());
                    humanObj.sendMsg(builder.build());
                    return;
                }
                builder.setRank(Utils.intValue(ret.result().get(0)));
                // 发送响应
                humanObj.sendMsg(builder.build());
            });
        // 重置游戏数据
        controlData.resetGameData();
    }

    public void on_act_stamina_refresh_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("slime没有活动数据, humanId={}, type={}", humanObj.getHumanId(), type);
            return;
        }
        ControlCardEliminateData controlData = (ControlCardEliminateData) data.getControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动轮次配置表没有找到活动sn:{},轮次:{}", data.getActControlData().getActivitySn(), data.getActControlData().getRound());
            return;
        }
        MsgAct2.act_stamina_refresh_s2c.Builder msg = MsgAct2.act_stamina_refresh_s2c.newBuilder();
        int oldStamina = controlData.ticketMgr.stamina;
        int newStamina = controlData.ticketMgr.getStamina();
        msg.setActType(type);
        msg.setStamina(newStamina);
        msg.setNextRecoverTime(controlData.getNextRecoverTime());
        if(oldStamina != newStamina) {
            data.updateControlData();
        }
        humanObj.sendMsg(msg);
    }

    /**
     * 处理buff刷新请求
     */
    public void on_act_card_buff_refresh_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}刷新buff时，活动数据不存在", humanObj.id, type);
            return;
        }

        ControlCardEliminateData controlData = (ControlCardEliminateData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动{}数据为空", humanObj.id, type);
            return;
        }

        // 检查是否有可刷新的buff选项
        if (controlData.buffChoices.isEmpty()) {
            Log.activity.error("玩家{}请求刷新buff时，没有可刷新的buff选项", humanObj.id);
            return;
        }

        // 获取刷新次数和刷新消耗配置
        ConfGlobal confGlobal = ConfGlobal.get("card_buff_refresh");
        if (confGlobal == null || confGlobal.strValue == null) {
            Log.activity.error("找不到buff刷新配置Card_refresh_buff");
            return;
        }

        Map<Integer, Integer> countCostMap = Utils.strToMapIntInt(confGlobal.strValue);
        int maxRefreshCount = Collections.max(countCostMap.keySet());

        // 检查刷新次数是否达到上限
        if (controlData.refreshCount >= maxRefreshCount) {
            Log.activity.error("玩家{}刷新次数已达上限", humanObj.id);
            return;
        }
        Integer cost = countCostMap.get(controlData.refreshCount + 1);
        if (cost == null) {
            Log.activity.error("玩家{}刷新buff时，找不到对应的消耗配置, refreshCount={}", humanObj.id, controlData.refreshCount + 1);
            return;
        }
        // 检查并扣除刷新消耗
        if (cost != 0 && !ProduceManager.inst().checkAndCostItem(humanObj, TokenItemType.GOLD, cost, MoneyItemLogKey.翻牌).success) {
            Log.activity.error("玩家{}刷新buff时货币不足", humanObj.id);
            return;
        }

        // 增加刷新次数
        controlData.refreshCount++;

        refreshBuff(controlData);

        // 更新数据
        data.updateControlData();

        // 发送响应
        MsgAct2.act_card_buff_refresh_s2c.Builder builder = MsgAct2.act_card_buff_refresh_s2c.newBuilder();
        builder.setActType(type);
        builder.setRefreshCount(controlData.refreshCount);
        builder.addAllBuffChoices(controlData.buffChoices);
        humanObj.sendMsg(builder.build());
    }

    private static void refreshBuff(ControlCardEliminateData controlData) {
        // 生成新的buff选项
        controlData.buffChoices.clear();
        List<Integer> availableBuffs = new ArrayList<>();

        // 优先检查已获得但未满星的buff
        for (Map.Entry<Integer, Integer> entry : controlData.buffs.entrySet()) {
            int buffId = entry.getKey();
            int currentStar = entry.getValue();
            ConfCardEliminateBuff buff = ConfCardEliminateBuff.get(buffId);
            if (buff != null && currentStar < buff.star) {
                availableBuffs.add(buffId);
            }
        }

        // 如果没有已获得的可升星buff,且已获得的buff数量小于3个,则从未获得的buff中选择
        if (controlData.buffs.size() < 3) {
            for (ConfCardEliminateBuff buff : ConfCardEliminateBuff.findAll()) {
                if (controlData.score >= buff.unlock
                        && controlData.openCount >= buff.unlock2[0] && controlData.openCount <= buff.unlock2[1]
                        &&!controlData.buffs.containsKey(buff.sn)) {
                    availableBuffs.add(buff.sn);
                }
            }
        }

        // 随机选择3个buff
        if (!availableBuffs.isEmpty()) {
//            Collections.shuffle(availableBuffs);
            int choiceCount = Math.min(3, availableBuffs.size());
            controlData.buffChoices.addAll(availableBuffs.subList(0, choiceCount));
        }
    }
}
