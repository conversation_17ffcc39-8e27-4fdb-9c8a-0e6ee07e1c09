package org.gof.demo.worldsrv.activity.calculator;


import com.alibaba.fastjson.JSONObject;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.dbsrv.redis.Tool;
import org.gof.core.support.Param;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.core.support.function.GofReturnFunction1;
import org.gof.demo.worldsrv.activity.ActivityControlType;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.EActivityType;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.AbstractControlData;
import org.gof.demo.worldsrv.activity.data.serverdata.IServerActData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.entity.ActivityData;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.mall.PayMallTypeKey;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.task.type.ITaskTypeData;
import org.gof.demo.worldsrv.task.type.TaskTypeDataFactory;
import org.gof.demo.worldsrv.task.type.activityData.ActivityTaskVO;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public abstract class AbstractActivityControl implements IActivityControl {
    protected int type;

    protected AbstractActivityControl(int type) {
        this.type = type;
    }

    @Override
    public IServerActData initServerActData(int round) {
        return null;
    }

    @Override
    public IServerActData initServerActData(int round, ActivityData activityData) {
        return initServerActData(round);
    }

    @Override
    public IServerActData parseServerActData(String json, int round) {
        return null;
    }

    @Override
    public void settleServerActData(int serverId, int actSn, int round) {
    }

    @Override
    public void initActivityData(HumanObject humanObj, ConfActivityControl confControl, ActivityVo vo) {
        //初始化活动数据
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            data = new ActivityControlObjectData(humanObj, type, vo);
            humanObj.operation.activityControlData.addControlObjectData(type, data);
            initActivityControlData(humanObj, data, confControl, vo);
            data.persist();
        } else {
            //
            if(data.getActControlData().getOpenTime() != vo.openTime && data.getActControlData().getCloseTime() != vo.closeTime){
                Log.temp.info("===活动时间改变，删除旧的数据，重新开始活动， humanId={}, activitySn={}, ActivityVo={}, data={}",
                        humanObj.id, data.getActControlData().getActivitySn(), vo, data.getActControlData());
                humanObj.operation.activityControlData.removerObjectData(type);
                data = new ActivityControlObjectData(humanObj, type, vo);
                humanObj.operation.activityControlData.addControlObjectData(type, data);
                initActivityControlData(humanObj, data, confControl, vo);
                data.persist();
            }
        }
    }

    protected void initActivityControlData(HumanObject humanObj, ActivityControlObjectData data, ConfActivityControl confControl,ActivityVo vo) {
        //初始获得任务数据
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(confControl.type, vo.round);
        if(confTerm == null) {
            return;
        }
        //记录需要回收的道具
        if(confTerm.recycle_goods_id != null && confTerm.recycle_goods_id.length > 0){
            JSONObject recycleJson = Utils.toJSONObject(humanObj.operation.itemData.getItem().getItemRecycleJSON());
            int actCloseTime = data.getActControlData().getCloseTime();
            String key = Integer.toString(confTerm.sn);
            // 如果已存在回收时间，取最大值
            int existTime = recycleJson.getIntValue(key);
            recycleJson.put(key, Math.max(existTime, actCloseTime + confControl.showTime));
            humanObj.operation.itemData.getItem().setItemRecycleJSON(recycleJson.toJSONString());
        }

        clearPayMallBuyNum(humanObj);

        if(confTerm.task_list == null || confTerm.task_list.length == 0) {
            return;
        }
        if(!data.getActivityTask().isEmpty()) {
            return;
        }
        for (int i = 0; i < confTerm.task_list.length; i++) {
            ConfActivityTaskGroup confTaskGroup = ConfActivityTaskGroup.get(confTerm.task_list[i]);
            if(confTaskGroup == null || confTaskGroup.task_list == null || confTaskGroup.task_list.length == 0) {
                Log.game.error("活动任务组配置不存在，活动sn:{},任务组sn:{}", confControl.sn, confTerm.task_list[i]);
                continue;
            }

            for (int j = 0; j < confTaskGroup.task_list.length; j++){
                ConfActivityTask confTask = ConfActivityTask.get(confTaskGroup.task_list[j]);
                if(confTask == null) {
                    Log.game.error("活动任务配置不存在，活动sn:{},任务sn:{}", confControl.sn, confTaskGroup.task_list[j]);
                    continue;
                }
                ActivityTaskVO taskVO = new ActivityTaskVO(confControl.sn, confControl.type, confTask, confTaskGroup.sn);
                ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(taskVO.getConditionType());
                if (idata != null) {
                    idata.checkPlan(humanObj, taskVO, 0);
                }
                data.addActivityTask(taskVO,false);
            }
        }
        data.setActivityTask();
    }

    private void clearPayMallBuyNum(HumanObject humanObj) {
        List<Integer> confSnList = GlobalConfVal.actTypePayMallSnMap.get(type);
        if(confSnList != null && !confSnList.isEmpty()){
            String json = humanObj.operation.mall.getPayMallBuyNumMap();
            Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(json);
            boolean isReset = false;
            for (Integer confSn : confSnList) {
                ConfPayMall confPayMall = ConfPayMall.get(confSn);
                if(confPayMall == null || confPayMall.type != PayMallTypeKey.PAY_Type_7 || snBuyNumMap.get(confSn) == null){
                    continue;
                }
                isReset = true;
                snBuyNumMap.remove(confSn);
            }
            if(isReset){
                humanObj.operation.mall.setPayMallBuyNumMap(Utils.mapIntIntToJSON(snBuyNumMap));
            }
        }
    }

    /**
     * 清掉指定类型的礼包购买次数, 为了灵活调整过滤条件, 使用 GofReturnFunction1
     * 要求礼包的重置配置成0
     * @param humanObj      玩家对象
     * @param filter        过滤lamda
     */
    protected void clearPayMallBuyNum(HumanObject humanObj, GofReturnFunction1<Boolean, ConfPayMall> filter) {
        List<Integer> confSnList = GlobalConfVal.actTypePayMallSnMap.get(type);
        if (confSnList == null || confSnList.isEmpty()) {
            return;
        }
        String json = humanObj.operation.mall.getPayMallBuyNumMap();
        Map<Integer, Integer> snBuyNumMap = Utils.jsonToMapIntInt(json);
        boolean isReset = false;
        for (Integer confSn : confSnList) {
            ConfPayMall confPayMall = ConfPayMall.get(confSn);
            if (confPayMall == null || confPayMall.reset != 0) {
                continue;
            }
            if (!filter.apply(confPayMall)) {
                continue;
            }
            isReset = true;
            snBuyNumMap.remove(confSn);
        }
        if (isReset) {
            humanObj.operation.mall.setPayMallBuyNumMap(Utils.mapIntIntToJSON(snBuyNumMap));
        }
    }


    public void onActivityClose(HumanObject humanObj, ConfActivityControl confControl) {
        if(confControl.type == ActivityControlType.Act_33 && confControl.isClose == 0){
            return;
        }
        if(confControl.showTime > 0){
            ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
            if (data == null) {
                return;
            }
            ActControlData actControlData = data.getActControlData();
            if(actControlData.getState() == EActivityType.STATE_ENDSHOW){
                return;
            }

            int roundId = ActivityManager.inst().getActivityTermSn(confControl.type, confControl.sn);
            Define.p_act.Builder act = Define.p_act.newBuilder();
            act.setId(actControlData.getActivitySn());
            act.setType(type);
            act.setRound(actControlData.getRound());
            act.setRoundId(roundId);
            act.setStartTime(actControlData.getOpenTime());
            act.setEndTime(actControlData.getCloseTime() + confControl.showTime);
            act.setState(EActivityType.STATE_ENDSHOW);
            MsgAct.act_update_s2c.Builder msg =  MsgAct.act_update_s2c.newBuilder();
            msg.setUpdate(act);
            humanObj.sendMsg(msg);

            actControlData.setState(EActivityType.STATE_ENDSHOW);
            data.updateControlData();
        }
    }
    public void onActivityEndShow(HumanObject humanObj, ConfActivityControl confControl) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        //道具回收
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if(confTerm != null){
            ActivityManager.inst().recycleActivityItem(humanObj,confControl.sn, confTerm);
        }else {
            Log.game.error("活动结束回收道具失败，活动sn:{},轮次sn:{}", confControl.sn, data.getActControlData().getRound());
        }
        Define.p_act.Builder act = Define.p_act.newBuilder();
        act.setId(data.getActControlData().getActivitySn());
        act.setType(type);
        act.setRound(data.getActControlData().getRound());
        act.setRoundId(0);
        act.setStartTime(0);
        act.setEndTime(0);
        act.setState(EActivityType.STATE_NULL);

        // 跨服战在非closed下是常驻活动，不通知关闭
        if(type != ActivityControlType.Act_33 || (confControl != null && confControl.isClose == 1)){
            MsgAct.act_update_s2c.Builder msg =  MsgAct.act_update_s2c.newBuilder();
            msg.setUpdate(act);
            humanObj.sendMsg(msg);
        }
        humanObj.operation.activityControlData.removerObjectData(type);
    }

    @Override
    public void sendActivityData(HumanObject humanObj) {
        Define.p_act.Builder act = toActivityData(humanObj);
        if (act == null) {
            return;
        }
        if(!humanObj.openActivitySnList.contains(act.getId()) && !humanObj.showActivitySnList.contains(act.getId())){
            return;
        }
        ConfActivityControl conf = ConfActivityControl.get(act.getId());
        if(!humanObj.isModUnlock(conf.newfunctionID)){
            return;
        }
        MsgAct.act_update_s2c.Builder msg =  MsgAct.act_update_s2c.newBuilder();
        msg.setUpdate(act);
        humanObj.sendMsg(msg);
    }
    public void sendActivityTaskData(HumanObject humanObj){
        MsgAct.act_task_update_s2c.Builder msg = MsgAct.act_task_update_s2c.newBuilder();
        msg.setType(type);
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        List<Define.p_act_task> taskList = to_p_act_task_list(data);
        msg.addAllTaskList(taskList);
        humanObj.sendMsg(msg);
    }
    @Override
    public Define.p_act.Builder toActivityData(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return null;
        }
        ActControlData actControlData = data.getActControlData();
        int activitySn = actControlData.getActivitySn();
        ConfActivityControl confControl = ConfActivityControl.get(activitySn);
        if(confControl == null) {
            Log.game.error("玩家{}获取失败ConfActivityControl sn={}", humanObj.id, activitySn);
            return null;
        }
        Define.p_act.Builder act = Define.p_act.newBuilder();
        act.setId(actControlData.getActivitySn());
        act.setType(type);
        int roundId = 0;
        if(confControl.is_round == 1){
            act.setRound(actControlData.getRound());
            roundId = ActivityManager.inst().getActivityTermSn(confControl.type, actControlData.getRound());
            if(actControlData.getRound() == activitySn){
                Log.temp.error("===循环活动出错了sn={}, round={}, roundId={}", activitySn, actControlData.getRound(), roundId);
            }
        } else {
            act.setRound(activitySn);
            roundId = ActivityManager.inst().getActivityTermSn(confControl.type, activitySn);
            if(actControlData.getRound() != activitySn){
                Log.temp.error("===不循环活动出错了sn={}, round={}, roundId={}", activitySn, actControlData.getRound(), roundId);
                actControlData.setRound(activitySn);
                actControlData.update();
            }
        }
        int now = Utils.getTimeSec();
        int endShowTime = actControlData.getCloseTime()+confControl.showTime;
        act.setRoundId(roundId);
        act.setStartTime(actControlData.getOpenTime());
        act.setEndTime(endShowTime);
        if(now <= actControlData.getCloseTime()){
            act.setState(EActivityType.STATE_OPEN);
        }else if(now <= actControlData.getCloseTime()+confControl.showTime){
            act.setState(EActivityType.STATE_ENDSHOW);
        }else{
            Log.game.error("活动存在问题下发了过期的活动：{}，humanId={}", actControlData.getActivitySn(), humanObj.id);
        }

        act.addStateTime(Define.p_act_state_time.newBuilder().setState(EActivityType.STATE_OPEN).setStartTime(actControlData.getOpenTime()).setEndTime(actControlData.getCloseTime()-1));
        act.addStateTime(Define.p_act_state_time.newBuilder().setState(EActivityType.STATE_ENDSHOW).setStartTime(actControlData.getOpenTime()).setEndTime(endShowTime-1));
        act.addAllTaskList(to_p_act_task_list(data));
        return act;
    }

    @Override
    public void unlockActivity(HumanObject humanObj) {
        sendActivityData(humanObj);
    }

    @Override
    public void pay(HumanObject humanObj, int payMallSn) {
    }

    @Override
    public void itemUse(HumanObject humanObj, int goodsId, int num) {
    }

    @Override
    public void itemAdd(HumanObject humanObj, int goodsId, int num) {
    }
    public List<Define.p_act_task> to_p_act_task_list(ActivityControlObjectData data) {
        List<Define.p_act_task> taskList = new ArrayList<>();
        Map<Integer, ActivityTaskVO> taskVOMap = data.getActivityTask();
        if(taskVOMap == null) {
           return taskList;
        }
        for (ActivityTaskVO taskVO : taskVOMap.values()) {
            Define.p_act_task.Builder task = Define.p_act_task.newBuilder();
            task.setTaskId(taskVO.getTaskSn());
            task.setGroupId(taskVO.getGroupSn());
            task.setCount(taskVO.getPlan());
            task.setState(ActivityManager.inst().getTaskState(taskVO.getStatus()));
            taskList.add(task.build());
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if(confTerm == null || confTerm.task_list == null || confTerm.task_list.length == 0) {
            return taskList;
        }
        Map<Integer,Integer> perfectMap = Utils.jsonToMapIntInt(data.getActControlData().getPerfectMap());
        for (int i = 0; i < confTerm.task_list.length; i++) {
            ConfActivityTaskGroup confGroup = ConfActivityTaskGroup.get(confTerm.task_list[i]);
            if(confGroup == null || confGroup.reward == null || confGroup.reward.length == 0) {
                continue;
            }
            Define.p_act_task.Builder group = ActivityManager.inst().to_p_act_task_group(confTerm.task_list[i],perfectMap);
            taskList.add(group.build());
        }
        return taskList;

    }

    public void on_act_task_reward_c2s(HumanObject humanObj, int taskId, int groupId) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if(data == null) {
            return;
        }

        Map<Integer, ActivityTaskVO> taskVOMap = data.getActivityTask();
        if(taskVOMap == null) {
            return;
        }
        if(taskId == 0){
            Map<Integer,Integer> perfectMap = Utils.jsonToMapIntInt(data.getActControlData().getPerfectMap());
            if(perfectMap.getOrDefault(groupId, 0) != EActivityType.TASK_CAN_GET) {
                return;
            }
            if(!ActivityManager.inst().isAllTasksCompletedInGroup(taskVOMap, groupId)) {
               return;
            }
            ConfActivityTaskGroup confTaskGroup = ConfActivityTaskGroup.get(groupId);
            if(confTaskGroup == null || confTaskGroup.reward == null || confTaskGroup.reward.length == 0) {
                return;
            }
            perfectMap.put(groupId, EActivityType.TASK_HAD_GET);
            data.getActControlData().setPerfectMap(Utils.mapIntIntToJSON(perfectMap));
            data.getActControlData().update();
            ProduceManager.inst().produceAdd(humanObj, confTaskGroup.reward, MoneyItemLogKey.活动完美每轮);
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, confTaskGroup.reward);
            MsgAct.act_task_reward_s2c.Builder msg = MsgAct.act_task_reward_s2c.newBuilder();
            msg.setType(type);
            msg.setTaskId(0);
            msg.setGroupId(groupId);
            msg.addAllRewardList(ProduceManager.inst().to_p_rewardList(confTaskGroup.reward));
            humanObj.sendMsg(msg);
            MsgAct.act_task_update_s2c.Builder msg_update = MsgAct.act_task_update_s2c.newBuilder();
            msg_update.setType(type);
            Define.p_act_task.Builder task = ActivityManager.inst().to_p_act_task_group(groupId, perfectMap);
            msg_update.addTaskList(task);
            humanObj.sendMsg(msg_update);

        }else {
            ActivityTaskVO taskVO = taskVOMap.get(taskId);
            if(taskVO == null) {
                return;
            }
            if(taskVO.getGroupSn() != groupId) {
                return;
            }
            if(taskVO.getStatus() != TaskConditionTypeKey.TASK_STATUS_已完成) {
                return;
            }
            ConfActivityTask confTask = ConfActivityTask.get(taskId);
            if(confTask == null || confTask.reward == null || confTask.reward.length == 0) {
                return;
            }
            taskVO.setStatus(TaskConditionTypeKey.TASK_STATUS_已领取奖励);
            data.saveActivityTask();
            List<Define.p_reward> rewardList = ProduceManager.inst().rewardProduceToMsg(humanObj, confTask.reward, MoneyItemLogKey.活动任务奖励);
            InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewardList);
            MsgAct.act_task_reward_s2c.Builder msg = MsgAct.act_task_reward_s2c.newBuilder();
            msg.setType(type);
            msg.setTaskId(taskId);
            msg.setGroupId(groupId);
            msg.addAllRewardList(rewardList);
            humanObj.sendMsg(msg);
            sendTaskUpdate(humanObj, taskVO);
        }

    }

    protected void sendTaskUpdate(HumanObject humanObj, ActivityTaskVO vo) {
        MsgAct.act_task_update_s2c.Builder msg = MsgAct.act_task_update_s2c.newBuilder();
        msg.setType(type);
        Define.p_act_task.Builder task = Define.p_act_task.newBuilder();
        task.setState(ActivityManager.inst().getTaskState(vo.getStatus()));
        task.setGroupId(vo.getGroupSn());
        task.setTaskId(vo.getTaskSn());
        task.setCount(vo.getPlan());
        msg.addTaskList(task);
        humanObj.sendMsg(msg);
    }

    public void addActivityProgress(HumanObject humanObj, int conditionType, long progress, Object... objs) {
        addActivityProgress(humanObj, conditionType, progress, true, objs);
    }

    public void addActivityProgress(HumanObject humanObj, int conditionType, long progress, boolean isAddProgress, Object... objs) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        if (data.getActControlData().getRound() == 0) {
            return;
        }
        Object[] newObjs = new Object[objs.length+1];
        newObjs[0] = type;
        System.arraycopy(objs, 0, newObjs, 1, objs.length);
        humanObj.operation.taskData.checkTaskPlan(humanObj, TaskConditionTypeKey.TASK_活动, conditionType, newObjs);
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            return;
        }
        if (confTerm.rank_id == 0) {
            return;
        }
        ConfRanktype confRanktype = ConfRanktype.get(confTerm.rank_id);
        if (confRanktype == null) {
            return;
        }
        ConfActivityRankScore confRankScore = getRankScoreConf(data.getActControlData().getRound());
        if (confRankScore == null) {
            return;
        }
        if (confRankScore.condition[0] != conditionType) {
            return;
        }
        if (confRankScore.condition[1] != 0 && confRankScore.condition[1] != Utils.intValue(objs[0])) {
            return;
        }
        int closeTime = data.getActControlData().getCloseTime();
        int serverId = humanObj.getHuman().getServerId();
        if(confRanktype.rank_type ==  RankParamKey.rankTypeGuild){
            serverId = Util.getServerIdReal(serverId);
        }
        String rankKey = RankManager.inst().getRedisRankTypeKey(serverId, confRanktype);
        if (confRanktype.crossType == 0) {
            // 本服排行榜
            if (conditionType == TaskConditionTypeKey.TASK_TYPE_8) {
                RankManager.inst().updateRankScore(humanObj, rankKey, confRanktype, humanObj.getHuman2().getRepSn() - 1);
            } else if(conditionType == TaskConditionTypeKey.TASK_TYPE_2005){
                RankManager.inst().updateRankScore(humanObj, rankKey, confRanktype, progress);
            }else {
                if (isAddProgress) {
                    RankManager.inst().addRankScore(humanObj, rankKey, confRanktype, progress);
                } else {
                    RankManager.inst().updateRankWithTime(humanObj, rankKey, confRanktype, progress);
                }
            }
            // 设置rankKey的过期时间
            RedisTools.ttl(EntityManager.redisClient, rankKey, res -> {
                if (res.failed()) {
                    return;
                }
                Long ttl = res.result();
                if (ttl == null || ttl <= 0) {
                    int expireTime = (int) (closeTime - Port.getTime() / Time.SEC + Tool.WEEK);
                    RedisTools.expire(EntityManager.redisClient, rankKey, expireTime);
                }
            });
        } else {
            // 跨服排行榜，redis榜的过期时间是活动结算的时候再设置过期时间的
            RankManager.inst().updateRankWithTime(humanObj, rankKey, confRanktype, progress);
        }
    }

    private ConfActivityRankScore getRankScoreConf(int round) {
        Collection<ConfActivityRankScore> confs = ConfActivityRankScore.findAll();
        for (ConfActivityRankScore conf : confs) {
            if(type == conf.type && round >= conf.round_range[0] && round <= conf.round_range[1]) {
                return conf;
            }
        }
        return null;
    }

    public void dailyResetActivityData(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null){
            return;
        }
        if(data.getActivityTask().isEmpty()){
            return;
        }
        //初始获得任务数据
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if(confTerm == null) {
            return;
        }
        if(confTerm.task_list == null || confTerm.task_list.length == 0) {
            return;
        }
        Map<Integer,Integer> perfectFlag = Utils.jsonToMapIntInt(data.getActControlData().getPerfectMap());
        for (int i = 0; i < confTerm.task_list.length; i++) {
            ConfActivityTaskGroup confTaskGroup = ConfActivityTaskGroup.get(confTerm.task_list[i]);
            if(confTaskGroup == null || confTaskGroup.task_list == null || confTaskGroup.task_list.length == 0) {
                continue;
            }
            if(confTaskGroup.reset != EActivityType.TASK_RESET_1){
                continue;
            }
            perfectFlag.remove(confTaskGroup.sn);
            for (int j = 0; j < confTaskGroup.task_list.length; j++){
                ConfActivityTask confTask = ConfActivityTask.get(confTaskGroup.task_list[j]);
                if(confTask == null) {
                    Log.game.error("活动任务配置不存在，活动type:{},任务sn:{}", type, confTaskGroup.task_list[j]);
                    continue;
                }
                ActivityTaskVO taskVO = new ActivityTaskVO(data.getActControlData().getActivitySn(), type, confTask, confTaskGroup.sn);
                ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(taskVO.getConditionType());
                if (idata != null) {
                    idata.checkPlan(humanObj, taskVO, 0);
                }
                data.addActivityTask(taskVO,false);
            }
        }
        data.setActivityTask();
        data.getActControlData().setPerfectMap(Utils.mapIntIntToJSON(perfectFlag));
        sendActivityTaskData(humanObj);
    }

    public void on_act_task_update_c2s(HumanObject humanObj) {
        sendActivityTaskData(humanObj);
    }

    public boolean canPay(HumanObject humanObj, ConfPayMall confPayMall) {
        return true;
    }

    @Override
    public void handleEvent(HumanObject humanObj, int event, Param param) {
    }

    public <T extends AbstractControlData> T getControlData(HumanObject humanObj, Class<T> clazz) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("活动{}数据不存在，humanId={}", type, humanObj.getHumanId());
            return null;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("期数表找不到活动{}的期数，humanId={}", type, humanObj.getHumanId());
            return null;
        }
        return data.getControlDataNew(clazz);
    }

}
