package org.gof.demo.worldsrv.activity.calculator;

import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityEffectHelper;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlWuzhiLoveData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfActivityControl;
import org.gof.demo.worldsrv.config.ConfActivityTask;
import org.gof.demo.worldsrv.config.ConfActivityTerm;
import org.gof.demo.worldsrv.config.ConfWuzhiLove;
import org.gof.demo.worldsrv.config.ConfWuzhiLoveLevel;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.task.type.activityData.ActivityTaskVO;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.task.type.ITaskTypeData;
import org.gof.demo.worldsrv.task.type.TaskTypeDataFactory;

import java.util.*;

/**
 * 无职联动-好感度活动控制器
 */
public class ActivityControlWuzhiLove extends AbstractActivityControl {
    
    public ActivityControlWuzhiLove(int type) {
        super(type);
    }

    private final static int EVENT_STATUS_INIT = 1;
    private final static int EVENT_STATUS_PRE_TASK = 2;
    private final static int EVENT_STATUS_MAIN_TASK = 3;

    /**
     * 获取无职好感配置
     */
    private ConfWuzhiLove getWuzhiLoveConf(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return null;
        }
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            return null;
        }

        return ConfWuzhiLove.get(controlData.selectedCharId, type, confTerm.group_id);
    }

    /**
     * 获取礼物的好感度值
     */
    private int getGiftFavorValue(ConfWuzhiLove conf, int itemId) {
        if (conf.gift == null || conf.gift.length == 0) {
            return 0;
        }

        for (int[] giftConfig : conf.gift) {
            if (giftConfig.length >= 2 && giftConfig[0] == itemId) {
                return giftConfig[1]; // 返回好感度值
            }
        }

        return 0; // 不是有效的礼物
    }

    /**
     * 清除前置任务并添加每日任务
     */
    private void clearPreTasksAndAddDailyTasks(HumanObject humanObj, ActivityControlObjectData data) {
        // 获取配置
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动期数配置不存在，type={}, round={}", type, data.getActControlData().getRound());
            return;
        }


        // 清除前置任务
        Map<Integer, ActivityTaskVO> taskMap = data.getActivityTask();
        taskMap.clear();

        // 添加每日任务
        Collection<ConfWuzhiLove> confList = ConfWuzhiLove.findBy(ConfWuzhiLove.K.type, type, ConfWuzhiLove.K.group_id, confTerm.group_id);
        if (confList.isEmpty()) {
            Log.activity.error("无职好感活动配置不存在，type={}, groupId={}", type, confTerm.group_id);
            return;
        }
        for (ConfWuzhiLove conf : confList) {
            addDailyTasksFromConfig(humanObj, data, conf);
        }
        data.setActivityTask();

        // 发送任务更新
        sendActivityTaskData(humanObj);
    }

    public void sendActivityTaskData(HumanObject humanObj){
        MsgAct.act_task_update_s2c.Builder msg = MsgAct.act_task_update_s2c.newBuilder();
        msg.setType(type);
        msg.setIsClear(1);
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        List<Define.p_act_task> taskList = to_p_act_task_list(data);
        msg.addAllTaskList(taskList);
        humanObj.sendMsg(msg);
    }

    /**
     * 从配置添加每日任务
     */
    private void addDailyTasksFromConfig(HumanObject humanObj, ActivityControlObjectData data, ConfWuzhiLove conf) {
        if (conf.daily_task_id == null || conf.daily_task_id.length < 2) {
            Log.activity.error("每日任务配置错误，humanId={}", humanObj.id);
            return;
        }

        // 从配置表获取任务池范围
        int dailyTaskNum = conf.daily_task_num;

        // 生成任务池
        List<Integer> taskPool = new ArrayList<>();
        for (int i = 0; i <= conf.daily_task_id.length; i++) {
            taskPool.add(conf.daily_task_id[i]);
        }

        // 使用当天的时间作为随机种子，确保全服一致
        long currentTime = Port.getTime();
        long startTime = data.getActControlData().getOpenTime() * Time.SEC;
        long dayNum = (currentTime - startTime) / Time.DAY;
        long todayStart = currentTime - dayNum * Time.DAY;
        Random random = new Random(todayStart);

        // 随机选择任务
        Collections.shuffle(taskPool, random);
        List<Integer> selectedTasks = taskPool.subList(0, Math.min(dailyTaskNum, taskPool.size()));

        // 添加任务到通用任务系统
        for (int taskId : selectedTasks) {
            ConfActivityTask confTask = ConfActivityTask.get(taskId);
            if (confTask != null) {
                ActivityTaskVO taskVO = new ActivityTaskVO(data.getActControlData().getActivitySn(), type, confTask, 0);
                ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(taskVO.getConditionType());
                if (idata != null) {
                    idata.checkPlan(humanObj, taskVO, 0);
                }
                data.addActivityTask(taskVO, false);
            }
        }
        Log.activity.info("添加每日任务，humanId={}, selectedTasks={}", humanObj.id, selectedTasks);
    }


    @Override
    protected void initActivityControlData(HumanObject humanObj, ActivityControlObjectData data, ConfActivityControl confControl, ActivityVo vo) {
        // 先调用父类方法初始化通用任务（包括前置任务）
        super.initActivityControlData(humanObj, data, confControl, vo);

        // 获取无职好感配置
        ConfWuzhiLove conf = getWuzhiLoveConf(humanObj);
        if (conf == null) {
            Log.activity.error("无职好感活动配置不存在，type={}, humanId={}", type, humanObj.id);
            return;
        }
        // 前置任务
        if (conf.pre_task != null && conf.pre_task.length > 0) {
            for (int preTaskId : conf.pre_task) {
                ConfActivityTask confTask = ConfActivityTask.get(preTaskId);
                if (confTask != null) {
                    ActivityTaskVO taskVO = new ActivityTaskVO(data.getActControlData().getActivitySn(), type, confTask, 0);
                    ITaskTypeData idata = TaskTypeDataFactory.getTaskTypeData(taskVO.getConditionType());
                    if (idata != null) {
                        idata.checkPlan(humanObj, taskVO, 0);
                    }
                    data.addActivityTask(taskVO, false);
                }
            }
            data.saveActivityTask();
        }
    }

    @Override
    public void dailyResetActivityData(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }

        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            return;
        }

        // 根据活动阶段处理不同的任务
        if (controlData.eventStatus == EVENT_STATUS_PRE_TASK) {
            // 前置任务阶段：调用父类方法处理前置任务重置
            super.dailyResetActivityData(humanObj);
        } else if (controlData.eventStatus == EVENT_STATUS_MAIN_TASK) {
            // 正式活动阶段：清除前置任务，添加每日任务
            clearPreTasksAndAddDailyTasks(humanObj, data);
        }
    }
    
    /**
     * 处理活动信息请求
     */
    public void on_act_wuzhi_love_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求无职好感度活动{}信息时，活动数据不存在", humanObj.id, type);
            return;
        }
        
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的无职好感度活动{}数据为空", humanObj.id, type);
            return;
        }
        
        // 构建响应消息
        MsgAct2.act_wuzhi_love_info_s2c.Builder builder = MsgAct2.act_wuzhi_love_info_s2c.newBuilder();
        builder.setActType(type);
        builder.setEventStatus(controlData.eventStatus);
        builder.setSelectedCharId(controlData.selectedCharId);

        // 添加好感度信息
        for (ControlWuzhiLoveData.FavorInfo favorInfo : controlData.favorInfos.values()) {
            Define.p_favor_info.Builder favorBuilder = Define.p_favor_info.newBuilder();
            favorBuilder.setCharId(favorInfo.charId);
            favorBuilder.setLevel(favorInfo.level);
            favorBuilder.setExp(favorInfo.exp);
            favorBuilder.addAllRewardedLv(favorInfo.rewardedLv);
            builder.addFavorInfos(favorBuilder);
        }

        // 任务信息现在由通用任务系统处理，通过sendActivityTaskData发送

        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 处理选择角色请求
     */
    public void on_act_wuzhi_love_select_char_c2s(HumanObject humanObj, int charId) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}选择角色时，活动数据不存在", humanObj.id);
            return;
        }
        
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动数据为空", humanObj.id);
            return;
        }
        
        // 验证活动状态
        if (controlData.eventStatus != 1) {
            Log.activity.error("玩家{}选择角色时活动状态错误，当前状态:{}", humanObj.id, controlData.eventStatus);
            return;
        }
        
        // 获取配置验证角色ID
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            return;
        }
        ConfWuzhiLove conf = ConfWuzhiLove.get(charId, type, confTerm.group_id);
        if (conf == null) {
            Log.activity.error("无职好感活动配置不存在，humanId={}", humanObj.id);
            return;
        }

        // 验证角色ID是否有效（通过好感等级表验证）
        List<ConfWuzhiLoveLevel> levelConfs = ConfWuzhiLoveLevel.findBy(ConfWuzhiLoveLevel.K.character_id, charId);
        if (levelConfs.isEmpty()) {
            Log.activity.error("无效的角色ID，charId={}, humanId={}", charId, humanObj.id);
            return;
        }

        // 设置选择的角色
        controlData.selectedCharId = charId;
        controlData.eventStatus = 2; // 进入前置任务阶段

        // 初始化该角色的好感度信息
        ControlWuzhiLoveData.FavorInfo favorInfo = new ControlWuzhiLoveData.FavorInfo(charId);
        controlData.setFavorInfo(charId, favorInfo);

        // 发放选择角色奖励
        ProduceManager.inst().produceAdd(humanObj, conf.select_reward, MoneyItemLogKey.无职好感度);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, conf.select_reward);
        // 更新数据
        data.updateControlData();
        
        // 发送响应
        MsgAct2.act_wuzhi_love_select_char_s2c.Builder builder = MsgAct2.act_wuzhi_love_select_char_s2c.newBuilder();
        builder.setActType(type);
        builder.setCharId(charId);
        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 处理合成手办请求
     */
    public void on_act_wuzhi_love_compose_figure_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}合成手办时，活动数据不存在", humanObj.id);
            return;
        }
        
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动数据为空", humanObj.id);
            return;
        }
        
        // 验证活动状态
        if (controlData.eventStatus != 2) {
            Log.activity.error("玩家{}合成手办时活动状态错误，当前状态:{}", humanObj.id, controlData.eventStatus);
            return;
        }
        
        // 获取配置
        ConfWuzhiLove conf = getWuzhiLoveConf(humanObj);
        if (conf == null) {
            Log.activity.error("无职好感活动配置不存在，humanId={}", humanObj.id);
            return;
        }

        // 验证和扣除合成材料
        if(!ProduceManager.inst().checkAndCostItem(humanObj, conf.compose_cost, 1, MoneyItemLogKey.无职好感度).success){
            Log.activity.error("合成消耗不足，humanId={}", humanObj.id);
            return;
        }

        // 发放手办奖励
        int figureId = 0;
        if (conf.compose_reward != null && conf.compose_reward.length > 2) {
            int actType = conf.compose_reward[0];
            figureId = conf.compose_reward[1];
            IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
            if(control == null || !(control instanceof ActivityControlFigureCollection)) {
                Log.activity.error("无职好感度{}找不到对应的手办活动{}", type, actType);
            }else {
                ActivityControlFigureCollection figureControl = (ActivityControlFigureCollection) control;
                figureControl.addFigure(humanObj, figureId);
            }
        }
        
        // 进入正式活动阶段
        controlData.eventStatus = 3;

        // 清除前置任务并添加每日任务
        clearPreTasksAndAddDailyTasks(humanObj, data);
        
        // 更新数据
        data.updateControlData();
        
        // 发送响应
        MsgAct2.act_wuzhi_love_compose_figure_s2c.Builder builder = MsgAct2.act_wuzhi_love_compose_figure_s2c.newBuilder();
        builder.setActType(type);
        builder.setFigureId(figureId);
        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 处理赠送礼物请求
     */
    public void on_act_wuzhi_love_give_gift_c2s(HumanObject humanObj, int charId, List<Define.p_key_value> itemSnNumList) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}赠送礼物时，活动数据不存在", humanObj.id);
            return;
        }
        
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动数据为空", humanObj.id);
            return;
        }
        
        // 验证活动状态
        if (controlData.eventStatus != EVENT_STATUS_MAIN_TASK) {
            Log.activity.error("玩家{}赠送礼物时活动状态错误，当前状态:{}", humanObj.id, controlData.eventStatus);
            return;
        }
        
        // 验证角色ID
        if (charId != controlData.selectedCharId) {
            Log.activity.error("玩家{}赠送礼物时角色ID错误，选择的角色:{}, 请求的角色:{}", humanObj.id, controlData.selectedCharId, charId);
            return;
        }
        
        ControlWuzhiLoveData.FavorInfo favorInfo = controlData.getFavorInfo(charId);
        if (favorInfo == null) {
            Log.activity.error("玩家{}赠送礼物时找不到角色{}的好感度信息", humanObj.id, charId);
            return;
        }
        
        // 获取配置
        ConfWuzhiLove conf = getWuzhiLoveConf(humanObj);
        if (conf == null) {
            Log.activity.error("无职好感活动配置不存在，humanId={}", humanObj.id);
            return;
        }

        int[][] itemSnNumArr = new int[itemSnNumList.size()][2];
        // 计算好感度增加值
        int totalFavorAdd = 0;
        for (Define.p_key_value item : itemSnNumList) {
            int itemId = (int)item.getK();
            int itemNum = (int)item.getV();

            itemSnNumArr[itemSnNumList.indexOf(item)][0] = itemId;
            itemSnNumArr[itemSnNumList.indexOf(item)][1] = itemNum;
            // 从配置表获取礼物的好感度值
            int favorValue = getGiftFavorValue(conf, itemId);
            if (favorValue > 0) {
                totalFavorAdd += itemNum * favorValue;
                Log.activity.info("礼物好感度计算，humanId={}, itemId={}, itemNum={}, favorValue={}",
                    humanObj.id, itemId, itemNum, favorValue);
            } else {
                Log.activity.error("无效的礼物道具，humanId={}, itemId={}", humanObj.id, itemId);
                return;
            }
        }

        if(!ProduceManager.inst().checkAndCostItem(humanObj, itemSnNumArr, 1, MoneyItemLogKey.无职好感度).success){
            Log.activity.error("赠送礼物消耗不足，humanId={}", humanObj.id);
            return;
        }
        // 手办增益
        int bonus = ActivityEffectHelper.getLoveBonus(humanObj, favorInfo.charId);
        totalFavorAdd = (int) (totalFavorAdd * (1 + bonus/100.0f));
        // 增加好感度经验
        favorInfo.exp += totalFavorAdd;
        
        // 检查升级
        checkLevelUp(favorInfo);
        
        // 更新数据
        data.updateControlData();
        
        // 发送响应
        MsgAct2.act_wuzhi_love_give_gift_s2c.Builder builder = MsgAct2.act_wuzhi_love_give_gift_s2c.newBuilder();
        builder.setActType(type);
        builder.setCharId(charId);
        builder.setNewLevel(favorInfo.level);
        builder.setNewExp(favorInfo.exp);
        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 处理领取等级奖励请求
     */
    public void on_act_wuzhi_love_claim_level_reward_c2s(HumanObject humanObj, int charId, int level) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}领取等级奖励时，活动数据不存在", humanObj.id);
            return;
        }
        
        ControlWuzhiLoveData controlData = (ControlWuzhiLoveData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动数据为空", humanObj.id);
            return;
        }
        
        // 验证活动状态
        if (controlData.eventStatus != 3) {
            Log.activity.error("玩家{}领取等级奖励时活动状态错误，当前状态:{}", humanObj.id, controlData.eventStatus);
            return;
        }
        
        ControlWuzhiLoveData.FavorInfo favorInfo = controlData.getFavorInfo(charId);
        if (favorInfo == null) {
            Log.activity.error("玩家{}领取等级奖励时找不到角色{}的好感度信息", humanObj.id, charId);
            return;
        }
        
        // 检查等级是否达到
        if (favorInfo.level < level) {
            Log.activity.error("玩家{}领取等级奖励时等级不足，当前等级:{}, 请求等级:{}", humanObj.id, favorInfo.level, level);
            return;
        }
        
        // 检查是否已领取
        if (favorInfo.rewardedLv.contains(level)) {
            Log.activity.error("玩家{}重复领取等级{}奖励", humanObj.id, level);
            return;
        }
        
        // 发放等级奖励
        ConfWuzhiLoveLevel levelConf = ConfWuzhiLoveLevel.get(charId, level);
        if (levelConf != null && levelConf.reward != null && levelConf.reward.length > 0) {
            for (int[] reward : levelConf.reward) {
                if (reward.length >= 2) {
                    int itemId = reward[0];
                    int itemNum = reward[1];
                    // TODO: 调用道具发放接口
                    Log.activity.info("发放好感等级奖励，humanId={}, charId={}, level={}, itemId={}, itemNum={}",
                        humanObj.id, charId, level, itemId, itemNum);
                }
            }
        }
        
        // 记录已领取
        favorInfo.rewardedLv.add(level);
        
        // 更新数据
        data.updateControlData();
        
        // 发送响应
        MsgAct2.act_wuzhi_love_claim_level_reward_s2c.Builder builder = MsgAct2.act_wuzhi_love_claim_level_reward_s2c.newBuilder();
        builder.setActType(type);
        builder.setCharId(charId);
        builder.setLevel(level);
        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 检查好感度升级
     */
    private void checkLevelUp(ControlWuzhiLoveData.FavorInfo favorInfo) {
        while (true) {
            int expNeeded = getExpNeeded(favorInfo.charId, favorInfo.level);
            if (expNeeded <= 0 || favorInfo.exp < expNeeded) {
                break; // 没有下一级或经验不足
            }

            favorInfo.exp -= expNeeded;
            favorInfo.level++;
            Log.activity.info("好感度升级，charId={}, newLevel={}, remainExp={}",
                favorInfo.charId, favorInfo.level, favorInfo.exp);
        }
    }

    /**
     * 获取指定等级升级所需经验
     */
    private int getExpNeeded(int charId, int level) {
        ConfWuzhiLoveLevel levelConf = ConfWuzhiLoveLevel.get(charId, level);
        if (levelConf != null) {
            return levelConf.need;
        }
        return 0; // 已达到最高等级
    }
}
