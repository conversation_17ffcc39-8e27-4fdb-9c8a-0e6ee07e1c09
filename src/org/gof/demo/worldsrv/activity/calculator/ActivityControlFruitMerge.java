package org.gof.demo.worldsrv.activity.calculator;

import io.vertx.redis.client.RedisAPI;
import org.gof.core.dbsrv.redis.CrossRedis;
import org.gof.core.support.Time;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlFruitMergeData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlWarTokenData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.Utils;
import io.vertx.core.json.JsonArray;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 合成大西瓜活动控制器
 */
public class ActivityControlFruitMerge extends AbstractActivityControl {

    public ActivityControlFruitMerge(int type) {
        super(type);
    }

    /**
     * 获取活动信息
     */
    public void on_act_fruit_merge_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("合成大西瓜活动数据不存在, humanId={}", humanObj.getHumanId());
            return;
        }
        
        ControlFruitMergeData fruitMergeData = (ControlFruitMergeData) data.getControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("找不到活动期数配置, type={}, round={}", type, data.getActControlData().getRound());
            return;
        }

        MsgAct2.act_fruit_merge_info_s2c.Builder builder = fruitMergeData.toProto();
        builder.setActType(type);

        // 获取排名
        if (confTerm.rank_id > 0) {
            ConfRanktype confRanktype = ConfRanktype.get(confTerm.rank_id);
            if (confRanktype != null) {
                String rankKey = RankManager.inst().getRedisRankTypeKey(humanObj.getHuman().getServerId(), confRanktype);
                RankManager.inst().getMyRank(confTerm.rank_id, humanObj.getHuman().getServerId(), humanObj.id, ret -> {
                    int scoreRankSize = 100;
                    if (ret.succeeded()) {
                        int rank = Utils.intValue(ret.result());
                        builder.setRank(rank);
                        scoreRankSize = Math.min(rank, scoreRankSize);
                    }
                    // 获取前100名分数列表
                    getTopScoreList(humanObj, confRanktype, rankKey, builder, scoreRankSize);
                });
                return;
            }
        }
        
        // 没有排行榜配置，直接发送
        humanObj.sendMsg(builder);
    }
    
    /**
     * 获取前100名分数列表
     */
    private void getTopScoreList(HumanObject humanObj, ConfRanktype confRanktype, String rankKey,
                                MsgAct2.act_fruit_merge_info_s2c.Builder builder, int scoreRankSize) {
        // 获取前100名分数列表
        RedisAPI client = confRanktype.crossType > 0 ?
            CrossRedis.getClient() :
            EntityManager.getRedisClient();

        RedisTools.getRankListByIndex(client, rankKey, 0, scoreRankSize-1, false, ret -> {
            List<Integer> scoreList = new ArrayList<>();
            if (ret.succeeded()) {
                JsonArray json = ret.result();
                for (int i = 1; i < json.size(); i += 2) { // 跳过ID，只取分数
                    int score = Utils.intValue(json.getValue(i));
                    scoreList.add(score);
                }
            }
            builder.addAllScoreList(scoreList);
            humanObj.sendMsg(builder);
        });
    }

    /**
     * 结束游戏
     */
    public void on_act_fruit_merge_end_c2s(HumanObject humanObj, int currScore) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("合成大西瓜活动数据不存在, humanId={}", humanObj.getHumanId());
            return;
        }
        
        ControlFruitMergeData fruitMergeData = (ControlFruitMergeData) data.getControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("找不到活动期数配置, type={}, round={}", type, data.getActControlData().getRound());
            return;
        }

        if(currScore > fruitMergeData.maxScore){
            addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2007, currScore, false, new Object[]{type,currScore});
        }

        fruitMergeData.endGame(currScore);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2008, currScore, new Object[]{type,currScore});
        
        // 保存数据
        data.updateControlData();
        
        // 发送响应
        MsgAct2.act_fruit_merge_end_s2c.Builder builder = MsgAct2.act_fruit_merge_end_s2c.newBuilder();
        builder.setActType(type);
        builder.setCurrScore(currScore);
        builder.setTotalScore(fruitMergeData.totalScore);
        humanObj.sendMsg(builder);
    }

    /**
     * 消耗体力
     */
    public void on_act_fruit_merge_stamina_cost_c2s(HumanObject humanObj, int totalCost) {
        Log.activity.info("合成大西瓜消耗体力,totalCost = {}",totalCost);
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("合成大西瓜活动数据不存在, humanId={}", humanObj.getHumanId());
            return;
        }
        
        ControlFruitMergeData fruitMergeData = (ControlFruitMergeData) data.getControlData();
        
        // 计算需要扣除的体力差值
        int currentTotalCost = fruitMergeData.totalCostStamina;
        int costDiff = totalCost - currentTotalCost;
        
        if (costDiff > 0) {
            // 检查体力是否足够
            if (!fruitMergeData.staminaMgr.consumeStamina(costDiff)) {
                Log.activity.error("体力不足, humanId={}, need={}, have={}", 
                    humanObj.getHumanId(), costDiff, fruitMergeData.staminaMgr.getStamina());
                return;
            }
            
            // 更新总消耗体力
            fruitMergeData.totalCostStamina = totalCost;

            // 奖励道具 期数表的String字段parameter2 体力数量|道具ID，数量 每体力数量个活动一次道具id，数量
            ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
            staminaCostReward(humanObj, confTerm, costDiff);

            staminaCostAddWarTokenExp(humanObj, confTerm, costDiff);

            // 保存数据
            data.updateControlData();
//            on_act_stamina_refresh_c2s(humanObj);
            Log.activity.info("合成大西瓜消耗体力, humanId={}, cost={}, totalCost={}",
                humanObj.getHumanId(), costDiff, totalCost);
        }
    }

    private void staminaCostAddWarTokenExp(HumanObject humanObj, ConfActivityTerm confTerm, int costDiff) {
        if(confTerm.parameter > 0){
            IActivityControl control = ActivityControlTypeFactory.getTypeData(confTerm.parameter);
            if(control == null || !(control instanceof ActivityControlWarToken)) {
                Log.activity.error("合成大西瓜活动{}找不到对应的战力{}", type, confTerm.parameter);
            }else {
                ActivityControlWarToken activityControlWarToken = (ActivityControlWarToken) control;
                activityControlWarToken.addWartokenExp(humanObj, costDiff);
            }
        }
    }

    private static void staminaCostReward(HumanObject humanObj, ConfActivityTerm confTerm, int costDiff) {
        if(confTerm.parameter2 != null){
            String[] param = confTerm.parameter2.split("\\|");
            if(param.length >= 2){
                int staminaNum = Utils.intValue(param[0]);
                String[] reward = param[1].split(",");
                if(reward.length >= 2){
                    int itemId = Utils.intValue(reward[0]);
                    int itemNum = Utils.intValue(reward[1]);
                    if(staminaNum > 0 && itemId > 0 && itemNum > 0){
                        int rewardNum = costDiff / staminaNum;
                        if(rewardNum > 0){
                            ProduceManager.inst().produceAdd(humanObj, new int[]{itemId, rewardNum * itemNum}, MoneyItemLogKey.合成大西瓜活动);
                        }
                    }
                }
            }
        }
    }

    /**
     * 使用道具
     */
    public void on_act_fruit_merge_use_item_c2s(HumanObject humanObj, int itemSn) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("合成大西瓜活动数据不存在, humanId={}", humanObj.getHumanId());
            return;
        }

        // 记录道具使用
        ControlFruitMergeData fruitMergeData = (ControlFruitMergeData) data.getControlData();
        fruitMergeData.useItem(itemSn);
        // 保存数据
        data.updateControlData();
        
        // 检查道具是否足够
        if(!ProduceManager.inst().checkAndCostItem(humanObj, itemSn, 1, MoneyItemLogKey.合成大西瓜活动).success){
            Log.activity.error("道具不足, humanId={}, itemSn={}", humanObj.getHumanId(), itemSn);
            return;
        }
        Log.activity.debug("合成大西瓜使用道具, humanId={}, itemSn={}", humanObj.getHumanId(), itemSn);
    }

    /**
     * 上报状态
     */
    public void on_act_fruit_merge_state_c2s(HumanObject humanObj, List<Integer> mergeFruit, 
                                           Define.p_fruit_merge_state state) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("合成大西瓜活动数据不存在, humanId={}", humanObj.getHumanId());
            return;
        }
        
        ControlFruitMergeData fruitMergeData = (ControlFruitMergeData) data.getControlData();
        int newScore = state.getCurrentScore();
        if (newScore > fruitMergeData.maxScore) {
            addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2007, newScore, false, new Object[]{type,newScore});
        }
        
        // 更新游戏状态
        fruitMergeData.updateGameState(state);
        
        // 更新任务进度 - 根据合成的水果更新任务
        Map<Integer, Integer> mergeFruitCount = fruitMergeData.mergeFruitCount;
        if (mergeFruit != null) {
            for (Integer fruitSn : mergeFruit) {
                // 更新合成水果相关的任务进度
                mergeFruitCount.put(fruitSn, mergeFruitCount.getOrDefault(fruitSn, 0) + 1);
                addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2024, 0, false, new Object[]{fruitSn,mergeFruitCount.get(fruitSn)});
            }
        }
        
        // 保存数据
        data.updateControlData();
    }

    /**
     * 体力刷新
     */
    public void on_act_stamina_refresh_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("合成大西瓜活动数据不存在, humanId={}", humanObj.getHumanId());
            return;
        }

        ControlFruitMergeData fruitMergeData = (ControlFruitMergeData) data.getControlData();

        // 发送体力更新消息
        MsgAct2.act_stamina_refresh_s2c.Builder builder = MsgAct2.act_stamina_refresh_s2c.newBuilder();
        int oldStamina = fruitMergeData.staminaMgr.stamina;
        int newStamina = fruitMergeData.staminaMgr.getStamina();
        builder.setActType(type);
        builder.setStamina(newStamina);
        builder.setNextRecoverTime((int)(fruitMergeData.staminaMgr.nextRecoverTime/Time.SEC));

        if (oldStamina != newStamina) {
            data.updateControlData();
        }
        humanObj.sendMsg(builder);
    }

    @Override
    public void itemUse(HumanObject humanObj, int goodsId, int num) {
        // 道具使用逻辑，这里可以是体力道具等
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(type);
        if (controlData == null) {
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, controlData.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动找不到活动期数表ConfActivityTerm, type={}, round={}", type, controlData.getActControlData().getRound());
            return;
        }
        ConfActivityStamina_0 staminaConf = ConfActivityStamina_0.get(type, confTerm.group_id);
        if (staminaConf == null) {
            Log.activity.error("活动体力配置不存在, type={}, groupId={}", type, confTerm.group_id);
            return;
        }
        if (goodsId != staminaConf.stamina_goods[0]) {
            Log.activity.error("活动体力道具不匹配, type={}, groupId={}, goodsId={}, confItem={}", type, confTerm.group_id, goodsId, staminaConf.stamina_goods[0]);
            return;
        }
        num = num * staminaConf.stamina_goods[1];
        ControlFruitMergeData data = (ControlFruitMergeData) controlData.getControlData();
        data.staminaMgr.addStamina(num);
        controlData.updateControlData();

        // 发送体力更新消息
        on_act_stamina_refresh_c2s(humanObj);
    }

    /**
     * 扫荡功能
     */
    public void on_act_fruit_sweep_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("合成大西瓜活动数据不存在, humanId={}", humanObj.getHumanId());
            return;
        }

        ControlFruitMergeData fruitMergeData = (ControlFruitMergeData) data.getControlData();
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("找不到活动期数配置, type={}, round={}", type, data.getActControlData().getRound());
            return;
        }

        //正在游戏中不能扫荡
        if(fruitMergeData.currScore > 0){
            Log.activity.error("正在游戏中不能扫荡, humanId={}", humanObj.getHumanId());
            return;
        }

        // 获取体力配置
        ConfActivityStamina_0 staminaConf = ConfActivityStamina_0.get(type, confTerm.group_id);
        if (staminaConf == null) {
            Log.activity.error("找不到活动体力配置, type={}, groupId={}", type, confTerm.group_id);
            return;
        }

        // 检查param配置：扫荡一次最多消耗多少体力|1点体力获得多少分
        if (staminaConf.param == null || staminaConf.param.length < 2) {
            Log.activity.error("扫荡配置不正确, type={}, groupId={}", type, confTerm.group_id);
            return;
        }

        int maxStaminaCost = staminaConf.param[0]; // 扫荡一次最多消耗多少体力
        int scorePerStamina = staminaConf.param[1]; // 1点体力获得多少分

        // 获取当前体力
        int currentStamina = fruitMergeData.staminaMgr.getStamina();

        // 检查是否有体力
        if (currentStamina <= 0) {
            Log.activity.error("体力不足进行扫荡, humanId={}, currentStamina={}",
                humanObj.getHumanId(), currentStamina);
            return;
        }

        // 计算实际消耗的体力：消耗玩家所有当前体力，如果体力小于扫荡一次最多消耗体力则直接消耗所有体力
        int actualStaminaCost = Math.min(currentStamina, maxStaminaCost);

        // 计算获得的积分：实际消耗体力 * 每点体力获得分数
        int sweepScore = actualStaminaCost * scorePerStamina;

        // 战令加成处理
        ActivityControlObjectData warTokenData = humanObj.operation.activityControlData.getControlObjectData(confTerm.parameter);
        if(warTokenData != null) {
            ControlWarTokenData controlWarTokenData = (ControlWarTokenData) warTokenData.getControlData();
            if(controlWarTokenData.getIsBuyMid() == 1) {
                ConfWartoken confWartoken = ConfWartoken.get(controlWarTokenData.getTokenSn());
                if(confWartoken != null) {
                    ConfPayMall confPayMall = ConfPayMall.get(confWartoken.bundle_id2);
                    if(confPayMall != null && confPayMall.param != null) {
                        String[] param = confPayMall.param.split("\\,");
                        float scoreMultiple = 1+Utils.intValue(param[2])/10000.0f;
                        sweepScore = (int)(sweepScore * scoreMultiple);
                    }
                }
            }
        }

        // 消耗实际体力
        if (!fruitMergeData.staminaMgr.consumeStamina(actualStaminaCost)) {
            Log.activity.error("扣除体力失败, humanId={}, cost={}, have={}",
                humanObj.getHumanId(), actualStaminaCost, currentStamina);
            return;
        }

        // 累计分数
        fruitMergeData.totalScore += sweepScore;

        // 检查是否超过最大局内分数，如果超过则更新排行榜
        boolean updateRank = false;
        if (sweepScore > fruitMergeData.maxScore) {
            fruitMergeData.maxScore = sweepScore;
            updateRank = true;
        }

        // 更新排行榜
        if (updateRank && confTerm.rank_id > 0) {
            ConfRanktype confRanktype = ConfRanktype.get(confTerm.rank_id);
            if (confRanktype != null) {
                String rankKey = RankManager.inst().getRedisRankTypeKey(humanObj.getHuman().getServerId(), confRanktype);
                RankManager.inst().updateRankWithTime(humanObj, rankKey, confRanktype, fruitMergeData.maxScore);
            }
        }

        // 更新任务进度
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_2008, sweepScore, new Object[]{type, sweepScore});

        // 保存数据
        data.updateControlData();
        staminaCostReward(humanObj, confTerm, actualStaminaCost);
        staminaCostAddWarTokenExp(humanObj, confTerm, actualStaminaCost);

        // 获取当前排名
        if (confTerm.rank_id > 0) {
            ConfRanktype confRanktype = ConfRanktype.get(confTerm.rank_id);
            if (confRanktype != null) {
                int finalSweepScore = sweepScore;
                RankManager.inst().getMyRankAndScore(confTerm.rank_id, humanObj.getHuman().getServerId(), humanObj.id, ret -> {
                    int rank = 0;
                    if (ret.succeeded()) {
                        List<String> result = ret.result();
                        if (result != null && result.size() >= 2) {
                            rank = Integer.parseInt(result.get(0));
                        }
                    }

                    // 发送扫荡响应
                    sendSweepResponse(humanObj, actualStaminaCost, finalSweepScore, fruitMergeData, rank);
                });
                return;
            }
        }

        // 没有排行榜配置，直接发送响应
        sendSweepResponse(humanObj, actualStaminaCost, sweepScore, fruitMergeData, 0);
    }

    /**
     * 发送扫荡响应
     */
    private void sendSweepResponse(HumanObject humanObj, int staminaCost, int sweepScore, ControlFruitMergeData fruitMergeData, int rank) {
        MsgAct2.act_fruit_sweep_s2c.Builder builder = MsgAct2.act_fruit_sweep_s2c.newBuilder();
        builder.setActType(type);
        builder.setSweepScore(sweepScore);
        builder.setMaxScore(fruitMergeData.maxScore);
        builder.setRank(rank);
        builder.setTotalScore(fruitMergeData.totalScore);
        builder.setStamina(fruitMergeData.staminaMgr.getStamina());
        builder.setNextRecoverTime((int)(fruitMergeData.staminaMgr.nextRecoverTime / Time.SEC));
        builder.setCostStamina(staminaCost);

        humanObj.sendMsg(builder);
        Log.activity.debug("合成大西瓜扫荡完成, humanId={}, sweepScore={}, maxScore={}, totalScore={}, 剩余体力={}",
            humanObj.getHumanId(), sweepScore, fruitMergeData.maxScore, fruitMergeData.totalScore, fruitMergeData.staminaMgr.getStamina());
    }
}
