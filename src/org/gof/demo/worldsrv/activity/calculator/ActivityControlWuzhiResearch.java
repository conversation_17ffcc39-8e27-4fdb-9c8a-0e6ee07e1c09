package org.gof.demo.worldsrv.activity.calculator;

import java.util.*;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlWuzhiResearchData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

/**
 * 无职研究活动控制类
 */
public class ActivityControlWuzhiResearch extends AbstractActivityControl {
    
    private static ActivityControlWuzhiResearch instance = new ActivityControlWuzhiResearch(0);
    
    public static ActivityControlWuzhiResearch getInstance(int type) {
        instance.type = type;
        return instance;
    }
    
    public ActivityControlWuzhiResearch(int type) {
        super(type);
    }
    
    /**
     * 处理活动信息请求
     */
    public void on_act_wuzhi_research_info_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}信息时，活动数据不存在", humanObj.id, type);
            return;
        }
        
        ControlWuzhiResearchData controlData = (ControlWuzhiResearchData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("发送活动数据玩家{}的活动{}的数据为空", humanObj.id, type);
            return;
        }
        
        // 构建响应消息
        MsgAct2.act_wuzhi_research_info_s2c.Builder builder = MsgAct2.act_wuzhi_research_info_s2c.newBuilder();
        builder.setActType(type);
        builder.setState(controlData.state);
        builder.setLayer(controlData.currentLayer);
        builder.setPrizeCount(controlData.prizeCount);
        builder.setClaimId(controlData.claimId);
        builder.setMode(controlData.mode);
        builder.setAutoAppraisal(controlData.autoAppraisal);
        builder.setAppraisalIndex(controlData.appraisalIndex);
        
        // 添加已开启格子
        for (Map.Entry<Integer, Integer> entry : controlData.openTiles.entrySet()) {
            builder.addOpenTiles(HumanManager.inst().to_p_key_value(entry.getKey(), entry.getValue()));
        }
        
        // 添加鉴定结果
        builder.addAllAppraisalResults(controlData.appraisalResults);
        
        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 处理研究(挖矿)请求
     */
    public void on_act_wuzhi_research_dig_c2s(HumanObject humanObj, int tileId) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}挖矿时，活动数据不存在", humanObj.id, type);
            return;
        }
        
        ControlWuzhiResearchData controlData = (ControlWuzhiResearchData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动{}数据为空", humanObj.id, type);
            return;
        }
        
        // 检查状态
        if (controlData.state != 0) {
            Log.activity.error("玩家{}请求挖矿时，当前状态{}不正确", humanObj.id, controlData.state);
            return;
        }
        
        // 检查格子是否已开启
        if (controlData.openTiles.containsKey(tileId)) {
            Log.activity.error("玩家{}请求挖矿格子{}已开启", humanObj.id, tileId);
            return;
        }
        
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动{}找不到活动期数表ConfActivityTerm", type);
            return;
        }
        
        // 获取配置
        ConfWuzhiResearchConfig config = ConfWuzhiResearchConfig.get(confTerm.group_id);
        if (config == null) {
            Log.activity.error("活动{}找不到配置表，group_id={}", type, confTerm.group_id);
            return;
        }
        
        // 检查并扣除消耗道具
        if (config.cost != null && config.cost.length >= 2) {
            int itemId = config.cost[0];
            int itemCount = config.cost[1];
            if (!ProduceManager.inst().checkAndCostItem(humanObj, itemId, itemCount, MoneyItemLogKey.无职研究).success) {
                Log.activity.error("玩家{}挖矿时道具不足，需要{}个{}", humanObj.id, itemCount, itemId);
                return;
            }
        }
        
        // 检查是否是最后一个格子（保底大奖）
        boolean isLastTile = controlData.isLastTile(config.num);
        
        // 获取奖励池
        List<ConfWuzhiResearchReward> rewardPool = getRewardPool(confTerm.group_id, controlData.currentLayer);
        if (rewardPool.isEmpty()) {
            Log.activity.error("活动{}当前层数{}没有奖励配置", type, controlData.currentLayer);
            return;
        }
        
        // 抽取奖励
        ConfWuzhiResearchReward selectedReward;
        if (isLastTile) {
            // 最后一个格子必定是大奖
            selectedReward = getGrandPrizeFromPool(rewardPool);
        } else {
            selectedReward = getRandomRewardFromPool(rewardPool);
        }
        
        if (selectedReward == null) {
            Log.activity.error("活动{}抽取奖励失败", type);
            return;
        }
        
        // 构建响应消息
        MsgAct2.act_wuzhi_research_dig_s2c.Builder builder = MsgAct2.act_wuzhi_research_dig_s2c.newBuilder();
        builder.setActType(type);
        builder.setTileId(tileId);
        
        if (selectedReward.grand == 0) {
            // 普通奖励
            Map<Integer, Integer> rewardMap = generateRewards(humanObj, selectedReward.output);
            for (Map.Entry<Integer, Integer> entry : rewardMap.entrySet()) {
                builder.addReward(Define.p_reward.newBuilder()
                    .setGtid(entry.getKey())
                    .setNum(entry.getValue()));
            }
            controlData.openTiles.put(tileId, selectedReward.output[0]); // 记录道具ID
        } else {
            // 大奖
            controlData.openTiles.put(tileId, 0); // 大奖标记为0
            controlData.startAppraisalPhase(selectedReward.sn, selectedReward.success);
        }
        
        data.updateControlData();
        humanObj.sendMsg(builder.build());
    }
    
    /**
     * 获取奖励池
     */
    private List<ConfWuzhiResearchReward> getRewardPool(int groupId, int currentLayer) {
        List<ConfWuzhiResearchReward> pool = new ArrayList<>();
        
        for (ConfWuzhiResearchReward reward : ConfWuzhiResearchReward.findAll()) {
            if (reward.research_id == groupId) {
                // 检查层数范围
                if (reward.layer != null && reward.layer.length >= 2) {
                    int minLayer = reward.layer[0];
                    int maxLayer = reward.layer[1];
                    if (currentLayer >= minLayer && currentLayer <= maxLayer) {
                        pool.add(reward);
                    }
                }
            }
        }
        
        return pool;
    }
    
    /**
     * 从奖励池中获取大奖
     */
    private ConfWuzhiResearchReward getGrandPrizeFromPool(List<ConfWuzhiResearchReward> pool) {
        List<ConfWuzhiResearchReward> grandPrizes = new ArrayList<>();
        for (ConfWuzhiResearchReward reward : pool) {
            if (reward.grand == 1) {
                grandPrizes.add(reward);
            }
        }
        
        if (grandPrizes.isEmpty()) {
            return null;
        }
        
        return getRandomRewardFromPool(grandPrizes);
    }
    
    /**
     * 从奖励池中随机抽取奖励
     */
    private ConfWuzhiResearchReward getRandomRewardFromPool(List<ConfWuzhiResearchReward> pool) {
        if (pool.isEmpty()) {
            return null;
        }
        
        // 计算总权重
        int totalWeight = 0;
        for (ConfWuzhiResearchReward reward : pool) {
            totalWeight += reward.weight;
        }
        
        if (totalWeight <= 0) {
            return pool.get(Utils.random(0, pool.size()));
        }
        
        // 加权随机
        int randomValue = Utils.random(1, totalWeight + 1);
        int currentWeight = 0;
        
        for (ConfWuzhiResearchReward reward : pool) {
            currentWeight += reward.weight;
            if (randomValue <= currentWeight) {
                return reward;
            }
        }
        
        return pool.get(pool.size() - 1);
    }
    
    /**
     * 生成奖励
     */
    private Map<Integer, Integer> generateRewards(HumanObject humanObj, int[] output) {
        if (output == null || output.length < 2) {
            return new HashMap<>();
        }
        return ProduceManager.inst().produceAddDrop(humanObj,
            new int[][]{output}, MoneyItemLogKey.无职研究);
    }

    /**
     * 处理灵感鉴定请求
     */
    public void on_act_wuzhi_research_appraise_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}鉴定时，活动数据不存在", humanObj.id, type);
            return;
        }

        ControlWuzhiResearchData controlData = (ControlWuzhiResearchData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动{}数据为空", humanObj.id, type);
            return;
        }

        // 检查状态
        if (controlData.state != 1) {
            Log.activity.error("玩家{}请求鉴定时，当前状态{}不正确", humanObj.id, controlData.state);
            return;
        }

        // 检查鉴定次数
        if (controlData.appraisalIndex >= controlData.appraisalResults.size()) {
            Log.activity.error("玩家{}请求鉴定时，鉴定次数{}超出范围", humanObj.id, controlData.appraisalIndex);
            return;
        }

        controlData.appraisalIndex++;

        // 构建响应消息
        MsgAct2.act_wuzhi_research_appraise_s2c.Builder builder = MsgAct2.act_wuzhi_research_appraise_s2c.newBuilder();
        builder.setActType(type);
        builder.addAllAppraisalResults(controlData.appraisalResults);
        builder.setAppraisalIndex(controlData.appraisalIndex);

        // 如果是第4次鉴定，给予奖励
        if (controlData.appraisalIndex >= 4) {
            ConfWuzhiResearchReward rewardConfig = ConfWuzhiResearchReward.get(controlData.currentGrandRewardSn);
            if (rewardConfig != null) {
                Map<Integer, Integer> rewardMap = generateRewards(humanObj, rewardConfig.output);
                for (Map.Entry<Integer, Integer> entry : rewardMap.entrySet()) {
                    builder.addReward(Define.p_reward.newBuilder()
                        .setGtid(entry.getKey())
                        .setNum(entry.getValue()));
                }

                // 检查是否是完美鉴定（4次全部成功）
                if (controlData.currentSuccessCount == 4 && rewardConfig.grand_reward > 0) {
                    controlData.startDrawPhase(rewardConfig.grand_reward);
                } else {
                    // 完成当前轮次
                    controlData.completeRound();
                }
            }
        }

        data.updateControlData();
        humanObj.sendMsg(builder.build());
    }

    /**
     * 处理摇奖请求
     */
    public void on_act_wuzhi_research_draw_grand_reward_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}摇奖时，活动数据不存在", humanObj.id, type);
            return;
        }

        ControlWuzhiResearchData controlData = (ControlWuzhiResearchData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动{}数据为空", humanObj.id, type);
            return;
        }

        // 检查状态
        if (controlData.state != 2) {
            Log.activity.error("玩家{}请求摇奖时，当前状态{}不正确", humanObj.id, controlData.state);
            return;
        }

        // 构建响应消息
        MsgAct2.act_wuzhi_research_draw_grand_reward_s2c.Builder builder = MsgAct2.act_wuzhi_research_draw_grand_reward_s2c.newBuilder();
        builder.setActType(type);

        // 根据grandRewardId生成摇奖奖励
        if (controlData.grandRewardId > 0) {
            // 这里需要根据grandRewardId从output表中获取奖励
            // 假设grandRewardId就是掉落组ID
            Map<Integer, Integer> rewardMap = ProduceManager.inst().getDropMap(controlData.grandRewardId);
            ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.无职研究);

            for (Map.Entry<Integer, Integer> entry : rewardMap.entrySet()) {
                builder.addReward(Define.p_reward.newBuilder()
                    .setGtid(entry.getKey())
                    .setNum(entry.getValue()));
            }
        }

        // 完成当前轮次
        controlData.completeRound();

        data.updateControlData();
        humanObj.sendMsg(builder.build());
    }

    /**
     * 处理领取累计奖励请求
     */
    public void on_act_wuzhi_research_claim_reward_c2s(HumanObject humanObj, int id) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}领取奖励时，活动数据不存在", humanObj.id, type);
            return;
        }

        ControlWuzhiResearchData controlData = (ControlWuzhiResearchData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动{}数据为空", humanObj.id, type);
            return;
        }

        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动{}找不到活动期数表ConfActivityTerm", type);
            return;
        }

        // 获取累计奖励配置
        ConfWuzhiResearchTimes_0 timesConfig = ConfWuzhiResearchTimes_0.get(id, confTerm.group_id);
        if (timesConfig == null) {
            Log.activity.error("活动{}找不到累计奖励配置，id={}, group_id={}", type, id, confTerm.group_id);
            return;
        }

        // 检查是否已领取
        if (controlData.claimId >= id) {
            Log.activity.error("玩家{}累计奖励{}已领取", humanObj.id, id);
            return;
        }

        // 检查累计次数是否满足
        if (controlData.prizeCount < timesConfig.cumulative_times) {
            Log.activity.error("玩家{}累计大奖次数{}不足，需要{}", humanObj.id, controlData.prizeCount, timesConfig.cumulative_times);
            return;
        }

        // 发放奖励
        if (timesConfig.reward == null || timesConfig.reward.length < 2) {
            Log.activity.error("活动{}累计奖励配置{}奖励为空", type, id);
            return;
        }
        Map<Integer, Integer> rewardMap = ProduceManager.inst().produceAddDrop(humanObj,
                new int[][]{timesConfig.reward}, MoneyItemLogKey.无职研究);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewardMap);

        // 更新已领取ID
        controlData.claimId = id;

        // 构建响应消息
        MsgAct2.act_wuzhi_research_claim_reward_s2c.Builder builder = MsgAct2.act_wuzhi_research_claim_reward_s2c.newBuilder();
        builder.setActType(type);
        builder.setClaimId(controlData.claimId);

        data.updateControlData();
        humanObj.sendMsg(builder.build());
    }

    /**
     * 处理设置一键研究选项请求
     */
    public void on_act_wuzhi_research_set_options_c2s(HumanObject humanObj, int mode, boolean autoAppraisal) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}设置选项时，活动数据不存在", humanObj.id, type);
            return;
        }

        ControlWuzhiResearchData controlData = (ControlWuzhiResearchData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动{}数据为空", humanObj.id, type);
            return;
        }

        // 更新设置
        controlData.mode = mode;
        controlData.autoAppraisal = autoAppraisal;

        data.updateControlData();
    }

    /**
     * 处理一键研究请求
     */
    public void on_act_wuzhi_research_one_click_c2s(HumanObject humanObj, int mode, boolean autoAppraisal) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            Log.activity.error("玩家{}请求活动{}一键研究时，活动数据不存在", humanObj.id, type);
            return;
        }

        ControlWuzhiResearchData controlData = (ControlWuzhiResearchData) data.getControlData();
        if (controlData == null) {
            Log.activity.error("玩家{}的活动{}数据为空", humanObj.id, type);
            return;
        }

        // 检查状态
        if (controlData.state != 0) {
            Log.activity.error("玩家{}请求一键研究时，当前状态{}不正确", humanObj.id, controlData.state);
            return;
        }

        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.activity.error("活动{}找不到活动期数表ConfActivityTerm", type);
            return;
        }

        // 获取配置
        ConfWuzhiResearchConfig config = ConfWuzhiResearchConfig.get(confTerm.group_id);
        if (config == null) {
            Log.activity.error("活动{}找不到配置表，group_id={}", type, confTerm.group_id);
            return;
        }

        // 检查是否可以使用一键研究
        if (!controlData.canUseOneClick(config.auto_need)) {
            Log.activity.error("玩家{}累计大奖次数{}不足，需要{}", humanObj.id, controlData.prizeCount, config.auto_need);
            return;
        }

        // 更新设置
        controlData.mode = mode;
        controlData.autoAppraisal = autoAppraisal;

        // 执行一键研究
        List<Define.p_reward_status_list> rewardsList = new ArrayList<>();
        Map<Integer, Integer> totalRewardsMap = new HashMap<>(); // 统一收集奖励
        boolean stoppedByGrandPrize = false;
        int totalCostCount = 0; // 统一记录消耗数量

        // 计算可用次数
        int itemTotal = ItemManager.inst().getItemNum(humanObj, config.cost[0]);
        int canUseCount = itemTotal / config.cost[1];

        while (true) {
            // 检查道具是否足够
            if (config.cost != null && config.cost.length >= 2) {
                int itemCount = config.cost[1];
                if (totalCostCount + itemCount > itemTotal) {
                    break; // 道具不足，停止
                }
            }

            // 获取下一个格子ID
            int tileId;
            if (mode == 0) {
                tileId = controlData.getNextTileIdSequential(config.num);
            } else {
                tileId = controlData.getNextTileIdRandom(config.num);
            }

            if (tileId == -1) {
                break; // 没有可开启的格子
            }

            // 记录消耗数量（不立即扣除）
            if (config.cost != null && config.cost.length >= 2) {
                totalCostCount += config.cost[1];
            }

            // 获取奖励池并抽取奖励
            List<ConfWuzhiResearchReward> rewardPool = getRewardPool(confTerm.group_id, controlData.currentLayer);
            boolean isLastTile = controlData.isLastTile(config.num);

            ConfWuzhiResearchReward selectedReward;
            if (isLastTile) {
                selectedReward = getGrandPrizeFromPool(rewardPool);
            } else {
                selectedReward = getRandomRewardFromPool(rewardPool);
            }

            if (selectedReward == null) {
                break;
            }

            Define.p_reward_status_list.Builder rewardStatusBuilder = Define.p_reward_status_list.newBuilder();
            rewardStatusBuilder.setStatus(tileId);

            if (selectedReward.grand == 0) {
                // 普通奖励 - 收集到总奖励中
                Map<Integer, Integer> rewardMap = generateRewards(humanObj, selectedReward.output);
                for (Map.Entry<Integer, Integer> entry : rewardMap.entrySet()) {
                    Define.p_reward reward = Define.p_reward.newBuilder()
                        .setGtid(entry.getKey())
                        .setNum(entry.getValue())
                        .build();
                    rewardStatusBuilder.addReward(reward);

                    // 累加到总奖励中
                    totalRewardsMap.put(entry.getKey(),
                        totalRewardsMap.getOrDefault(entry.getKey(), 0) + entry.getValue());
                }
                controlData.openTiles.put(tileId, selectedReward.output[0]);
            } else {
                // 大奖
                controlData.openTiles.put(tileId, 0);
                stoppedByGrandPrize = true;

                // 如果自动鉴定，直接处理鉴定和奖励
                if (autoAppraisal) {
                    Map<Integer, Integer> rewardMap = generateRewards(humanObj, selectedReward.output);
                    for (Map.Entry<Integer, Integer> entry : rewardMap.entrySet()) {
                        // 累加到总奖励中
                        totalRewardsMap.put(entry.getKey(),
                            totalRewardsMap.getOrDefault(entry.getKey(), 0) + entry.getValue());
                    }

                    // 检查是否是完美鉴定
                    if (selectedReward.success == 4 && selectedReward.grand_reward > 0) {
                        Map<Integer, Integer> grandRewardMap = ProduceManager.inst().getDropMap(selectedReward.grand_reward);
                        for (Map.Entry<Integer, Integer> entry : grandRewardMap.entrySet()) {
                            // 累加到总奖励中
                            totalRewardsMap.put(entry.getKey(),
                                totalRewardsMap.getOrDefault(entry.getKey(), 0) + entry.getValue());
                        }
                    }

                    controlData.completeRound();
                } else {
                    controlData.startAppraisalPhase(selectedReward.sn, selectedReward.success);
                }

                rewardsList.add(rewardStatusBuilder.build());
                break; // 挖到大奖，停止
            }

            rewardsList.add(rewardStatusBuilder.build());
        }

        // 统一扣除道具
        if (totalCostCount > 0 && config.cost != null && config.cost.length >= 2) {
            int itemId = config.cost[0];
            ProduceManager.inst().checkAndCostItem(humanObj, itemId, totalCostCount, MoneyItemLogKey.无职研究);
        }

        // 统一给予奖励
        if (!totalRewardsMap.isEmpty()) {
            ProduceManager.inst().produceAdd(humanObj, totalRewardsMap, MoneyItemLogKey.无职研究);
        }

        // 转换总奖励为消息格式
        List<Define.p_reward> totalRewards = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : totalRewardsMap.entrySet()) {
            Define.p_reward reward = Define.p_reward.newBuilder()
                .setGtid(entry.getKey())
                .setNum(entry.getValue())
                .build();
            totalRewards.add(reward);
        }

        // 构建响应消息
        MsgAct2.act_wuzhi_research_one_click_s2c.Builder builder = MsgAct2.act_wuzhi_research_one_click_s2c.newBuilder();
        builder.setActType(type);
        builder.setAutoAppraisal(autoAppraisal);
        builder.addAllRewards(rewardsList);
        builder.addAllReward(totalRewards);
        builder.setStoppedByGrandPrize(stoppedByGrandPrize);

        data.updateControlData();
        humanObj.sendMsg(builder.build());
    }
}
