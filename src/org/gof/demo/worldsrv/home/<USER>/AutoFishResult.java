package org.gof.demo.worldsrv.home.Fish;

import java.util.HashMap;
import java.util.Map;

/**
 * 自动钓鱼结果数据结构
 */
public class AutoFishResult {
    /** 本轮钓鱼次数 */
    public int fishCount;
    
    /** 使用的鱼饵SN */
    public int baitSn;
    
    /** 第一条鱼的SN */
    public int firstFishSn;
    
    /** 第一条鱼的长度 */
    public int firstFishLen;
    
    /** 成功钓到的次数 */
    public int successCount;
    
    /** 逃跑的次数 */
    public int slippedCount;
    
    /** 更新的图鉴鱼类 Map<鱼类组SN, FishDetail> */
    public Map<Integer, FishDetail> albumFishes = new HashMap<>();
    
    /** 卖出的鱼类 Map<鱼类组SN, 数量> */
    public Map<Integer, Integer> sellFishes = new HashMap<>();
    
    /** 获得的奖励 Map<货币ID, 数量> */
    public Map<Integer, Long> rewards = new HashMap<>();

    public Map<Integer, Integer> quitNumMap = new HashMap<>();
    public Map<Integer, Integer> typeNumMap = new HashMap<>();
    public Map<Integer, Integer> gradeNumMap = new HashMap<>();

    /** 实际结算的轮次数（用于记录余数轮次） */
    public int actualRounds;

    public AutoFishResult() {
        this.fishCount = 0;
        this.baitSn = 0;
        this.firstFishSn = 0;
        this.firstFishLen = 0;
        this.successCount = 0;
        this.slippedCount = 0;
        this.actualRounds = 0;
    }

    public AutoFishResult(int fishCount, int baitSn) {
        this();
        this.fishCount = fishCount;
        this.baitSn = baitSn;
    }

    public AutoFishResult(int fishCount, int baitSn, int actualRounds) {
        this();
        this.fishCount = fishCount;
        this.baitSn = baitSn;
        this.actualRounds = actualRounds;
    }
    
    @Override
    public String toString() {
        return "AutoFishResult{" +
                "fishCount=" + fishCount +
                ", baitSn=" + baitSn +
                ", firstFishSn=" + firstFishSn +
                ", firstFishLen=" + firstFishLen +
                ", successCount=" + successCount +
                ", slippedCount=" + slippedCount +
                ", albumFishes=" + albumFishes.size() +
                ", sellFishes=" + sellFishes.size() +
                ", rewards=" + rewards.size() +
                ", actualRounds=" + actualRounds +
                '}';
    }
}
