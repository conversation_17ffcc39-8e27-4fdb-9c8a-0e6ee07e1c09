package org.gof.demo.worldsrv.support.observer;

public final class EventKey {
    public static final int GAME_STARTUP_BEFORE = 0x1001;               //游戏启动前
    public static final int GAME_STARTUP_FINISH = 0x1002;               //游戏启动完毕

    public static final int CALENDAR_START = 0x1003;                    //日程开启
    public static final int CALENDAR_CLOSE = 0x1004;                    //日程结束

    //====跨服相关基础事件====
    //节点注册
    public static final int NODE_REGISTER=0x0101;
    //节点取消注册
    public static final int NODE_UNREGISTER=0x0102;


    /*地图相关事件*/
    public static final int STAGE_REGISTER = 0x2001;                    //地图被注册
    public static final int STAGE_OBJECT_START = 0x2009;                //地图启动
    public static final int STAGE_OBJECT_START_OK = 0x2012;             //地图启动成功


    public static final int HUMAN_UPGRADE = 0x3001;                     //用户升级，每次升级抛出一次，传递开始和结束等级
    public static final int HUMAN_BE_KILLED = 0x3002;                   //用户死亡
    public static final int HUMAN_CHANGE_NAME = 0x3003;                 //用户改名
    public static final int LOOK_AD = 0x3004;                           //看广告

    public static final int PRODUCE_MONEY_CHANGE = 0x3010;              //虚拟币改变

    public static final int UPDATE_ACHIEVE = 0x3500;                    //更新成就
    public static final int ACTIVITY_ADD_PROGRESS = 0x3501;             //活动更新进度
    public static final int ACTIVITY_CLOSE = 0x3502;                    //活动关闭
    public static final int COMMIT_ACHIEVE = 0x3503;                    //提交成就
    public static final int OPEN_ACHIEVE = 0x3504;                      //运营活动开启
    public static final int ACTIVITY_OPEN = 0x3505;                     //活动开始
    public static final int ACTIVITY_END_SHOW = 0x3506;                    //活动展示期结束
    public static final int UPDATE_NEWS = 0x3507;                      // 更新新闻播报

    public static final int PRIVILEGE_CARD_ACTIVE = 0x3600;             //特权卡激活
    public static final int PRIVILEGE_CARD_RENEW = 0x3601;              //特权卡续费

    public static final int HOME_FISHING = 0x3700;                      //钓鱼

    public static final int HUMAN_CREATE = 0x4001;                      //创建角色
    public static final int HUMAN_LOGIN = 0x4002;                       //玩家登录
    public static final int HUMAN_LOGIN_FINISH = 0x4003;                //玩家登录结束，可以开始接收消息。
    public static final int HUMAN_LOGIN_FINISH_FIRST_TODAY = 0x4004;    //玩家今日首次登录结束，可以开始接收消息。
    public static final int HUMAN_LOGOUT = 0x4005;                      //玩家登出游戏

    public static final int PET_CREATE = 0x4006;                        //创建伙伴
    public static final int PET_UPGRADE = 0x4007;                       //伙伴升级

    public static final int UPGRADE_FINISH = 0x4008;                    // 升级完成，这里指的是耗时升级

    public static final int HUMAN_ONLINE_TIME = 0x4015;                  //玩家累加在线奖励
    public static final int PARTNER_DISPATCH = 0x4016;                   //伙伴派遣
    public static final int PARTNER_FORMATION = 0x4017;                  //伙伴阵容改变

	public static final int HUMAN_BUY_MONTH_CARD = 0x4021;			     //玩家购买月卡
	public static final int HUMAN_ALL_LIFE_FIRST_LOGIN = 0x4022;	     //玩家这辈子首次登陆

    public static final int EQUIP_BOX_OPEN_NUM = 0x4030;		         //抽装备（神灯）
	public static final int FINISH_TASK = 0x4031;		                 //完成任务
    public static final int TASK_CONDITION_TYPE = 0x4032;		         //触发任务类型
    public static final int FINISH_QUSET_TO_UNLOCK_ACTIVITY = 0x4050;    //解锁活动相关的任务完成
    public static final int FUNCTION_OPEN = 0x4051;    //功能解锁
    public static final int FUNCTION_OPEN_LOGIN = 0x4052;               // 功能解锁（处理本次登陆时功能判定解锁了，上一个事件是要在游戏过程中产生的）

    public static final int HUMAN_RESET_CHECK = 0x5000;
    public static final int HUMAN_RESET_ZERO = 0x5001;                    //玩家进行零时所需清理
    public static final int HUMAN_RESET_FIVE = 0x5002;                    //玩家进行五时所需清理
    public static final int HUMAN_RESET_12ST = 0x5003;                    //玩家进行12点处理
    public static final int HUMAN_RESET_18ST = 0x5004;                    //玩家进行18点处理
    public static final int HUMAN_RESET_21ST = 0x5005;                    //玩家进行21点处理
    public static final int HUMAN_RESET_6MIN = 0x5006;                    //玩家每6分钟恢复一点活力
    public static final int HUMAN_RESET_WEEK_ZERO = 0x5007;               //玩家每周零时进行处理
    public static final int HUMAN_RESET_WEEK_135 = 0x5008;                //玩家每周一三五零时进行处理


    public static final int HUMAN_RESET_EVERY_HOUR = 0x5009;              //玩家一个小时进行处理
    public static final int HUMAN_RESET_AREAN_WEEK = 0x5010;              //玩家每周重置斗魂场
    public static final int HUMAN_RESET_WEEK_5ST = 0x5017;                //周一5点变更
    public static final int HUMAN_RESET_MONTH_1ST = 0x5018;               //每月一号0点变更0x5018

    public static final int CRON_WEEK_345_hour_22 = 0x5019;                //玩家每周三四五22时进行处理

    public static final int HUMAN_STAGE_ENTER_BEFORE = 0x6001;            //玩家进入地图之前一点点（切换地图时会触发）
    public static final int HUMAN_STAGE_ENTER = 0x6002;                   //玩家进入地图（切换地图时会触发）
    public static final int HUMAN_STAGE_REGISTER = 0x6003;                //玩家注册到场景中的时候
    public static final int HUMAN_STAGE_LEAVE = 0x6004;                   //玩家离开地图
    public static final int HUMAN_ENTER_SCENE_AFTER = 0x6005;             //玩家进入到场景之后触发
    public static final int HUMAN_STAGE_CLIENT_ENTER = 0x6006;            //客户端明确进入到场景了（明确在玩家模型渲染出来了）

    public static final int HUMAN_RETABLISH_CONNECTION = 0x6007;          //重连

    public static final int HUMAN_DATA_MAIN_LOAD_BEGIN = 0x7001;          //用户玩家主要数据加载开始
    public static final int HUMAN_DATA_LOAD_BEGIN = 0x7002;               //用户玩家数据加载开始
    public static final int HUMAN_DATA_LOAD_BEGIN_ONE = 0x7003;        //用户玩家数据加载开始一个
    public static final int HUMAN_DATA_LOAD_FINISH_ONE = 0x7004;        //用户玩家数据加载完成一个
    public static final int FRIEND_DATA_LOAD_BEGIN_ONE = 0x7005;        //好友数据加载开始一个
    public static final int FRIEND_DATA_LOAD_FINISH_ONE = 0x7006;        //好友数据加载完成一个
    public static final int FRIEND_SEND_ACTIVITY_ITEM = 0x7007;         // 好友赠送活动道具

    public static final int HUMAN_PRODUCE_PROP_CHANGE = 0x8001;                    //玩家produce属性变化
    public static final int HUMAN_PRODUCE_PROP_ADD = 0x8002;                        //玩家produce属性增加
    public static final int HUMAN_PRODUCE_PROP_REDUCE = 0x8003;                    //玩家produce属性减少

    public static final int HUMAN_PROP_ = 0x8003;                    //玩家属性计算

    public static final int UNIT_MOVE_START = 0x9001;                    //可移动单元每次开始移动
    public static final int UNIT_HPLOSS = 0x9002;                                //战斗单元受到伤害
    public static final int UNIT_BOSS_HP_LOSS = 0x9003;                                //战斗单元受到伤害
    //	public static final int UNIT_ATTACK = 0x9003;								//战斗单元攻击
//	public static final int UNIT_ACT = 0x9004;									//地图单元有动作，移动，攻击，施法等
    public static final int UNIT_BE_ATTACKED = 0x9005;                    //战斗单元受攻击
    public static final int UNIT_BE_KILLED = 0x9006;                            //战斗单元死亡
    public static final int UNIT_DO_SKILL = 0x9007;                            //释放技能
    public static final int HUMAN_BE_SETSTATE = 0x9008;                 //玩家被设定状态
    public static final int INBORN_CHANGE = 0x9009;                            //天赋更换
    public static final int UNIT_TARGET_HPINFO = 0x9010;                //单元进攻目标血条变化信息
    public static final int HUMAN_REMOVE_SKILL = 0x9011;                //玩家移除技能
    public static final int HUMAN_ADD_SKILL = 0x9012;                    //玩家增加一个技能
    public static final int UNIT_MOVE_LEAVE = 0x9013;                    //可移动目标离开
    public static final int HUMAN_CHANGE_SKILL = 0x9014;                    //玩家更换技能
    public static final int HUMAN_HOME_BUILD_ADD_LV = 0x9015;               //玩家家园建筑升级
    public static final int HUMAN_HOME_SEED = 0x9016;                   //玩家家园播种
    public static final int HUMAN_HOME_PASTURE = 0x9017;                //玩家家园牧场养殖
    public static final int HUMAN_SOUL_VARIATION = 0x9018;                //玩家专精
    public static final int HUMAN_ELF_ADD_LV = 0x9019;                //精灵升级
    public static final int HUMAN_HOME_DORM_ADD_PARTNER = 0x9020;                // 家园入驻伙伴
    public static final int HUMAN_OPEN_SERVER_TIME = 0x9021;                // 根据开服时间完成的任务

    public static final int MONSTER_HPLOSS = 0xA002;                        //怪物受到伤害
    public static final int MONSTER_ATTACK = 0xA004;                        //怪物攻击
    public static final int MONSTER_BE_KILLED_BEFORE = 0xA005;              //怪物被击杀前一刻
    public static final int MONSTER_BE_KILLED = 0xA006;                    //怪物被击杀
    public static final int MONSTER_BORN = 0xA00A;                        //怪物出生

    public static final int MONSTER_IRREGULAR_DEATH = 0xA00D;       //怪物非正常死亡，通常是一个奇遇情况
    public static final int MONSTER_ARRIVED = 0xA00E;                //怪物到达路点
    public static final int MONSTER_CHANGE_PATH = 0xA00F;           //怪物改变路径
    public static final int MONSTER_DIE_SCOPE = 0xA010;       //怪的死亡的范围

    public static final int HUMAN_HPLOSS = 0xB004;                        //玩家受到伤害
    public static final int HUMAN_REVIVE = 0xB00A;                        //玩家复活
    public static final int HUMAN_GETSOUL = 0xB00B;                     //玩家获得武魂
    public static final int HUMAN_HPADD = 0xB00C;                        //玩家被治疗
    public static final int HUMAN_MIRROR_HPLOSS = 0xB00D;               //玩家镜像(机器人)收到伤害

    public static final int ITEM_CHANGE = 0xC001;                    //物品变动
    public static final int ITEM_CHANGE_ADD = 0xC002;                //物品增加
    public static final int ITEM_CHANGE_DEL = 0xC003;                //物品删除
    public static final int ITEM_CHANGE_MOD = 0xC004;                //物品修改
    public static final int ITEM_INIT = 0xC005;                        //物品创建
    public static final int ITEM_USE = 0xC008;                        //物品使用
    public static final int ITEM_BE_USED_SUCCESS = 0xC009;            //物品使用成功
    public static final int ITEM_BAG_ARRANGE = 0xC00A;                //整理背包
    public static final int ITEM_BAG_EXPAND = 0xC00B;                //扩背包
    public static final int ITEM_BAG_SELL = 0xC00C;                    //物品出售
    public static final int ITEM_ADD_NUM = 0xC00D;                     //物品增加带数量

    public static final int POCKET_LINE_HANDLE = 0xD001;                //按模块发送待办事件
    public static final int POCKET_LINE_HANDLE_ONE = 0xD002;            //用户待办事件 = 100;逐条发送
    public static final int POCKET_LINE_HANDLE_END = 0xD003;            //待办事件处理结束

    public static final int SKILL_PASSIVE_CHECK = 0xE001;            //被动技能检查
    public static final int SKILL_UPGRADE = 0xE002;                    //技能升级
    public static final int SKILL_SHAKE_INTERRUPTED = 0xE003;       //前摇被打断
    public static final int SKILL_EVENT_ON_MISS = 0xE004;            // 技能闪避事件

    public static final int INSTANCE_PASS = 0xF001;                    //副本通关
    public static final int GENERAL_RELATION_PASS = 0xF002;         //将星录通关
    public static final int COMPETE_OFFLINE_PASS = 0xF003;        //竞技场通过
    public static final int TOWER_PASS = 0xF004;                //爬塔通过当前层
    public static final int TOWER_ENTER = 0xF005;                //爬塔进入
    public static final int INSTANCE_AUTO_PASS = 0xF006;        //副本扫荡
    public static final int HOME_GUART_SET_TEAM = 0xF007;     //玩家家园设置防守阵型
    public static final int HOME_GUART_SEARTCH_ATTACK = 0xF008;    //家园攻击
    public static final int TEAM_REP_PASS = 0xF009;        //组队副本通过
    public static final int SWITCH_NEWER_REP = 0xF010;        //切换新手副本
    public static final int NPC_REACORD = 0xF011;        //npc挑战
    public static final int TREASURE = 0xF012;        //挖宝

	public static final int MATCH_CANCEL_ALL = 0x16004;         //取消所有匹配
	public static final int MATCH_QUERY_INFO = 0x16005;         //查询匹配信息
	public static final int SWITCH_ROLE = 0x16006;              //切换角色
	
	public static final int GATHER_SUCCESS = 0x17001; //采集成功事件

	public static final int QUESTION_ANSWER_RIGHT = 0x17004; //正确答题
	public static final int QUESTION_ANSWER_WRONG = 0x17005; //错误答题
	public static final int QUESTION_ANSWER = 0x17006; //答题
	public static final int FACTION_BOSS_END = 0x17007; //击杀帮派boss
	public static final int WORLD_BOSS_END = 0x17008; //击杀世界boss

	public static final int AUCTION_SELL_SUCCESS = 0x17010; //拍卖成功
	public static final int ADD_FRIEND = 0x17011; //添加好友
	public static final int MAKE_EQUIP = 0x17012; //打造装备
	public static final int TOWER_ALL_PASS = 0x17013; //通关远征
	public static final int EXPLORE_FINISH = 0x17014; //探索结束
	public static final int WATTING_QUEST = 0x17015; //等待任务等待结束
	public static final int ESCORT_SUCCESS = 0x17016;	 //护送成功
	public static final int TRANSFORM_END = 0x17017;	 //变身结束
    public static final int HUMAN_COMBAT_CHANGE = 0x10001;                //玩家战斗力变化

    public static final int COMPETE_OFFLINE_RANK_CHANGE = 0x11001;        // 离线竞技排行变化
    public static final int COMPETE_RESET = 0x11002;                        // 竞技场整点结算
    public static final int ARENA_POINT_CHANGE = 0x11003;

    public static final int USUAL_PROMPT_TASK_INIT = 0x12001;                //待做事项初始化
    public static final int USUAL_PROMPT_TASK_CHANGE = 0x12002;        //待做事项改变


    public static final int COLLEGE_QUESTIONNAIRE =  0x20001; // 师徒问卷

    public static final int LOTTERY_SELECT = 0x13001;            //抽卡
    public static final int MONEY_TREE_SHARK = 0x13002;    //摇钱树

    public static final int BUY_ACT = 0x13004;        // 购买体力

    public static final int EXPLOREVAL_UPDATE = 0x13005; //探索值改变

    public static final int BODY_ITEM_CHANGE = 0x14001;                    //穿戴物品变化
    public static final int BODY_PART_QIANGHUA = 0x14002;                //装备位强化
    public static final int BODY_PART_CHONGXING = 0x14003;                //装备位冲星
    public static final int BODY_GEM_CHANGE = 0x14004;                    //宝石变动
    public static final int BODY_GEM_COMPOSITE = 0x14005;                //宝石合成
    public static final int BODY_PART_CHONGXING_SUCCESS = 0x14006;        //冲星成功
    public static final int SOULRING_TRAIN = 0x14008;             //武魂培养
    public static final int DROP_NOT_IN_BAG_ITEM = 0x14010;          //获得一个不进包的收集道具
    public static final int BODY_PART_RWCAST = 0x14011;                //装备位重铸
    public static final int BODY_PART_STAR_ADD_LV = 0x14012;           //装备升星

    public static final int ARTIFACT_PRACTIVE = 0x14311;                //绝学修炼
    public static final int ARTIFACT_BREAK = 0x14312;                    //绝学突破


    //开始场景触发器定义
    public static final int SCENE_TRIGGER_01 = 0x15001;        //移动
    public static final int SCENE_TRIGGER_02 = 0x15002;        //怪物剩余血量
    public static final int SCENE_TRIGGER_03 = 0x15003;        //怪物死亡
    public static final int SCENE_TRIGGER_04 = 0x15004;        //释放技能触发
    public static final int SCENE_TRIGGER_05 = 0x15005;        //敌对目标剩余数量
    public static final int SCENE_TRIGGER_06 = 0x15006;        //副本倒计时
    public static final int SCENE_TRIGGER_07 = 0x15007;        //进入副本
    public static final int SCENE_TRIGGER_08 = 0x15008;        //完成事件
    public static final int SCENE_TRIGGER_09 = 0x15009;        //前端触发节拍器
    public static final int SCENE_TRIGGER_10 = 0x15010;        //怪物到达指定地点
    public static final int SCENE_TRIGGER_11 = 0x15011;        //吸收魂环完成
    public static final int SCENE_TRIGGER_12 = 0x15012;        //采集物被采集
    public static final int SCENE_TRIGGER_13 = 0x15013;        //怪物存活时间
    public static final int SCENE_TRIGGER_14 = 0x15014;        //指定单位杀死
    public static final int SCENE_TRIGGER_15 = 0x15015;        //释放了某个技能
    public static final int SCENE_TRIGGER_16 = 0x15016;        //玩家人数检测
    public static final int SCENE_TRIGGER_17 = 0x15017;        //buff检测加次数
    public static final int SCENE_TRIGGER_18 = 0x15018;        //怪物死亡次数
    public static final int SCENE_TRIGGER_19 = 0x15019;        //当某一个组计时器数量大于X
    public static final int SCENE_TRIGGER_20 = 0x15020;        //指定检测摆放表sn身上buff多少层
    public static final int SCENE_TRIGGER_21 = 0x15022;        //指定技能释放结束，指定技能，指定摆放表（0=所有玩家都达成,怪物则单个），指定BUFF，指定层数

    //开始场景事件定义
    public static final int SCENE_EVENT_05 = 0x15025;        //[S]刷怪
    public static final int SCENE_EVENT_06 = 0x15026;        //[S]技能
    public static final int SCENE_EVENT_07 = 0x15027;        //[S]机关
    public static final int SCENE_EVENT_09 = 0x15029;        //[S]道具
    public static final int SCENE_EVENT_10 = 0x15030;        //[S]通知
    public static final int SCENE_EVENT_11 = 0x15031;        //[S]挑战结果
    public static final int SCENE_EVENT_14 = 0x15034;        //[S]触发死亡
    public static final int SCENE_EVENT_15 = 0x15035;        //[S]暂停计时
    public static final int SCENE_EVENT_18 = 0x15036;        //[S]杀死指定的怪
    public static final int SCENE_EVENT_17 = 0x15037;        //[S]杀死指定的怪
    public static final int SCENE_EVENT_21 = 0x15038;        //[S]触发传送阵
    public static final int SCENE_EVENT_23 = 0x15039;        //[S]触发消除某组怪物的buff

    public static final int SCENE_EVENT_24 = 0x15040;        //[S]给主角加buff
    public static final int SCENE_EVENT_25 = 0x15041;        //[S]刷一个采集精灵出来
    public static final int SCENE_EVENT_26 = 0x15042;        //[S]强制设置玩家位置
    public static final int SCENE_EVENT_30 = 0x15043;        //[S]修改怪物路点
    public static final int SCENE_EVENT_34 = 0x15044;        //[S]开启或者关闭AI
    public static final int SCENE_EVENT_40 = 0x15045;        //[s]给主角加怒气
    public static final int SCENE_EVENT_41 = 0x15046;        //[s]产生一个npc
    public static final int SCENE_EVENT_42 = 0x15047;        //押镖副本生成怪物，给马车加定身buff
    public static final int SCENE_EVENT_43 = 0x15048;        //退出宗门或踢出宗门
    public static final int SCENE_EVENT_43_ANSWER = 0x15049;    //[s]怪物ui说话（答题）
    public static final int SCENE_EVENT_46 = 0x15050;        //[s]改变怪物技能组
    public static final int SCENE_EVENT_47 = 0x15051;        //[s]改变怪物ai
    public static final int SCENE_EVENT_48 = 0x15052;        //[s]赠送礼物
    public static final int SCENE_EVENT_49 = 0x15053;        //[s]场景关闭
    public static final int SCENE_EVENT_50 = 0x15054;        //[s]指定位置刷怪事件
    public static final int SCENE_EVENT_51 = 0x15055;        //[s]指定位置刷采集物事件
    public static final int SCENE_EVENT_52 = 0x15056;        //[s]开启某一组计时器达到X秒
    public static final int SCENE_EVENT_53 = 0x15057;        //[s]关闭某一组计时器


    public static final int TEAM_QUEST_CYCLE_ADD = 0x17018;        //组队副本的队伍环数+1
    public static final int QUEST_CYCLE_LISTEN = 0x17019;            //单人跑环事件监听
    public static final int MIRROR_INSTANCE_SUCCESS = 0x17020;    //镜像副本结束
    public static final int TEAM_CHANGE = 0X17021;//队伍改变
    public static final int ARENA_END = 0x17023; //pvp战场结束

    public static final int BOSSEILTE_CONSUMESTRENGTH = 0x17027;//

    public static final int GUILD_GVE_CACHE_HUMANDATA = 0x18001;// 家族胖头鱼通知玩家去缓存数据


    public static final int FASHION_ACTIVE = 0x30001;             // 时装激活（包含直接购买和物品直接获得）
    public static final int FASHION_OUTDATE = 0x30002;            // 时装过期

    public static final int SOULBONE_EQUIP_UPDATE = 0x40001;            // 魂骨装备更新（装备、卸下、切换方案触发）


    public static final int GM = 0x150001;//GM命令
    public static final int PAY = 0x150002;//充值
    public static final int PAY_NOTIFY = 0x150003;//充值通知


    public static final int CrossStartupBefore = 0x310001;            //跨服游戏启动前
    public static final int CrossStartupFinish = 0x310002;                //跨服游戏启动完毕
    public static final int CombatantLogout = 0x310003;                            //玩家登出游戏
    public static final int CombatantStageEnterBefore = 0x310004;    //玩家进入地图之前一点点（切换地图时会触发）
    public static final int CombatantStageEnter = 0x310005;                    //玩家进入地图（切换地图时会触发）
    public static final int CombatantLogin = 0x310006;                                //玩家登录
    public static final int CombatantStageLeave = 0x310005;                    //玩家离开地图（切换地图时会触发）
    public static final int PLAYER_GROUP_EVENTS = 0x310007;                    //玩家组队事件(结算触发)

    public static final int HUMAN_ALL_BROADCAST = 0x310008;                    //播报事件(达成特定条件触发)
    public static final int ACTIVITY_BROADCAST_SCHEDULE = 0x310009; //活动定时播报事件

    //跨服服务事件
    public static final int CROSS_STARTUP_BEFORE = 0x410001;            //跨服游戏启动前
    public static final int CROSS_STARTUP_FINISH = 0x410002;                //跨服游戏启动完毕
    public static final int COMBATANT_LOGOUT = 0x410003;                            //玩家登出游戏
    public static final int COMBATANT_STAGE_ENTER_BEFORE = 0x410004;    //玩家进入地图之前一点点（切换地图时会触发）
    public static final int COMBATANT_STAGE_ENTER = 0x410005;                    //玩家进入地图（切换地图时会触发）
    public static final int COMBATANT_LOGIN = 0x410006;                                //玩家登录


}