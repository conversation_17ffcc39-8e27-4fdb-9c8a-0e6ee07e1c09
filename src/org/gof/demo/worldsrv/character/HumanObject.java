package org.gof.demo.worldsrv.character;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.Message;
import com.google.protobuf.Message.Builder;
import com.pwrd.op.LogOp;
import com.pwrd.op.LogOpChannel;
import org.gof.core.*;
import org.gof.core.connsrv.ConnectionProxy;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.support.*;
import org.gof.demo.battlesrv.msgHandler.SkillManager;
import org.gof.demo.battlesrv.stageObj.CharacterObject;
import org.gof.demo.battlesrv.stageObj.MoveSyncChecker;
import org.gof.demo.battlesrv.stageObj.UnitObject;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.battlesrv.support.PropKey;
import org.gof.demo.battlesrv.support.UnitObjectStateKey;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.support.PeriodChecker;
import org.gof.demo.support.PeriodExecutor;
import org.gof.demo.support.statistics.StatisticsHuman;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.activity.EActivityType;
import org.gof.demo.worldsrv.activity.calculator.IActivityControl;
import org.gof.demo.worldsrv.angel.AngelManager;
import org.gof.demo.worldsrv.appearance.AppearanceManager;
import org.gof.demo.worldsrv.artifact.ArtifactData;
import org.gof.demo.worldsrv.camp.CampTypeKey;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.Currency;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.equip.EquipInfo;
import org.gof.demo.worldsrv.equip.EquipManager;
import org.gof.demo.worldsrv.fate.FateData;
import org.gof.demo.worldsrv.fate.FateManager;
import org.gof.demo.worldsrv.flyPet.FlyPetManager;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.guild.GuildParamKey;
import org.gof.demo.worldsrv.guild.HumanBriefVO;
import org.gof.demo.worldsrv.home.FarmData;
import org.gof.demo.worldsrv.home.HomeManager;
import org.gof.demo.worldsrv.human.*;
import org.gof.demo.worldsrv.inform.ErrorTip;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.mall.MallManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgIds;
import org.gof.demo.worldsrv.pet.PetManager;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.relic.RelicManager;
import org.gof.demo.worldsrv.relic.ReliceData;
import org.gof.demo.worldsrv.stage.StageHistory;
import org.gof.demo.worldsrv.stage.StageObject;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.*;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventBridgeKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.treasureSkin.TreasureSkinInfo;
import org.gof.demo.worldsrv.wing.WingManager;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 角色
 */
@DistrClass
public class HumanObject extends CharacterObject {

	private long tmCreate; 	//产生时候的时间
	private long tmNow; 	//当前的时候  使用timeCrete +  pulse 的时间

	private int tmDelta; 	//变化的时间
	public static int CLOSE_DELAY = 10;
	public CallPoint connPoint = new CallPoint(); // 连接点信息
	public int loadingNum = 0; // 正在加载玩家数据时的计数器 当等于0时代表加载完毕
	public long loadingPID = 0; // 正在加载玩家数据时的请求ID


	public int cheatCount = 0;
	// 人物持久化信息
	protected long closeTime = 0L;

	// 客户端地图状态已准备完毕
	public boolean isClientStageReady;
	// 正在切换地图中
	private boolean isStageSwitching = false;
	// 玩家登陆状态判断 临时属性 0=无状态 1=登陆中 2=今日首次登陆中
	public int loginStageState;
	// 记录玩家的属性变化，并在本次心跳结束后发送变化至客户端
//	private HumanInfoChange humanInfoChange;
	// 是否本心跳监控玩家属性变化
	private boolean isHumanInfoListen;


	// 整点检测（随机延迟避免同时导致卡顿）
	public TickTimer hourTickTimer = null;

	public TickTimer checkMailFive = new TickTimer(5*Time.MIN);

	public TickTimer checkPayGiftFiveSec = new TickTimer(5*Time.SEC);

	public TickTimer checkGuildBattleMin = new TickTimer(Time.MIN);

	public TickTimer checkHumanDataTT = new TickTimer(10 * Time.SEC);

	// 玩家充值礼包计时器
	public TickTimer payGiftTickTimer = null;

	// 是否是登录（切地图）
	public boolean isLogin = false;
	public int isOnline = 0;

	// 日本那边的平台id(每次登录可能都不一样)
	public String platformId = "";


	public Map<String, PayGift> payGiftMap = new HashMap<>();// 充值礼包表

	// 货币数据
	public Currency currency;

	public int seasonNow = 0;// 竞技场排位赛赛季

	/* 聊天 */
	public long informLastSayTime; // 最后一次发言时间
	public Map<Integer, Long> lastSayTimeMap = new HashMap<>();

	/** 业务功能 */
	public HumanOperationPersistance operation = new HumanOperationPersistance();

	/* 是否是镜像 */
	public boolean isMirror = false;

	public boolean isPayGiftRemove = true;

	/** 是否首次创角登录, 0不是，1是*/
	public int isCreateLogin = 0;

	//队长名字
	public String leaderName;
	//队长id
	public long leaderId;
	//上一张地图的数据信息
	private HumanStageInfoVO lastInfo = null;

	/** 做消息冷却， msgid, 下次可以发的时间戳 */
	public Map<Integer, Long> msgIdCDMap = new HashMap<>();
	private Map<Integer, Long> cdMap = new HashMap<>();

	/** ios切出时间 */
	public long iosQuitTime = 0;

	public int languageType = 0;
	private String regional = ConfExchangeRate.K.JPY;

	public String account = "";

	public boolean isLoginOk = false;
	public boolean isTakAllOk = false;

	//用于记录登陆时human数据模块是否加载完成
	public volatile transient AtomicInteger loginLoadPart=new AtomicInteger(0);

	/**
	 * 不持久化 连杀
	 */
	public int continueKillNum = 0;
	public int continueAssist = 0;

	public int camp = 1;
	//登录状态：0无状态  1登录状态  2今日首次登录
	public int humanLoginStage = 0;
	public HumanGlobalInfo hgInfo;

	public String clientIP = "";

	//玩家所在物理服务器ID 当合服后这个属性与human.serverId可能不同
	public int realServerId;
	//玩家所在服务器开服时间
	public long serverOpenTime;
	//玩家所在服务器合服时间
	public long serverMergeTime;

	public int guildLv = 0;
	public int guildPosition = GuildParamKey.position_5;
	public String guildGvgShow = "";
	// 副本类型，进入副本时间
	public Map<Integer, Long> repTypeEnterTimeMap = new HashMap<>();

	// 已经执行过的待办集合
	public Map<Long, Long> pocketIdMap = new HashMap<>();

	public MoveSyncChecker moveChecker = new MoveSyncChecker();
	public HumanReset humanReset;

	private PeriodChecker periodChecker = new PeriodChecker();

	public List<Integer> funcOpenList = new ArrayList<>();
	public List<Integer> skillOpenPosList = new ArrayList<>();

	public List<Integer> petOpenPosList = new ArrayList<>();

	public Set<Integer> openActivitySnList = new HashSet<>();
	public Set<Integer> openHumanActivitySnList = new HashSet<>();
	public Set<Integer> showActivitySnList = new HashSet<>();
	public Set<Integer> openActivityFunSnList = new HashSet<>();

	public Map<Integer, JSONObject> arenaRankRobotMap = new HashMap<>();

	public boolean isArenaBridge = false;
	public boolean loginArenaTime = false;// 本次登录是否挑战过

	public boolean isLeagueBridge = false;// 先默认是联盟服

	public boolean isSendGvgMsg = true;

	public int syncArenaCdEndTime = 0;// 同步竞技场cd时间
	public long enterTypeTime = 0;// 是否发送主线副本结算(进入其他副本类型后10秒内不发)

	public int check20secNum = 0;

	public TickTimer check20sec = new TickTimer(12 * Time.SEC);

	public int checkReConnNum = 0;

	public TickTimer syncSec = new TickTimer(6 * Time.SEC);

	public long idGen = 0;

	public boolean isCombatUp = false;

	public long updateArenaSyncTime = 0;
	public long updateArenaCrossSyncTime = 0;

	/**
	 * 强制复活计时器
	 */
	private TickTimer timerReviveForce = new TickTimer();

	/**
	 * 对应强制复活的类型
	 */
	private int reviveForceType = 0;

	/**
	 * 组队自动战斗设定（单次），和setting内是互斥的
	 */
	private boolean autoTeamSetting = false;

	/**
	 * 切换地图的时候来源。用于判断buff的条件
	 */
	private int switchFrom;

	public boolean isRepLog = false;

	/**
	 * 临时变量，只有本地图有效(在此时间内如果收到stop，则认为可以信任，只信任一次。信任距离不可过大)
	 */
	private long stopByClient;

	public long lastOpTime = 0;

	public long combatId = 0;
	public long combatForId = 0;
	public int combatType = 0;//打架的类型
	public long combatParam = 0;//打架的参数
	public long combatSeed = 0;//随机种子
	public long combatMaxHp = 0;//战斗中对方最大血量
	public HumanBriefVO combatBriefVo = null;
	public HumanBrief combatBrief = null;

	/**
	 * 每日重置记录和记录转换的对象映射
	 */
	private HumanDailyResetTime resetRecord;
	private Map<Integer, HumanDailyResetInfo> resetInfoMap = new HashMap<>();

	public ArtifactData artifact = new ArtifactData();
	public FateData fate = new FateData();
	public ReliceData relic = new ReliceData();
	public FarmData farmData = new FarmData();
	private TickTimer checkActivityTime = new TickTimer(Time.MIN);

	// 武道会当前阶段组
	public int currentKungFuRaceStageGroup = 0;
	// 武道会登录时的赛季
	public int kungFuRaceLoginSeason;
    // 乱斗32强当前阶段组
    public int currentFamily32StageGroup = 0;
    // 乱斗32强登录时的赛季
    public int family32LoginSeason;
	// 已经通知删除的AI礼包payId
	private Set<String> payGiftDelSet = new HashSet<>();
	// 皮肤过期检测
	private TickTimer checkSkinOutTime = new TickTimer(1 * Time.MIN);

	/**
	 * 构造函数
	 */
	public HumanObject() {
		super(null);
		initPeriodChecker();
	}

	public String getPortId() {
		return Port.getCurrent().getId();
	}
	public Port getPortNow() {
		return Port.getCurrent();
	}


	@Override
	public void writeTo(OutputStream out) throws IOException {
		super.writeTo(out);
		out.write(tmCreate);
		out.write(tmNow);
		out.write(tmDelta);

		out.write(dataPers);
		out.write(connPoint);
		out.write(isStageSwitching);
		out.write(operation);

		out.write(informLastSayTime);
		out.write(switchFrom);
		out.write(closeTime);
		out.write(lastInfo);

		out.write(ragePart);
		out.write(camp);
		out.write(clientIP);

		out.write(humanLoginStage);

		out.write(realServerId);

		out.write(serverOpenTime);
		out.write(serverMergeTime);
		out.write(guildLv);
		out.write(guildPosition);
		out.write(skillPassiveEffectVO);
		out.write(isLogin);
		out.write(isOnline);
		out.write(leaderName);
		out.write(leaderId);
		out.write(currency);
		out.write(periodChecker);
		out.write(loginStageState);
		out.write(isSpectators);

		out.write(platformId);
		out.write(languageType);
		out.write(account);
		out.write(resetRecord);
		out.write(hgInfo);
		out.write(humanReset);

		out.write(pocketIdMap);
		out.write(payGiftMap);
		out.write(resetInfoMap);
		out.write(artifact);
		out.write(fate);
		out.write(relic);
		out.write(repTypeEnterTimeMap);
		out.write(funcOpenList);
		out.write(skillOpenPosList);
		out.write(petOpenPosList);
		out.write(isCreateLogin);
		out.write(openActivitySnList);
		out.write(openHumanActivitySnList);
		out.write(openActivityFunSnList);
		out.write(isLoginOk);
		out.write(idGen);
		out.write(treasureSkinMap);
		out.write(gift26ExpireMap);
		out.write(gift26DrawNumMap);
		out.write(seasonNow);
		out.write(currentKungFuRaceStageGroup);
		out.write(kungFuRaceLoginSeason);
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		super.readFrom(in);
		tmCreate = in.read();
		tmNow = in.read();
		tmDelta = in.read();
		dataPers = in.read();
		connPoint = in.read();
		isStageSwitching = in.read();
		operation = in.read();

		informLastSayTime = in.read();
		switchFrom = in.read();
		closeTime = in.read();
		lastInfo =  in.read();


		ragePart = in.read();
		camp = in.read();
		clientIP = in.read();


		humanLoginStage = in.read();

		realServerId = in.read();

		serverOpenTime = in.read();
		serverMergeTime = in.read();
		guildLv = in.read();
		guildPosition = in.read();

		skillPassiveEffectVO = in.read();
		isLogin = in.read();
		isOnline = in.read();

		leaderName = in.read();
		leaderId = in.read();

		currency = in.read();

		periodChecker = in.read();
		loginStageState = in.read();

		rebindCheckFunc();

		isSpectators = in.read();

		platformId = in.read();
		languageType = in.read();
		account = in.read();

		resetRecord = in.read();

		hgInfo = in.read();

		humanReset = in.read();

		pocketIdMap = in.read();
		payGiftMap = in.read();
		resetInfoMap.putAll(in.read());
		artifact = in.read();
		fate = in.read();
		relic = in.read();
		repTypeEnterTimeMap = in.read();
		funcOpenList = in.read();
		skillOpenPosList = in.read();
		petOpenPosList = in.read();
		isCreateLogin = in.read();
		openActivitySnList = in.read();
		openHumanActivitySnList = in.read();
		openActivityFunSnList = in.read();
		isLoginOk = in.read();
		idGen = in.read();
		treasureSkinMap = in.read();
		gift26ExpireMap = in.read();
		gift26DrawNumMap = in.read();
		seasonNow=in.read();
		currentKungFuRaceStageGroup = in.read();
		kungFuRaceLoginSeason = in.read();
	}


	public void loginOut(){

	}


	@Override
	public void die(UnitObject killer, Param params) {
		super.die(killer, params);
		this.continueKillNum = 0;
		this.continueAssist = 0;
		this.setInFighting(false);
	}

	public Human getHuman() {
		return (Human) dataPers.unit;
	}

	public Human2 getHuman2() {
		return operation.human2;
	}
	public Human3 getHuman3() {
		return operation.human3;
	}

	public void setTeamBundleId(long teamId){
		this.teamBundleID = teamId;
		if(!(dataPers.unit instanceof Human)) {
			return;
		}

		Human2 human2 = getHuman2();
		long oldTeamId = human2.getTeamId();
		if(teamId == human2.getTeamId()) {
			return;
		}

		human2.setTeamId(teamId);

		if(!(this instanceof HumanObject)){
			return;
		}

		HumanObject humanObj = this.getHumanObj();
		if(humanObj.isMirror){
			return;
		}

		if (teamId != oldTeamId){//队伍ID改变才发布事件
			Event.fire(EventKey.TEAM_CHANGE, "humanObj", this, "teamId", teamId, "oldTeamId", oldTeamId);
		}
	}
	private void initPeriodChecker() {
		periodChecker.scheduleCheck("oneSecCheck", this::oneSecCheck, Time.SEC);
		periodChecker.scheduleCheck("onlineTimePlus", this::onlineTimePlus, 5 * Time.MIN);
		periodChecker.scheduleCheck("periodCheckSync", this::periodCheckSync, 1 * Time.MIN);
	}

	// 反序列化后重新绑定周期检测函数
	private void rebindCheckFunc() {
		for (PeriodExecutor executor : periodChecker.getExecutors()) {
			switch (executor.methodName) {
				case "oneSecCheck":
					executor.setFunction(this::oneSecCheck);
					break;
				case "onlineTimePlus":
					executor.setFunction(this::onlineTimePlus);
					break;
				case "periodCheckSync":
					executor.setFunction(this::periodCheckSync);
					break;
				case PeriodKey.guildBossRecover:
					executor.setFunction(this::periodCheckGuildRecover);
					break;
				case PeriodKey.periodCheckItemRecover:
					executor.setFunction(this::periodCheckItemRecover);
					break;
				case PeriodKey.periodCheckScience:
					executor.setFunction(this::periodCheckScience);
					break;
				case PeriodKey.periodCheckEquipBox:
					executor.setFunction(this::periodCheckEquipBox);
					break;
				case PeriodKey.periodCheckPersonality:
					executor.setFunction(this::periodCheckPersonality);
					break;
				default:
					break;
			}
		}
	}

	private void oneSecCheck() {
//		periodCheckQuest();
	}

	private void periodCheckQuest() {

	}

	public void scheduleCheckWaitQuest(long interval) {
		periodChecker.scheduleCheck("periodCheckWaitQuest", this::periodCheckWaitQuest, interval).setTimes(1);
	}

	public void stopCheckDelegate() {
		periodChecker.cancelSchedule("periodCheckDelegate");
	}


	private void periodCheckWaitQuest() {
		Event.fire(EventKey.WATTING_QUEST, "humanObj", this);
	}

	private void periodCheckRecover() {
		Param param = periodChecker.getParamByMethodName("recover");
		if (!param.containsKey("recover")) {
			return;
		}

		int recover = param.getInt("recover");
	}

	private void periodCheckGuildRecover() {
		Param param = periodChecker.getParamByMethodName(PeriodKey.guildBossRecover);
		if (!param.containsKey("recover")) {
			return;
		}
		int recover = param.getInt("recover");
		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.league_solo_chapter_recover.SN);
		if (confGlobal == null) {
			Log.game.error("ConfGlobal配置不存在，无法进行公会Boss检查");
			return;
		}

		int maxNum = confGlobal.intArray[1];
		Human3 human3 = getHuman3();
		long time = human3.getGuildBossTime();
		int currNum = human3.getGuildBossNum();
		int addNum = 0;
		if (time <= Port.getTime() && currNum < maxNum) {
			// 物品恢复的时刻小于当前时刻，那就要考虑恢复
			addNum = Math.min(recover, maxNum - currNum);
			ProduceManager.inst().produceAdd(this, GuildParamKey.guildBoss_7002, addNum, MoneyItemLogKey.公会Boss挑战道具恢复);
			currNum = ItemManager.inst().getItemNum(this, GuildParamKey.guildBoss_7002);
			human3.setGuildBossNum(currNum);
		}

		if (currNum >= maxNum) {
			// 超过最大，那就不用在挂定时器
			time = Port.getTime();
			human3.setGuildBossNum(maxNum);
			periodChecker.cancelSchedule(PeriodKey.guildBossRecover);
		} else {
			// 计算下次恢复时刻
			time += addNum * confGlobal.intArray[0] * Time.SEC;
		}
		human3.setGuildBossTime(time);
		human3.update();
		InstanceManager.inst().sendMsg_dungeon_update_s2c(this, InstanceConstants.LEAGUESOLOCHAPTER_6);
	}

	public void scheduleUseRecoverItem(int interval, int recover, int times) {
		periodChecker.scheduleCheckImmediatly("periodCheckRecover", this::periodCheckRecover, interval).setTimes(times).addParam("recover", recover);
	}

	public void scheduleGuildBossRecover(long interval, int recover, long timeStart) {
		periodChecker.scheduleCheckImmediatly(PeriodKey.guildBossRecover, this::periodCheckGuildRecover, interval, timeStart, false).addParam("recover", recover);
	}

	/**
	 * 道具恢复
	 * @param interval
	 * @param itemSn
	 */
	public void startCheckItemRecover(long interval, int itemSn) {
		PeriodExecutor executor = periodChecker.getExecutor(PeriodKey.periodCheckItemRecover, "sn", itemSn);
		if(executor!=null){
			executor.setTimes(executor.times+1);
			executor.tickTimer.setTimeNext(Port.getTime()+interval);
			return;
		}
		if(interval <= 0){
			Log.temp.error("=============> interval<=0, itemSn={}", interval, itemSn);
			return;
		}
		periodChecker.scheduleCheckImmediatly(PeriodKey.periodCheckItemRecover, this::periodCheckItemRecover, interval, false).setTimes(1).addParam("sn", itemSn);
	}

	private void periodCheckItemRecover(Object obj) {
		Param param = (Param)obj;
		int itemSn = param.get("sn");
		ConfGoodsRefresh conf = ConfGoodsRefresh.get(itemSn);
		if(conf == null || conf.type != ItemConstants.AUTO_TYPE_TIMER){
			Log.game.error("periodCheckItemRecover:道具不存在，道具SN={}", itemSn);
			return;
		}
		int itemMax = ItemManager.inst().getRefreshGoodMaxNum(this, conf);
		int itemNum = ItemManager.inst().getItemNum(this, itemSn);
		if(itemNum >= itemMax){
			return;
		}
		ProduceManager.inst().produceAdd(this,itemSn,1, MoneyItemLogKey.道具恢复);
		int recoverTime = getRecoverTime(conf);
		ItemManager.inst().setRefreshGoodNextTime(this, itemSn,(int)(Port.getTime()/Time.SEC)+recoverTime);
		ItemManager.inst().checkAndRecoverRefreshGood(this, conf);
        HumanManager.inst().sendMsg_role_goods_refresh_list_s2c(this);
	}

	public int getRecoverTime(ConfGoodsRefresh conf){
		if(conf.sn == ItemConstants.AUTO_MINE){
			int attr = getPropPlus().getBigDecimal(PropKey.pickaxe_speed.getAttributeSn()).intValue();;
			return (int)(conf.time / (1 + attr / 10000.0));
		}
		return conf.time;
	}

	public int getRecoverTime(int itemSn){
		ConfGoodsRefresh conf = ConfGoodsRefresh.get(itemSn);
        if(conf == null){
            return 0;
        }
        return getRecoverTime(conf);
	}

	public void stopCheckItemRecover(int itemSn) {
		periodChecker.cancelSchedule(PeriodKey.periodCheckItemRecover,"sn", itemSn);
	}

	/**
	 * 科技完成定时器
	 * @param interval
	 */
	public void startCheckScience(long interval) {
		periodChecker.scheduleCheckImmediatly(PeriodKey.periodCheckScience, this::periodCheckScience, interval, false).setTimes(1);
	}
	public void periodCheckScience() {
		//科技完成
		HomeManager.inst().scienceFinish(this);
	}

	/**
	 * 宝箱完成定时器
	 * @param interval
	 */
	public void startCheckEquipBox(long interval) {
		periodChecker.scheduleCheckImmediatly(PeriodKey.periodCheckEquipBox, this::periodCheckEquipBox, interval, false).setTimes(1);
	}
	public void periodCheckEquipBox() {
		//科技完成
		EquipManager.inst().equipBoxUpgradeFinish(this);
	}

	/**
	 * 称号，头像框，气泡到期定时器
	 * @param interval
	 * @param type
	 */
	public void startCheckPersonality(long interval, int sn, int type){
		periodChecker.scheduleCheckImmediatly(PeriodKey.periodCheckPersonality, this::periodCheckPersonality, interval, false).setTimes(1).addParam("sn", sn).addParam("type", type);
	}
	public void periodCheckPersonality(Object obj) {
		//称号，头像框，气泡到期
		Param param = (Param)obj;
		AppearanceManager.inst().personalityFinish(this,param.get("sn"),param.get("type"));
	}

	private void periodCheckSkill() {
	}

	private void periodCheckSync() {
		HumanGlobalServiceProxy prx = HumanGlobalServiceProxy.newInstance();
		prx.syncInfoTime(id);
	}

	/**
	 * 定时器加速
	 * @param time 毫秒
	 */
	public void periodCheckerSpeedUp(String method, long time, Param param) {
		periodChecker.speedUp(method, time, param);
	}

	/**
	 * 玩家在线时间累积
	 * @param
	 */
	private void onlineTimePlus() {
		Human3 human = getHuman3();

		//本次累计增加秒数
		long secPlus = 5 * 60 * 1000 / Time.SEC;
		//单位 秒
		human.setTimeSecOnline((int)(human.getTimeSecOnline() + secPlus));

	}

	/**
	 * 执行心跳，由service服务调用
	 * <AUTHOR>
	 * @Date 2024/6/26
	 * @Param
	 */
	public void pulse() {
		long timeNow = Port.getTime();
		tmDelta += (int) (timeNow - tmNow);
		tmNow = timeNow;
		if (this.tmDelta >= 33) {
			// 玩家心跳取整
			pulse(tmDelta);
			tmDelta = 0;
		}
	}

	@Override
	public void pulse(int deltaTime) {
		// 先执行通用操作
		super.pulse(deltaTime);

		if(closeTime > 0 && closeTime < tmNow || check20secNum >= 3){
			connCloseClear();
		}
		//玩家信息变更，同步HumanGlobalInfo
		if(closeTime >= 0 && (HumanManager.inst().humanChange(this) || syncSec.isPeriod(tmNow))){
			// -1 代表下线不同步
			HumanManager.inst().syncHumanGlobal(this);
		}
		// 客户端是否已完成玩家的加载并登陆到地图前 不做任何操作
		if (!isClientStageReady) {
			return;
		}

		// 发送玩家本身属性更新信息至客户端
		if (isHumanInfoListen) {
			isHumanInfoListen = false;
//			humanInfoChange.resultForListen(this);
		}

		periodChecker.check(tmNow);

		if(hourTickTimer != null && hourTickTimer.isPeriod(tmNow)){
			hourTickTimer.stop();
//			QuestManager.inst().checkAcceptLimitQuest(this);
		}

		if(checkMailFive.isPeriod(tmNow)){
			HumanManager.inst().checkCrossSyns(this, HumanBrief.K.topCombat);
			checkMailFive();
		}
		if(isSendGvgMsg && checkGuildBattleMin.isPeriod(tmNow)){
			GuildManager.inst().checkGuildBattleMin(this);
		}
		if (checkSkinOutTime.isPeriod(Port.getTime())) {
			HumanManager.inst().checkAndDelOutTimeSkin(this);
		}


		if(closeTime >= 0 && check20sec.isPeriod(tmNow)){
			ConnectionProxy prx = ConnectionProxy.newInstance(connPoint.nodeId,connPoint.portId, connPoint.servId);
			prx.checkConn();
			prx.listenResult(this::result_checkConn);
		}

		if(checkPayGiftFiveSec.isPeriod(tmNow)){
			checkPayGift();
		}
		if(checkHumanDataTT.isPeriod(tmNow)){
			if(operation.taskData != null && !operation.taskData.isNull()){
				operation.taskData.saveTask();
			}
		}

	}

	/**
	 * 玩家挂钩的活动判断是否开启
	 */
	public void checkHumanActivityOpen(){
		List<Integer> confList = GlobalConfVal.getActivityType4List();
		for(int sn : confList){
			if(openHumanActivitySnList.contains(sn)){
				continue;
			}
			ConfActivityControl conf = ConfActivityControl.get(sn);
			if(conf == null){
				continue;
			}
			int actState = timeType_4(conf);
			if(actState == EActivityType.STATE_OPEN){
				openHumanActivitySnList.add(conf.sn);
				openActivitySnList.add(conf.sn);
				IActivityControl iActivityControl =  ActivityControlTypeFactory.getTypeData(conf.type);
				if(iActivityControl != null){
					int[] timeArr =getOpenHumanActivityTime(conf);
					iActivityControl.initActivityData(this, conf, new ActivityVo(conf.sn, timeArr[0], timeArr[1], conf.sn, conf.showTime));
					iActivityControl.sendActivityData(this);
				}
				MallManager.inst().sendActivityPayMallUpdate(this, conf.type);
			}else if(actState == EActivityType.STATE_ENDSHOW){
				showActivitySnList.add(conf.sn);
			}
		}
	}


	/**
	 * 检查创角活动状态
	 * @param conf 活动配置
	 * @return 活动状态：0-无状态，1-准备中，2-进行中，3-结束展示中
	 */
	private int timeType_4(ConfActivityControl conf) {
		if (conf.timeType != ParamKey.activityTimeType_4) {
			return EActivityType.STATE_NULL;
		}

		String[] timeArr = Utils.splitStr(conf.time, "\\|");
		if (timeArr == null || timeArr.length < 2) {
			Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
			return EActivityType.STATE_NULL;
		}

		int openDay = Utils.intValue(timeArr[0]);
		int closeDay = Utils.intValue(timeArr[1]);
		long timeNow = Port.getTime();
		long createTime = getHuman().getTimeCreate();
		long openTime = Utils.getOffDayTime(createTime, openDay - 1, 0);
		long closeTime = Utils.getOffDayTime(createTime, closeDay, 0) - Time.SEC;

		if (openTime >= closeTime) {
			Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
			return EActivityType.STATE_NULL;
		}

		// 活动还未开始，处于准备中状态
		if (timeNow < openTime) {
			return EActivityType.STATE_NULL;
		}

		// 活动正在进行中
		if (timeNow >= openTime && timeNow < closeTime) {
			return EActivityType.STATE_OPEN;
		}

		// 活动已结束，检查是否在展示期
		if (timeNow >= closeTime && conf.showTime > 0) {
			long showEndTime = closeTime + conf.showTime * Time.SEC;
			if (timeNow < showEndTime) {
				return EActivityType.STATE_ENDSHOW;
			}
		}

		// 活动已完全结束
		return EActivityType.STATE_NULL;
	}

	private int[] getOpenHumanActivityTime(ConfActivityControl conf){
		String[] timeArr = Utils.splitStr(conf.time, "\\|");
		if(timeArr == null || timeArr.length < 2){
			Log.temp.error("===ConfActivityControl配表错误，time={}, timeType={}, sn={}", conf.time, conf.timeType, conf.sn);
			return new int[]{0,0};
		}
		int openDay = Utils.intValue(timeArr[0]);
		int closeDay = Utils.intValue(timeArr[1]);
		long timeNow = Port.getTime();
		long createTime = getHuman().getTimeCreate();
		int openTime = (int) (Utils.getOffDayTime(createTime, openDay - 1, 0) / Time.SEC);
		int closeTime = (int)((Utils.getOffDayTime(createTime, closeDay, 0) - Time.SEC) / Time.SEC);
		return new int[]{openTime,closeTime};
	}

	private void result_checkConn(boolean timeout, Param results, Param context){
		if(timeout){
			check20secNum++;
			Inform.sendInform(id, 99);
			return;
		}
		check20secNum = 0;
	}

	public void checkOpenGuildBoss(){
		Human3 human = getHuman3();
		ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.league_solo_chapter_recover.SN);
		if (confGlobal == null) {
			Log.temp.error("ConfGlobal配置不存在，无法进行公会Boss检查");
			return;
		}
		long recoverCd = confGlobal.intArray[0] * Time.SEC;
		int maxNum = confGlobal.intArray[1];
		int currnum = this.operation.itemData.getItemNum(GuildParamKey.guildBoss_7002);
		int addNum = 0;
		long lastRecover = human.getGuildBossTime();
		while (Port.getTime() >= lastRecover && (currnum + addNum) < maxNum) {
			lastRecover += recoverCd;
			addNum++;
		}
		if (addNum > 0) {
			ProduceManager.inst().produceAdd(this, GuildParamKey.guildBoss_7002, addNum, MoneyItemLogKey.公会Boss挑战道具恢复);
			human.setGuildBossNum(ItemManager.inst().getItemNum(this, GuildParamKey.guildBoss_7002));
			human.setGuildBossTime(lastRecover);
			human.update();
		}
		if (getHuman2().getGuildId() <= 0 || human.getGuildBossNum() >= maxNum || currnum >= maxNum) {
			return;
		}

		scheduleGuildBossRecover(confGlobal.intArray[0] * Time.SEC, 1, lastRecover - recoverCd);
	}

	@Override
	public void startup() {
		super.startup();
		tmCreate = Port.getTime(); 	// 玩家本次登录时间
		tmNow = tmCreate;
		tmDelta = 0;
	}


	/**
	 * 发送消息至玩家
	 *
	 * @param builder
	 */
	public void sendMsg(Builder builder) {
 		if (builder == null)
			return;
		if(isMirror){
			//机器人没有连接，不发消息
			builder = null;
			return;
		}
		// 检测账号，不发消息
		if(getHuman().isCheckRobot()){
			builder = null;
			return;
		}
		if(!isLoginOk && MsgIds.getIdByClass(builder.build().getClass()) == MsgIds.role_login_s2c){
			isLoginOk = true;
		}
		if(!isLoginOk){
			builder = null;
			return;
		}
		sendMsg(builder.build());
	}

	public void sendMsg(List<Builder> builders) {
		if (builders == null || builders.size() == 0)
			return;

		List<Integer> idList = new ArrayList<Integer>();
		List<Chunk> chunkList = new ArrayList<Chunk>();
		for (Builder builder : builders) {
			Message msg = builder.build();
			idList.add(MsgIds.getIdByClass(msg.getClass()));
			chunkList.add(new Chunk(msg));
		}
		ConnectionProxy prx = ConnectionProxy.newInstance(connPoint.nodeId,
				connPoint.portId, connPoint.servId);
		prx.sendMsg(idList, chunkList);
		// stopMsgTimer();
	}

	public void sendMsg(List<Integer> idList, List<Chunk> chunkList) {
		if (idList == null || chunkList == null || idList.size() == 0)
			return;

		if(idList.size() != chunkList.size()){
			Log.temp.error("===发消息出问题了， humanId={}, idList={}, chunkList={}", id, idList, chunkList);
		}
		// 检测账号，不发消息
		if(getHuman().isCheckRobot()){
			return;
		}
		ConnectionProxy prx = ConnectionProxy.newInstance(connPoint.nodeId,connPoint.portId, connPoint.servId);
		prx.sendMsg(idList, chunkList);

	}

	/**
	 * 发送消息至玩家
	 *
	 * @param msg
	 */
	public void sendMsg(Message msg) {
//		Log.human.info("msg={}", msg);
		if (msg == null)
			return;

		if(getHuman().isCheckRobot()){
			Log.temp.info("===检测的玩家不需要通知，{}", id);
			msg = null;
			return;
		}
		String msgName = msg.getClass().getName();
		if((msgName.contains("dungeon_dc_1"))||(msgName.contains("wuzhi"))||msgName.contains("fruit")||msgName.contains("cohesionaa")){
			Log.game.info("===发送消息给玩家，{}，{}，{}", msgName.substring(msgName.lastIndexOf("$") + 1),msg, getHuman().getId());
		}
		// 玩家连接信息
		ConnectionProxy prx = ConnectionProxy.newInstance(connPoint.nodeId,connPoint.portId, connPoint.servId);
		int msgId = MsgIds.getIdByClass(msg.getClass());
		prx.sendMsg(msgId, new Chunk(msg));
		// stopMsgTimer();
	}

	/**
	 * 将玩家注册到地图中 暂不显示
	 *
	 * @param stageObj
	 */
	public void stageRegister(StageObject stageObj) {
		// 断线重连等情况下，会出现注册玩家前，可能会残留之前的数据
		// 这里调用数据的原坐标，能避免坐标差距，造成客户端人物位置改变。
		HumanObject humanObjOld = stageObj.getHumanObj(id);
		if (humanObjOld != null && !humanObjOld.getPosNow().equals(getPosNow())) {
			setPosNow(humanObjOld.getPosNow());
		}

		// 调用父类实现
		super.stageRegister(stageObj);

		// 玩家成功注册到某个地图中
		Event.fire(EventKey.HUMAN_STAGE_REGISTER, "humanObj", this);
		if (S.isBridge) {
			Event.fire(EventBridgeKey.BRIDGE_HUMAN_STAGE_REGISTER, "humanObj", this);
		}
		HumanStatusManager.inst().afterSwitch(this, stageObj.sn);
	}

	public void connDelayCloseClear() {
		// 处理延迟关于 启动关闭后XX 秒才会彻底清除玩家数据
//		m_closeTimer.start(CLOSE_DELAY * Time.SEC);
		long confTime = 600 * Time.SEC;
		long time = Port.getTime() + confTime;
		long delayIosTime = 5 * Time.MIN;
		if(iosQuitTime > 0){
			time = iosQuitTime + delayIosTime;
		}
		// 默认延迟
		closeTime = time + CLOSE_DELAY * Time.SEC;

		// 更新退出时间
		getHuman().setTimeLogout(Port.getTime());
		getHuman().update();
		setTableAllRedis(this);
	}


	/**
	 * 临时的，后续
	 * <AUTHOR>
	 * @Date 2024/6/19
	 * @Param
	 */
	private void setTableAllRedis(HumanObject humanObj){
		humanObj.operation.skillData.saveData(humanObj);
		humanObj.operation.petData.saveData(humanObj);
		humanObj.getHuman().update(true);
		humanObj.getHuman2().update(true);
		humanObj.operation.itemData.saveItemAll();
		dataPers.unitPropPlus.unitPropPlus.update(true);

		if(Config.DATA_DEBUG){
			Log.temp.info("humanId={}, equipBox={}", humanObj.id, EquipInfo.toMapJsonString(humanObj.operation.equipBoxMap));
		}
		humanObj.getHumanExtInfo().setEquipInfo(EquipInfo.toMapJsonString(humanObj.operation.equipBoxMap));
		humanObj.getHumanExtInfo().setMainRepMonsterIdListMap(Utils.mapIntListLongToJSON(humanObj.operation.mainRepKillIdMap));
		humanObj.getHumanExtInfo().update(true);

		Human3 human3 = humanObj.getHuman3();
		human3.setEmojiSnExpiredMap(Utils.mapIntLongToJSON(humanObj.operation.emojiSnTimeMap));
		human3.setEmojiSnList(Utils.listToString(new ArrayList<>(new HashSet<>(humanObj.operation.emojiSnList))));
		human3.setRepTypeNumMap(Utils.mapIntIntToJSON(humanObj.operation.repTypeNumMap));
		human3.update(true);


		humanObj.humanReset.saveHumanSubjoinInfo();
		humanObj.currency.update(true);
		for(Buff buff : dataPers.buffs.values()){
			buff.update(true);
		}

		for(PayLog payLogs : dataPers.payLogs){
			payLogs.update(true);
		}
		humanObj.operation.profession.update(true);
		for (Equip equip : humanObj.operation.equipsMap.values()){
			equip.update(true);
		}
		humanObj.operation.taskData.saveTask();
		if(humanObj.operation.friend != null){
			humanObj.operation.friend.update(true);
		}
		if(humanObj.operation.mall != null){
			humanObj.operation.mall.update(true);
		}
		if(humanObj.artifact.atf != null){
			humanObj.artifact.atf.update(true);
		}
		if(humanObj.operation.mount != null){
			humanObj.operation.mount.update(true);
		}

		if(humanObj.operation.wing != null){
			humanObj.operation.wing.update(true);
		}

		if(humanObj.fate.fate != null){
			humanObj.fate.fate.update(true);
		}
		if(humanObj.relic.relic != null) {
			humanObj.relic.relic.update(true);
		}
		if(humanObj.operation.mine != null){
			humanObj.operation.mine.update(true);
		}
		if(humanObj.operation.capture != null){
			humanObj.operation.capture.update(true);
		}
		if(humanObj.operation.privilege != null){
			humanObj.operation.privilege.update(true);
		}

		for( PayGift payGift : humanObj.payGiftMap.values()){
			payGift.update(true);
		}
		if(humanObj.operation.clientInfo != null){
			humanObj.operation.clientInfo.update(true);
		}

		if(humanObj.resetRecord != null){
			humanObj.resetRecord.update(true);
		}
		for(Mail mail : humanObj.operation.mailMap.values()){
			mail.update(true);
		}
		humanObj.operation.activityControlData.updateAll();
		if(humanObj.operation.carPark2 != null){
			humanObj.operation.carPark2.update(true);
		}
		// 美观值
		humanObj.operation.charmData.saveData();
		HumanManager.inst().updateHumanBrief(humanObj, true);
		//保存双人本行装0数据
		if(humanObj.operation.doubleChapter != null){
			humanObj.operation.doubleChapter.setBattleRole(to_p_battle_role(0).toByteArray());
		}
		Log.temp.info("===保存redis数据结束");

	}

	/**
	 * 玩家下线时进行清理
	 * @param
	 */
	public void connCloseClear() {
		Human human = this.getHuman();
		check20secNum = 0;
		closeTime = -1;

		if(bridge){
			Log.temp.info("===humanId={}", id);
		}
		setTableAllRedis(this);
		logLogout(this);
		//如果是在跨服服务器上，需要清理原本world服上面的玩家数据
		if (S.isBridge) {
			//发布跨服退出事件
			Event.fire(EventBridgeKey.BRIDGE_HUMAN_LOGOUT, "humanObj", this);
		} else {
			//发布游戏服退出事件
			Event.fire(EventKey.HUMAN_LOGOUT, "humanObj", this);

			if (bridge) {
				//踢出玩家
				HumanGlobalServiceProxy hgPrx = HumanGlobalServiceProxy.newInstance(NodeAdapter.bridge());
				hgPrx.kick(id, ErrorTip.SystemDefault);

				HumanGlobalServiceProxy hgPrxLeauge = HumanGlobalServiceProxy.newInstance(NodeAdapter.bridge(true));
				hgPrxLeauge.kick(id, ErrorTip.SystemDefault);
			}
		}
		// 清理
		HumanGlobalServiceProxy hgsprx = HumanGlobalServiceProxy.newInstance();
		hgsprx.cancel(id);
		if(human.isCheckRobot()){
			Log.temp.info("===检测玩家不需要记录登出,{}", human.getId());
			return;
		}
		StatisticsHuman.logout();

	}


	public void logLogout(HumanObject humanObj) {
		Human human = humanObj.getHuman();
		Human2 human2 = humanObj.getHuman2();
		Human3 human3 = humanObj.getHuman3();
		// 添加登出日志
		int questSn = 0;
		if(this.operation.taskData != null){
			for(int taskSn : this.operation.taskData.mainTaskMap.keySet()){
				questSn = taskSn;
			}
		}
		String positionName = human2.getPositionName().replaceAll("\\|", "");
		LogOp.log(LogOpChannel.LOGOUT, human.getId(),
				Port.getTime(),
				human.getAccount(),
				human.getName(),
				human.getLevel(),
				Utils.formatTime(Port.getTime(), "yyyy-MM-dd"),
				human3.getTimeSecOnline(),
				questSn,
				"",
				this.getPosNow().x + "," + this.getPosNow().y,
				"",
				"",
				"",
				human.getServerId(),

				human2.getGuildName(),
				positionName,
				new BigDecimal(human.getCombat()).longValue(),
				"",
				"",
				"",
				false,
				false,
				0,
				0,
				0,
				humanObj.operation.profession.getJobSn()
		);
	}
	/**
	 * 保存表格
	 * <AUTHOR>
	 * @Date 2024/3/6
	 * @Param
	 */
	private void tableflush(HumanObject humanObj){
		humanObj.operation.petData.saveData(humanObj);
		humanObj.operation.skillData.saveData(humanObj);

		humanObj.operation.itemData.saveItemAll();
		DB prxItem = DB.newInstance(Item.tableName);
		prxItem.flush();

		DB prxCurrency = DB.newInstance(Currency.tableName);
		prxCurrency.flush();

	}

	public void clearCloseStatus() {
//		m_closeTimer.stop();
		closeTime = 0;
	}

	/**
	 * 获取玩家当前stageId
	 *
	 * @param
	 * @return
	 */
	public long getStageNowId() {
		return getStageLastIds().get(0);
	}

	/**
	 * 返回玩家之前经历的地图路径的id集合，历史靠近的地图在list的index较小的位置
	 *
	 * @param
	 * @return
	 */
	public List<Long> getStageLastIds() {
		List<Long> res = new ArrayList<>();
		Human3 human3 = getHuman3();
		// 检测stageHistory
		HumanManager.inst().checkStageHistory(human3);
		List<StageHistory> stageHistoryList = StageHistory.stageHistoryList(human3.getStageHistory());
		//循环遍历查找地图
		for(StageHistory stageHistory : stageHistoryList){
			res.add(stageHistory.stageId);
		}
		return res;
	}

	/**
	 * 获取玩家在地图历史中某张地图的坐标
	 *
	 *
	 * @param stageId
	 * @return
	 */
	public Vector2D getStagePos(long stageId) {
		Vector2D vector = new Vector2D(-1, -1);
		Human3 human = getHuman3();
		List<StageHistory> stageHistoryList = StageHistory.stageHistoryList(human.getStageHistory());
		for(StageHistory stageHistory : stageHistoryList){
			if (stageId == stageHistory.stageId) {
				vector.x = stageHistory.posX;
				vector.y = stageHistory.posY;
				break;
			}
		}
		return vector;
	}

	/**
	 * 监听玩家的属性变化，并在本次心跳结束后发送变化至客户端
	 */
	public void humanInfoChangeListen() {
		// 本次心跳已经添加过监听 忽略新的监听请求
		if (isHumanInfoListen)
			return;
		isHumanInfoListen = true;

//		if (humanInfoChange == null) {
//			humanInfoChange = new HumanInfoChange(this);
//			humanInfoChange.syncMsg(this);
//		}
	}


	/**
	 * 通过ID活着的自己可以控制的对象， 如果是玩家自己就返回自己，如果是武将那么就返回武将
	 *
	 * @param id
	 * @return
	 */
	public UnitObject getUnitControl(long id) {
		if (this.id == id) {
			return this;
		}
		return null;
	}


	/**
	 * 获取阵营的类型
	 *
	 * @return
	 */
	public int getCampType() {
		return CampTypeKey.TYPE_DEFAULT_HUMAN;
	}

	@Override
	public int getProfession() {
		return operation.profession.getJobSn();
	}

	@Override
	public void toState(UnitObjectStateKey stateKey, long time) {
		super.toState(stateKey, time);
		// 触发设定状态的事件
		Event.fire(EventKey.HUMAN_BE_SETSTATE, "UnitObject", this, "state",
				stateKey.ordinal());
	}


	/**
	 * 敌我识别用的
	 */
	@Override
	public int getCamp() {
		return CampTypeKey.TYPE_DEFAULT_HUMAN;
	}

	@Override
	public long getTeamBundleId() {
		return teamBundleID;
	}


	private void _result_onMemberCombatEnd(Param results, Param context) {

	}

	public void onRevive(int type, boolean isWild) {
	}

	public int getReviveCount(int type) {
		return 0;
	}

	public boolean isAutoTeamSetting() {
		return autoTeamSetting;
	}

	public void setAutoTeamSetting(boolean autoTeamSetting) {
		this.autoTeamSetting = autoTeamSetting;
	}

	public void setSwitchFrom(int switchFrom) {
		this.switchFrom = switchFrom;
	}

	public long getStopByClient() {
		return stopByClient;
	}

	public void setStopByClient(long stopByClient) {
		this.stopByClient = stopByClient;
	}

	/**
	 * 禁言
	 * @param keepTime
	 */
	public void silence(long keepTime){
		long timeEnd = Port.getTime() + keepTime;
		long preTime = getHuman3().getSilenceEndTime();
		if(preTime > timeEnd){
			//直接等于preTime,不要使用preTime + keepTime的方式，当preTime很大的时候，会导致无限累加(如势力禁言)
			timeEnd = preTime;
		}
		getHuman3().setSilenceEndTime(timeEnd);
	}

	private void periodCheckStageSwitching() {
		setStageSwitching(false);
	}

	public void setStageSwitching(boolean switching){
		this.isStageSwitching = switching;
		if(switching){
			periodChecker.scheduleCheck("periodCheckStageSwitching", this::periodCheckStageSwitching, 10 * Time.SEC).setTimes(1);
		}
	}

	public boolean isStageSwitching(){
		return isStageSwitching;
	}

	public void createStageInfoVO(){
	}


	/**
	 * 获取历史场景记录
	 * <AUTHOR>
	 * @Date 2022/11/29
	 * @Param
	 */
	public StageHistory lastStageHistory(){
		if(lastInfo == null){
			return null;
		}
		return new StageHistory(lastInfo.getStageId(), lastInfo.getStageSn(), lastInfo.getX(), lastInfo.getY(), lastInfo.getStageType());
	}


	public StageGameTypeKey getLastGameType(){
		if(lastInfo == null)
			return StageGameTypeKey.city;
		else
			return StageGameTypeKey.valueOf(lastInfo.getGameType());
	}


	public void onErrorTime(){
		cheatCount++;
		if(cheatCount >= 3){
			HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance();
			proxy.kick(id, 8507);
		}
	}

	@Override
	public boolean isInWorld(){
		return super.isInWorld();
	}


	/**
	 * 根据每日获取
	 * <AUTHOR>
	 * @Date 2021/12/30
	 * @Param
	 */
	public int getDailyResetTypeValue(int type){
		if (containsResetType(type)) {
			HumanDailyResetInfo info = getDailyResetInfo(type);
			return info.getValue();
		}
		return 0;
	}

	/**
	 * 检测是否玩家是否离线
	 * <AUTHOR>
	 * @Date 2022/6/17
	 * @Param
	 */
	public void checkCloseClear(){
		if(closeTime > 0 && !S.isBridge){
			// 避免重复踢人下线
			closeTime = -1;
			HumanGlobalServiceProxy hgPrx = HumanGlobalServiceProxy.newInstance();
			hgPrx.kick(id, 5178);// 您已离线
		}
	}

	/**
	 * 是否需要离线
	 * <AUTHOR>
	 * @Date 2022/7/5
	 * @Param
	 */
	public boolean isLine(){
		if(closeTime != 0){
			return true;
		}
		return false;
	}

	public boolean isMsgIdCD(int msgId){
		return isMsgIdCD(msgId, 0);
	}

	/**
	 * 是否cd冷却中
	 * <AUTHOR>
	 * @Date 2023/6/5
	 * @Param
	 */
	public boolean isMsgIdCD(int msgId, float cd){
		Long cdTime = Port.getTime();

		if(msgIdCDMap.containsKey(msgId)){
			cdTime = msgIdCDMap.get(msgId);
		}
		if(cdTime > Port.getTime()){
			return false;
		}

		if(cdTime <= 0 || cdTime <= Port.getTime()){
			if(cd > 0){
				cdTime = Port.getTime() + (long)(cd * Time.SEC);
			} else {
				cdTime = Port.getTime() + 1 * Time.SEC;
			}
			msgIdCDMap.put(msgId, cdTime);
		}
		return true;
	}

	/**
	 * 是否cd冷却中
	 * @Param
	 * @return true表示在cd中，false表示cd走完
	 */
	public boolean isCd(int cdType){
		Long nowTime = Port.getTime();
		long cdTime=0;
		if(cdMap.containsKey(cdType)){
			cdTime = cdMap.get(cdType);
		}
		return cdTime > nowTime;
	}

	public void addCd(int cdType, long cdTime){
		Long nowTime = Port.getTime();
		if(cdMap.containsKey(cdType)){
			cdTime = cdMap.get(cdType);
		}
		if(cdTime > nowTime){
			cdMap.put(cdType, cdTime+cdTime);
		}else{
			cdMap.put(cdType, nowTime+cdTime);
		}
	}



	/**
	 * 数据库记录存储，把HumanDailyResetTime的字段数据转成HumanDailyRestInfo对象
	 */
	public void setDailyResetRecord(HumanDailyResetTime record) {
		this.resetRecord = record;
		if(record == null){
			return;
		}
		JSONObject resetTimeJSONObj = Util.toJSONObject(record.getResetTimeJSON());
		JSONObject valueJSONObj = Util.toJSONObject(record.getValueJSON());
		JSONObject paramJSONObj = Util.toJSONObject(record.getParamJSON());
		Set<String> typeSet = new HashSet<>();
		typeSet.addAll(resetTimeJSONObj.keySet());
		typeSet.addAll(valueJSONObj.keySet());
		typeSet.addAll(paramJSONObj.keySet());
		for (String typeStr : typeSet) {
			int type = Integer.valueOf(typeStr);
			long resetTime = resetTimeJSONObj.getLongValue(typeStr);
			int value = (Integer) valueJSONObj.getOrDefault(typeStr, 0);
			String param = (String) paramJSONObj.getOrDefault(typeStr, "{}");
			HumanDailyResetInfo info = new HumanDailyResetInfo();
			// 这里调用该方法，不要去调用对应的set，防止无意义的数据库更新
			info.initHumanDailyResetInfo(type, resetTime, value, param);
			resetInfoMap.put(type, info);
		}
	}

	public boolean containsResetType(int type) {
		return resetInfoMap.containsKey(type);
	}

	public Map<Integer, HumanDailyResetInfo> getDailyResetInfoMap() {
		return resetInfoMap;
	}

	public HumanDailyResetInfo getDailyResetInfo(int type) {
		HumanDailyResetInfo result;
		if (!containsResetType(type)) {
			result = newHumanDailyResetInfo(type);
		} else {
			result = resetInfoMap.get(type);
		}
		return result;
	}

	public HumanDailyResetInfo newHumanDailyResetInfo(int type) {
		HumanDailyResetInfo info = new HumanDailyResetInfo(type);
		resetInfoMap.put(type, info);
		saveDailyResetRecord();
		return info;
	}

	/**
	 * 保存日常重置信息
	 */
	public void saveDailyResetRecord() {
		JSONObject resetTimeJO = new JSONObject();
		JSONObject valueJO = new JSONObject();
		JSONObject paramJO = new JSONObject();
		Set<String> updateFieldSet = new HashSet<>();
		Set<Integer> resetTypeSet = resetInfoMap.keySet();
		for (Integer resetType : resetTypeSet) {
			HumanDailyResetInfo info = resetInfoMap.get(resetType);
			// 判断该字段是否有修改过
			if (info.isFieldUpdate(HumanDailyResetTime.K.resetTimeJSON)) {
				updateFieldSet.add(HumanDailyResetTime.K.resetTimeJSON);
			}
			resetTimeJO.put(resetType.toString(), info.getResetTime());
			// 判断该字段是否有修改过
			if (info.isFieldUpdate(HumanDailyResetTime.K.valueJSON)) {
				updateFieldSet.add(HumanDailyResetTime.K.valueJSON);
			}
			valueJO.put(resetType.toString(), info.getValue());
			// 判断该字段是否有修改过
			if (info.isFieldUpdate(HumanDailyResetTime.K.paramJSON)) {
				updateFieldSet.add(HumanDailyResetTime.K.paramJSON);
			}
			paramJO.put(resetType.toString(), info.getParam());
			// 重置字段的修改状态
			info.resetFieldUpdate();
		}
		if (resetRecord == null) {
			resetRecord = new HumanDailyResetTime();
			resetRecord.setId(getHumanId());
			resetRecord.setResetTimeJSON(resetTimeJO.toJSONString());
			resetRecord.setValueJSON(valueJO.toJSONString());
			resetRecord.setParamJSON(paramJO.toJSONString());
			resetRecord.persist();
		} else {
			for (String field : updateFieldSet) {
				switch (field) {
					case HumanDailyResetTime.K.resetTimeJSON:
						resetRecord.setResetTimeJSON(resetTimeJO.toJSONString());
						break;
					case HumanDailyResetTime.K.valueJSON:
						resetRecord.setValueJSON(valueJO.toJSONString());
						break;
					case HumanDailyResetTime.K.paramJSON:
						resetRecord.setParamJSON(paramJO.toJSONString());
						break;
				}
			}
		}
	}


	public HumanExtInfo getHumanExtInfo(){
		return dataPers.extInfo;
	}


	/**
	 * 功能是否已经解锁，各个条件并的关系
	 * <AUTHOR>
	 * @Date 2024/3/19
	 * @Param
	 */
	public boolean isModUnlock(int modSn){
		if(modSn == 0){
			if(S.isSelectLog){
				Log.temp.info("===modSn==0");
			}
			return true;
		}
		ConfNewFuncOpen conf = ConfNewFuncOpen.get(modSn);
		if (conf == null){
			Log.temp.info("===ConfNewFuncOpen 配表错误，not sn ={}", modSn);
			return false;
		}
		return isModUnlock(conf);
	}

	public boolean isModUnlock(ConfNewFuncOpen conf){
//		if (Utils.isDebugMode()) {
//			return true;
//		}
		boolean defaultValue = true;
		if (conf.goods_type != 0) {
			// 这个条件与其他条件的关系是 || 关系
			defaultValue = false;
			if (isEquipModUnlock(conf.goods_type)) {
				if(S.isSelectLog){
					Log.temp.info("===isEquipModUnlock humanId={}, sn={}", id, conf.sn);
				}
				return true;
			}
		}
		//等级判断
		if (conf.level > 0) {
			defaultValue = true;
			if (conf.level > this.getHuman().getLevel()) {
				if(S.isSelectLog){
					Log.temp.info("===humanId={}, sn={}, confLv={}, level={}", id, conf.sn, conf.level, this.getHuman().getLevel());
				}
				return false;
			}
		}
		// 任务
		if (conf.taskId > 0) {
			defaultValue = true;
			if (!this.operation.taskData.isFinishMainTaskSn(conf.taskId)) {
				if(S.isSelectLog){
					Log.temp.info("=== humanId={},sn={}, conftaskId={}, level={}", id, conf.sn, conf.taskId, this.operation.taskData.getFinishPreFuncList());
				}
				return false;
			}
		}
		// 副本
		if (conf.ChapterId > 0) {
			defaultValue = true;
			if (conf.ChapterId >= this.getHuman2().getRepSn()) {
				if(S.isSelectLog){
					Log.temp.info("=== humanId={},sn={}, chapterId={}, repSn={}", id, conf.sn, conf.ChapterId, this.getHuman2().getRepSn());
				}
				return false;
			}
		}
		// 开服天数
		if (conf.open_day > 0) {
			defaultValue = true;
			int day = Utils.getDaysBetween(Port.getTime(), Util.getOpenServerTime(getHuman().getServerId())) + 1;
			if (conf.open_day > day) {
				if(S.isSelectLog){
					Log.temp.info("===humanId={}, sn={}, confDay={}, day={}, repSn={}", id, conf.sn,conf.open_day, day, Util.getOpenServerTime(getHuman().getServerId()));
				}
				return false;
			}
		}
		// 创角天数
		if (conf.create_day > 0) {
			defaultValue = true;
			int day = Utils.getDaysBetween(Port.getTime(), getHuman().getTimeCreate()) + 1;
			if (conf.create_day > day) {
				if(S.isSelectLog){
					Log.temp.info("===humanId={}, sn={}, confDay={}, day={}, repSn={}", id, conf.sn,conf.open_day, day, Util.getOpenServerTime(getHuman().getServerId()));
				}
				return false;
			}
		}
		if(S.isSelectLog){
			Log.temp.info("===humanId={}, sn={}, defaultValue={}", id, conf.sn, defaultValue);
		}
		return defaultValue;
	}

	private boolean isEquipModUnlock(int type) {
		List<Integer> goodsSnList = null;
		if (type == ItemConstants.坐骑皮肤) {
			if (getHuman2().getMountUse() != 0) {
				return true;
			}
			goodsSnList = GlobalConfVal.mountGoodsSnList;
		} else if (type == ItemConstants.神器皮肤) {
			if (getHuman2().getArtifactUse() != 0) {
				return true;
			}
			goodsSnList = GlobalConfVal.artifactGoodsSnList;
		} else if (type == ItemConstants.背饰皮肤) {
			if (getHuman2().getWingUse() != 0) {
				return true;
			}
			goodsSnList = GlobalConfVal.wingGoodsSnList;
		}
		if (goodsSnList != null) {
			for (Integer confGoodsSn : goodsSnList) {
				if (ItemManager.inst().getItemNum(this, confGoodsSn) != 0) {
					return true;
				}
			}
		}
		return false;
	}

	public int useTimeSec(int repType){
		if(!repTypeEnterTimeMap.containsKey(repType)){
			return -1;
		}
		long enterTime = repTypeEnterTimeMap.get(repType);
		return (int)((Port.getTime() - enterTime) / Time.SEC);
	}

	public long idGen(){
		if(idGen == 0){
			idGen = getHumanExtInfo().getIdGen();
		}
		++idGen;
		getHumanExtInfo().setIdGen(idGen);
//		getHumanExtInfo().update();
		return idGen;
	}

	public Define.p_role_figure to_p_role_figure(){
		Human human = getHuman();
		Human2 human2 = getHuman2();

		Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
		Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(human2.getEquipFigureMap());

		List<Define.p_key_value> equipList = new ArrayList<>();
		//遍历weapon到face的部位不存在或者value=0就去默认取
		int weapon = figureMap.getOrDefault(EquipInfo.weapon, EquipManager.FIGURE_DEFAULT);
		equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_1, weapon).build());

		int hat = figureMap.getOrDefault(EquipInfo.hat, EquipManager.FIGURE_DEFAULT);
		equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_2, hat).build());

		int face = figureMap.getOrDefault(EquipInfo.face, EquipManager.FIGURE_DEFAULT);
		equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_3, face).build());

		equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_4, FateManager.inst().getFateShowSn(this)).build());
		// 翅膀
		equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_5, human2.getWingUse()).build());

		equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_6, RelicManager.inst().getMaxLocation(this)).build());
		//遍历weapon到face的部位不存在或者value=0就去默认取
		dFigur.addAllEquipList(equipList);

		dFigur.setHairFigure(human2.getHairColor());// 发色
		dFigur.setJobFigure(human.getJobModel());
		// 坐骑
		int mountSn = human2.getMountUse();
		dFigur.setMountFigure(mountSn);
		// 神器sn
		int artifactSn = human2.getArtifactUse();
		dFigur.setArtifactFigure(artifactSn);
		dFigur.setGender(1);// 无性别 默认1
		// 皮肤
		dFigur.addSkinList(HumanManager.inst().to_p_key_value(2, human2.getCurrentSkin()));
		// 头衔
		dFigur.setCurrentTitle(human.getCurrentTitleSn());
		return dFigur.build();
	}

	public Define.p_role_figure to_p_role_figure(int planType){
		Human2 human2 = getHuman2();
		if(planType == human2.getEquipTab()){
			return to_p_role_figure();
		}
		Human human = getHuman();
		PlanVo vo = operation.planVoMap.get(planType);
		if(vo == null){
			vo = new PlanVo(planType);
		}
		int tabEquip = vo.getTab(PlanVo.TAB_EQUIP);

		Equip equip = operation.equipsMap.get(tabEquip);
		if(equip == null){
			return to_p_role_figure();
		}

		Define.p_role_figure.Builder dFigur = Define.p_role_figure.newBuilder();
		List<Define.p_key_value> equipList = new ArrayList<>();
		EquipInfo equipInfo = new EquipInfo();
		Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(human2.getEquipFigureMap());
		if(figureMap.containsKey(ParamKey.figureEquip_1)){
			int weapon = figureMap.getOrDefault(EquipInfo.weapon, EquipManager.FIGURE_DEFAULT);
			equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_1, weapon).build());
		} else {
			equipInfo.fromJsonString(equip.getPart1());
			equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_1, equipInfo.sn).build());
		}

		if(figureMap.containsKey(ParamKey.figureEquip_2)){
			int hat = figureMap.getOrDefault(EquipInfo.hat, EquipManager.FIGURE_DEFAULT);
			equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_2, hat).build());
		} else {
			equipInfo.fromJsonString(equip.getPart2());
			equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_2, equipInfo.sn).build());
		}

		if(figureMap.containsKey(ParamKey.figureEquip_3)){
			int face = figureMap.getOrDefault(EquipInfo.face, EquipManager.FIGURE_DEFAULT);
			equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_3, face).build());
		} else {
			equipInfo.fromJsonString(equip.getPart3());
			equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_3, equipInfo.sn).build());
		}
		equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_4, FateManager.inst().getFateShowSn(this)).build());
		// 翅膀
		equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_5, human2.getWingUse()).build());
		int tabRelic = vo.getTab(PlanVo.TAB_RELIC);
		equipList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_6, RelicManager.inst().getMaxLocation(this, tabRelic)).build());
		//遍历weapon到face的部位不存在或者value=0就去默认取
		dFigur.addAllEquipList(equipList);

		dFigur.setHairFigure(human2.getHairColor());// 发色
		dFigur.setJobFigure(human.getJobModel());
		// 坐骑
		dFigur.setMountFigure(human2.getMountUse());
		// 神器sn
		int artifactSn = human2.getArtifactUse();
		dFigur.setArtifactFigure(artifactSn);
		dFigur.setGender(1);// 无性别 默认1
		// 皮肤
		dFigur.addSkinList(HumanManager.inst().to_p_key_value(2, human2.getCurrentSkin()));
		// 头衔
		dFigur.setCurrentTitle(human.getCurrentTitleSn());
		return dFigur.build();
	}

	public int getGuildNextBossTime(){
		for (PeriodExecutor executor : periodChecker.getExecutors()) {
			if(executor.methodName.equals(PeriodKey.guildBossRecover)){
				return (int)(executor.tickTimer.getNextTime() / Time.SEC);
			}
		}
		return 0;
	}

	private void checkMailFive(){
		EntityManager.getEntityListAsync(Mail.class, id, (res) -> {
			if (!res.succeeded()) {
				//加载失败
				Log.temp.info("===加载Mail失败，humanId={}", id);
				return;
			}
			List<Mail> modelRoundList = res.result();
			for (Mail mail : modelRoundList) {
				if (operation.mailMap.containsKey(mail.getId())) {
					continue;
				}
				operation.mailMap.put(mail.getId(), mail);
				MailManager.inst().addNew(this, mail);
			}
		});
	}

	/**
	 * 检测删除过期的充值礼包
	 * <AUTHOR>
	 * @Date 2023/11/2
	 * @Param
	 */
	private void checkPayGift(){
		if(payGiftMap.isEmpty()){
			return;
		}
		long nowTime = Port.getTime();
		List<PayGift> removeList = new ArrayList<>();
		for(PayGift payGift : payGiftMap.values()){
			// 处理ai礼包有问题的模板
			if(payGift.getPrice() == 0 && payGift.getTemplateId().equals(ParamKey.PAYGIFT_TYPE_6) && payGift.getGiftCode().equals("1000191")){
				Log.temp.info("====记录充值ai礼包模板改动,humanId={},payId={}, templateId={}", id, payGift.getPayId(), payGift.getTemplateId());
				payGift.setTemplateId(ParamKey.PAYGIFT_TYPE_3);
				// 通知客户端
				org.gof.demo.worldsrv.msg.MsgPayMall.pay_gift_new_s2c.Builder msg = org.gof.demo.worldsrv.msg.MsgPayMall.pay_gift_new_s2c.newBuilder();
				msg.addDPayGift(MallManager.inst().createDPayGift(payGift));
				msg.setInit(false);
				sendMsg(msg);
			}
			// 玩家在线时，不删除付费礼包，防止充值到账之前，刚好礼包过期删除，导致购买没有发奖励
			if (payGift.getTemplateId().equals(ParamKey.PAYGIFT_TYPE_1)
				|| payGift.getTemplateId().equals(ParamKey.PAYGIFT_TYPE_5)
				|| payGift.getTemplateId().equals(ParamKey.PAYGIFT_TYPE_6)
			) {
				if (!payGiftDelSet.contains(payGift.getPayId()) && (payGift.getEndTime() <= nowTime || payGift.getBuyNum() >= payGift.getPurchaseLimitAmount())) {
					MallManager.inst().sendSCPayGiftRemove(this, payGift.getPayId());// 通知客户端删除，但服务器不删
					payGiftDelSet.add(payGift.getPayId());
				}
				continue;
			}
			if(payGift.getEndTime() <= nowTime){
				removeList.add(payGift);
				continue;
			}
			// 策划要求不根据次数删除。
			if(payGift.getBuyNum() >= payGift.getPurchaseLimitAmount()){
				MallManager.inst().sendSCPayGiftRemove(this, payGift.getPayId());// 通知客户端删除，但服务器不删
				payGiftDelSet.add(payGift.getPayId());
				continue;
			}		}
		MallManager.inst().removePayGift(this, removeList);
	}

	public long getTmCreate() {
		return tmCreate;
	}

	public void updateHumanData(boolean b) {
	}

	public String getRegional() {
		return new String(regional);
	}

	public void setRegional(String regional) {
		this.regional = regional != null ? new String(regional) : null;
	}

	// region 神灯皮肤
	// Map<神灯皮肤sn, 神灯皮肤对象>
	private Map<Integer, TreasureSkinInfo> treasureSkinMap = new HashMap<>();

	/**
	 * 初始化
	 */
	public void initTreasureSkinMap() {
		treasureSkinMap.clear();
		String json = getHumanExtInfo().getTreasureUnlockSkinJSON();
		JSONArray ja = Utils.toJSONArray(json);
		for (int i = 0; i < ja.size(); i++) {
			String skinJSON = ja.getString(i);
			TreasureSkinInfo vo = new TreasureSkinInfo(skinJSON);
			treasureSkinMap.put(vo.sn, vo);
		}
	}

	/**
	 * 获取全部的宝箱皮肤
	 */
	public Map<Integer, TreasureSkinInfo> getTreasureSkinMap() {
		return treasureSkinMap;
	}

	/**
	 * 激活宝箱皮肤
	 */
	public void addTreasureSkin(int sn) {
		if (treasureSkinMap.containsKey(sn)) {
			return;
		}
		TreasureSkinInfo info = new TreasureSkinInfo();
		info.sn = sn;
		treasureSkinMap.put(sn, info);
		saveTreasureSkin();
	}

	public void saveTreasureSkin() {
		JSONArray ja = new JSONArray();
		for (TreasureSkinInfo info : treasureSkinMap.values()) {
			ja.add(info.toJSONString());
		}
		getHumanExtInfo().setTreasureUnlockSkinJSON(ja.toJSONString());
//		getHumanExtInfo().update();
	}
	// endregion 神灯皮肤

	// region 26类型礼包过期相关处理
	/**
	 * Map<26类型礼包condition[0]+condition[1], 是否过期>
	 */
	private Map<String, Integer> gift26ExpireMap = new HashMap<>();

	public boolean isGift26GroupExpire(String type) {
		return gift26ExpireMap.getOrDefault(type, 0) == 1;
	}

	public void setGift26Expire(String type) {
		gift26ExpireMap.put(type, 1);
		HumanDailyResetInfo info = getDailyResetInfo(DailyResetTypeKey.dailyGift26Expire.getType());
		info.setParam(Utils.mapStringIntToJSON(gift26ExpireMap));
		saveDailyResetRecord();
	}

	public void resetGift26Expire() {
		HumanDailyResetInfo info = getDailyResetInfo(DailyResetTypeKey.dailyGift26Expire.getType());
		gift26ExpireMap = Util.jsonToMapStringInt(info.getParam());
	}

	/**
	 * Map<抽卡类型，上次触发该抽卡类型礼包时的抽卡数量>
	 */
	private Map<Integer, Integer> gift26DrawNumMap = new HashMap<>();

	/**
	 * 返回上次触发26类型抽卡礼包的抽卡数值
	 */
	public int getLastPushGift26DrawNum(int drawType) {
		return gift26DrawNumMap.getOrDefault(drawType, 0);
	}

	/**
	 * 设置触发26类型抽卡礼包的数值
	 */
	public void setPushGift26DrawNum(int drawType, boolean isSave) {
		int dailyResetType = 0;
		if (drawType == 1) {
			dailyResetType = DailyResetTypeKey.dailyDraw1Count.getType();
		} else if (drawType == 2) {
			dailyResetType = DailyResetTypeKey.dailyDraw2Count.getType();
		} else if (drawType == 3) {
			dailyResetType = DailyResetTypeKey.dailyDraw3Count.getType();
		}
		if (dailyResetType == 0) {
			return;
		}
		int num = getDailyResetTypeValue(dailyResetType);
		gift26DrawNumMap.put(drawType, num);
		if (!isSave) {
			return;
		}
		HumanDailyResetInfo info = getDailyResetInfo(dailyResetType);
		info.setParam(String.valueOf(num));
		saveDailyResetRecord();
	}

	// Map<26类型的条件类型, 该类型的购买次数>
	private Map<String, Integer> gift26BuyNumMap = new HashMap<>();

	public int getGift26BuyNum(String type) {
		return gift26BuyNumMap.getOrDefault(type, 0);
	}

	public void addGift26BuyNum(String type) {
		int buyNum = gift26BuyNumMap.getOrDefault(type, 0);
		gift26BuyNumMap.put(type, buyNum + 1);
		saveGift26BuyNum();
	}
	public void saveGift26BuyNum() {
		HumanDailyResetInfo info = getDailyResetInfo(DailyResetTypeKey.dailyGift26BuyNum.getType());
		info.setParam(Utils.mapStringIntToJSON(gift26BuyNumMap));
		saveDailyResetRecord();
	}

	public void resetGift26BuyNum() {
		HumanDailyResetInfo info = getDailyResetInfo(DailyResetTypeKey.dailyGift26BuyNum.getType());
		gift26BuyNumMap = Util.jsonToMapStringInt(info.getParam());
	}
	// endregion 26类型礼包过期相关处理


	public Define.p_battle_role to_p_battle_role(){
		return to_p_battle_role(getHumanExtInfo().getPlan());
	}

	public Define.p_battle_role to_p_battle_role(int planType){
		return to_p_battle_role_builder(planType).build();
	}

	public Define.p_battle_role.Builder to_p_battle_role_builder(int planType){
		Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();
		dInfo.setId(id);
		dInfo.setName(name);
		dInfo.setLev(getHuman().getLevel());
		dInfo.setJob(operation.profession.getJobSn());
		Define.p_role_skill.Builder dSkill = Define.p_role_skill.newBuilder();

		PlanVo planVo = operation.planVoMap.get(planType);
		if(planVo == null){
			planVo = new PlanVo(planType);
		}
		int tabSkill = planVo.getTab(PlanVo.TAB_SKILL);
		List<Define.p_active_skill> activeSkills = SkillManager.inst().getAllActiveSkill(this, tabSkill);
		dSkill.addAllActiveSkill(activeSkills);

		List<Define.p_passive_skill> passiveSkills = SkillManager.inst().getAllPassiveSkill(this, planType);
		dSkill.addAllPassiveSkill(passiveSkills);
		dInfo.setRoleSkill(dSkill);

		int tabPet = planVo.getTab(PlanVo.TAB_PET);
		dInfo.addAllPetList(PetManager.inst().getP_role_petList(this, tabPet));
		int flyPetFightSn = FlyPetManager.inst().getFlyPetFightSn(getHuman2(), planType);
		if (flyPetFightSn != 0) {
			dInfo.addExt(Define.p_key_value.newBuilder().setK(ParamKey.extType_6).setV(flyPetFightSn));
		}
		int angelTab = planVo.getTab(PlanVo.TAB_ANGEL);
		dInfo.addAllExt(AngelManager.inst().to_p_key_value_AngelSkill(this, angelTab));

		PropCalc propCalc;
		if(planType == getHumanExtInfo().getPlan()){
			propCalc = getPropPlus();
		}else {
			propCalc = getPropPlus(planVo);
		}
		for (Map.Entry<Integer, BigDecimal> entry : propCalc.getDatas().entrySet()) {
			int key = entry.getKey();
			// 1001、1002、1003、1024转换成最终属性1、2、3、24
			int newKey = HumanManager.inst().getType(key);
			long value = HumanManager.inst().getCalculationValue(key, propCalc.getDatas());
			if(value == -1){
				// 1001、1002、1003、1024转换成最终属性1、2、3、24
				value = propCalc.getBigDecimal(newKey).longValue();
			}
			Define.p_key_value.Builder dattr = HumanManager.inst().to_p_key_value(key, value);
			dInfo.addAttrList(dattr);
		}
		List<Integer> activeSnList = new ArrayList<>();
		for(Define.p_active_skill dActive : activeSkills){
			activeSnList.add(dActive.getSkillId());
		}

		List<Integer> petSnList = operation.petData.lineupMap.getOrDefault(tabPet, new ArrayList<>());
		List<Define.p_attr_obj_list> p_attr_objListAll = SkillManager.inst().to_p_attr_obj_list(passiveSkills,
				petSnList, activeSnList, HumanManager.inst().petAttList(propCalc, petSnList));
		dInfo.addAllAttrObjList(p_attr_objListAll);// p_attr_obj_list
		// TODO

		dInfo.setFigure(to_p_role_figure());
		dInfo.setManualOperator(Utils.getTimeSec());
//		dInfo.addAllOperators();//p_battle_operator
//		dInfo.addAllExt(); //p_key_value
		Define.p_head.Builder head = Define.p_head.newBuilder();
		head.setId(getHuman().getHeadSn());
		head.setFrameId(getHuman().getCurrentHeadFrameSn());
		head.setUrl("");
		dInfo.setHead(head);
		dInfo.setLeftHp(100);

//		List<Define.p_key_value> extList = new ArrayList<>();
//		Map<Integer,Integer> figureMap = Utils.jsonToMapIntInt(getHuman2().getEquipFigureMap());
//		//遍历weapon到face的部位不存在或者value=0就去默认取
//		int weapon = figureMap.getOrDefault(EquipInfo.weapon, EquipManager.FIGURE_DEFAULT);
//		extList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_1, weapon).build());
//
//		int hat = figureMap.getOrDefault(EquipInfo.hat, EquipManager.FIGURE_DEFAULT);
//		extList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_2, hat).build());
//
//		int face = figureMap.getOrDefault(EquipInfo.face, EquipManager.FIGURE_DEFAULT);
//		extList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_3, face).build());
//
//		extList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_4, getHuman2().getFateShow()).build());
//		// 翅膀
//		extList.add(HumanManager.inst().to_p_key_value(ParamKey.figureEquip_5, getHuman2().getWingUse()).build());
//		dInfo.addAllExt(extList);
		return dInfo;
	}

	public PropCalc getPropPlus(PlanVo planVo) {
		List<EntityUnitPropPlus> excludeNameList = new ArrayList<>();
		excludeNameList.add(EntityUnitPropPlus.equip);
		excludeNameList.add(EntityUnitPropPlus.statue);
		excludeNameList.add(EntityUnitPropPlus.wingTalent);
		List<PropCalc> propCalcList = new ArrayList<>();
		propCalcList.add(EquipManager.inst().getEquipPropCalc(this, planVo.getTab(PlanVo.TAB_EQUIP)));
		propCalcList.add(HomeManager.inst().getStatuePropCalc(this, planVo.getTab(PlanVo.TAB_STATUE)));
		propCalcList.add(WingManager.inst().getTalentPropCalc(this, planVo.getTab(PlanVo.TAB_WING_TALENT)));
		return HumanManager.inst().getPropPlus(dataPers.unitPropPlus, excludeNameList, propCalcList);
	}

}
