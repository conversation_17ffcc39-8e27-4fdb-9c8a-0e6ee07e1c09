package org.gof.demo.battlesrv.stageObj;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.demo.worldsrv.entity.*;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class UnitDataPersistance implements ISerilizable {
	public Unit unit;								//基本信息
	public HumanExtInfo extInfo; 					//角色的扩展信息数据
	public HumanSubjoinInfo subjoinInfo;			// 角色的附加信息数据（扩展）
	public final Map<Integer, Buff> buffs;			//玩家身上的buff集合，type，Buff映射
	public UnitPropPlusMap unitPropPlus;			//玩家属性加成
	public List<PayLog> payLogs;					//充值记录
	public List<PocketLine> pocketLine;	            // 待办事项

	// 是否切跨服
	public boolean isSwitchBridge = false;

	/**
	 * 构造函数
	 */
	public UnitDataPersistance() {
		buffs = new ConcurrentHashMap<>();
		unitPropPlus = new UnitPropPlusMap();
		payLogs = new ArrayList<>();
		pocketLine = new ArrayList<>();
	}
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
//		Log.temp.error("===记录一下，isSwitchBridge={}, isBridge={}", isSwitchBridge, S.isBridge);
		out.write(isSwitchBridge);
		out.write(unit);
		out.write(extInfo);
		out.write(subjoinInfo);
		out.write(unitPropPlus);
		out.write(buffs);
		out.write(payLogs);
		out.write(pocketLine);

	}
	
	@Override
	public void readFrom(InputStream in) throws IOException {
		isSwitchBridge = in.read();
		unit = in.read();
		extInfo = in.read();
		subjoinInfo = in.read();
		unitPropPlus = in.read();
		buffs.clear();
		buffs.putAll(in.<Map<Integer, Buff>>read());
		payLogs.clear();
		payLogs = in.read();
		pocketLine.clear();
		pocketLine = in.read();
	}
}
