package org.gof.demo.battlesrv.stageObj.area;

import org.gof.demo.battlesrv.stageObj.UnitObject;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.stage.StageObject;

/**
 * 圆形地图区域
 */
public class RoundAreaObject extends AreaObject {

	// 半径
	private float radius;

	public RoundAreaObject(StageObject stageObj, int type, int shapeType,
			int camp, Vector2D center, float radius) {
		super(stageObj, type, shapeType, camp, center);
		this.radius = radius;
	}

	public RoundAreaObject(StageObject stageObj, Vector2D center, float radius) {
		this.center = center;
		this.radius = radius;
	}

	@Override
	public boolean isInArea(UnitObject obj) {
		boolean isIn = isVectInArea(obj.getPosNow());
		return isIn;
	}

	@Override
	public boolean isVectInArea(Vector2D vector2D) {
		double detaX = vector2D.x - center.x;
		double detaY = vector2D.y - center.y;
		boolean isInArea = (detaX * detaX + detaY * detaY) <= (radius * radius);
		return isInArea;
	}


	public float getRadius() {
		return radius;
	}

	
}
