package org.gof.demo.battlesrv.stageObj.area;

import org.gof.demo.battlesrv.stageObj.UnitObject;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.stage.StageObject;

/**
 * 矩形地图区域
 */
public class RectangleAreaObject extends AreaObject {

	// 宽高
	private float width;
	private float height;

	// 朝向
	private double cosA;
	private double sinA;

	
	public RectangleAreaObject(StageObject stageObj, int type, int shapeType,int camp, Vector2D center, float width, float height, Vector2D dir) {
		super(stageObj, type, shapeType, camp, center);
		this.width = width;
		this.height = height;

		double c = center.distance(dir);

		cosA = (double) (dir.x - center.x) / c;
		sinA = (double) (dir.y - center.y) / c;
	}
	
	@Override
	public boolean isInArea(UnitObject obj) {
		if (obj.stageObj == super.stageObj) {

			return isVectInArea(obj.getPosNow());

		}
		return false;
	}

	@Override
	public boolean isVectInArea(Vector2D vector2D) {
		Vector2D p = vector2D;

		double dx = p.x - center.x;
		double dy = p.y - center.y;
		double x1 = cosA * dx + sinA * dy + center.x;
		double y1 = cosA * dy - sinA * dx + center.y;

		return (y1 <= (center.y + width / 2)
				&& y1 >= (center.y - width / 2)
				&& x1 >= (center.x - height / 2) && x1 <= (center.x + height / 2));
	}

}
