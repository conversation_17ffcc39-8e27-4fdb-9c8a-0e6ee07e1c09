package org.gof.demo.battlesrv.stageObj;

import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.demo.battlesrv.support.Vector3D;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.support.Log;

import java.util.List;

public class MoveSyncChecker {
    // 收到移动请求包的时间
    private long lastPacketTime;
    private long lastSyncTime;
    private int syncTimes;
    private int suspectTimes;
    private int max_sync_times = 10;
    private int max_suspect_times = 5;
    private double offsetBase = 4.0;
    private double offsetEx = 8.0;
    private double offsetAdjust = 0;
    private long firstSyncTime = 0;
    private int pullTimes = 0;
    private long checkStartTime;



    /**
     * 检查角色当前位置和客户端发上来的起始移动坐标的差距
     * @param humanObj
     * @param posBegin
     * @return
     */
    public int check(HumanObject humanObj, Vector3D posBegin, int action, List<Vector3D> posEndList) {
        if (action == 3) {
            return check3(humanObj, posBegin, posEndList);
        } else {
            return check2(humanObj, posBegin);
        }
    }

    private int check3(HumanObject humanObj, Vector3D posBegin, List<Vector3D> posEndList) {
        if (!humanObj.running.isTimeExpired()) {
            return 0;
        }

        if (checkStartTime == 0L) {
            checkStartTime = Port.getTime();
        } else if (checkStartTime + Time.MIN < Port.getTime()) {
            checkStartTime = 0L;
            suspectTimes = 0;
            pullTimes = 0;
        }

        double distance_begin_now = posBegin.toVector2D().distance(humanObj.getPosNow());
        double distance_begin_end = posBegin.toVector2D().distance(posEndList.get(0).toVector2D());
        double distance_now_end = humanObj.getPosNow().distance(posEndList.get(0).toVector2D());

        if (distance_now_end > 2 * distance_begin_end && distance_begin_end > 0) {
            if (distance_begin_now > 4 && distance_begin_now > distance_begin_end) {
                if (checkStartTime > 0 && checkStartTime + Time.MIN > Port.getTime()) {
                    pullTimes++;
//                    Log.game.info("distance1 [{}] distance2 [{}] distance3 [{}] suspectTimes [{}]", distance_begin_now, distance_begin_end, distance_now_end, suspectTimes);
                }
            }

            if (distance_now_end > 3 * distance_begin_end && distance_begin_end > 0) {
                if (distance_begin_now > 4 && distance_begin_now > distance_begin_end) {
                    if (checkStartTime > 0 && checkStartTime + Time.MIN > Port.getTime()) {
                        suspectTimes++;
//                        Log.game.info("distance1 [{}] distance2 [{}] distance3 [{}] suspectTimes [{}]", distance_begin_now, distance_begin_end, distance_now_end, suspectTimes);
                    }
                }
            }

            if (pullTimes > 5) {
                pullTimes = 0;
                return 1;
            }

            if (suspectTimes > 10) {
                suspectTimes = 0;
                return 2;
            }
        }

        return 0;
    }

    private int check1(HumanObject humanObj, Vector3D posBegin, List<Vector3D> posEndList) {
        if (!humanObj.running.isTimeExpired()) {
            return 0;
        }

        double distance = posBegin.toVector2D().distance(humanObj.getPosNow());
        double distance2 = posBegin.toVector2D().distance(posEndList.get(0).toVector2D());
        double distance3 = humanObj.getPosNow().distance(posEndList.get(0).toVector2D());
        Log.game.info("distance {} distance2 {} distance3 {}", distance, distance2, distance3);
        double offset = offsetBase;
//        if (humanObj.getMountsUp() == 1) {
//            offset = offsetEx;
//        }

        double speed = 1.0D * humanObj.getUnit().getSpeed() / 100;
        offset += offsetAdjust;

        long now = System.currentTimeMillis();
        if (lastPacketTime > 0 && now >= lastPacketTime) {
            long diff = now - lastPacketTime;
            double maxDistance = (diff * speed) / Time.SEC;

            double clientSpeed = distance / diff;
            if (distance > maxDistance && maxDistance + offset < distance) {
//                Log.game.info("移动的距离有问题，进一步检查时间, 距离相差 {} 客户端速度 {}", distance - maxDistance, clientSpeed*Time.SEC);
                if (lastSyncTime == 0 || lastSyncTime + Time.SEC > now) {
//                    Log.game.info("不会是外挂吧，记着 {} diff {} offset {}", syncTimes, now - lastSyncTime, offset);
                    setLastSyncTime(now);
                    if (syncTimes == 0) {
                        firstSyncTime = now;
                    }

                    incSyncTimes();
                    if (offsetAdjust < 5) {
                        offsetAdjust += 0.01;
                    }
                } else {
//                    Log.game.info("距离上一个同步过了 {} 当前同步次数 {}", now - lastSyncTime, syncTimes);
                    if (syncTimes > 0) {
                        decSyncTimes();
                    }

                    setLastSyncTime(now);
                }
            } else {
//                Log.game.info("理论移动距离 {} 实际移动距离 {}  相差 {} offset {}", maxDistance, distance, Math.abs(distance - maxDistance), offset);
                setLastSyncTime(now);
            }

            if (syncTimes > max_sync_times) {
                suspectTimes++;
                Log.game.info("疑似外挂");
                syncTimes = 0;
                setLastSyncTime(0);
                return 1;
            }

            if (suspectTimes > max_suspect_times) {
                suspectTimes = 0;
                syncTimes = 0;
                setLastSyncTime(0);
                if (now > firstSyncTime + 3 * 60 + Time.SEC) {
                    Log.game.info("确定外挂，花费 {}" ,now-firstSyncTime);
                    firstSyncTime = 0;
                    offsetAdjust = 0;
                    return 2;
                } else {
                    Log.game.info("排除外挂");
                    firstSyncTime = 0;
                    offsetAdjust = 0;
                }
            }
        }

        lastPacketTime = now;
        return 0;
    }

    private int check2(HumanObject humanObj, Vector3D posBegin) {
        return 0;
//        if (posBegin.toVector2D().distance(humanObj.getPosNow()) < 25) {
//            return 0;
//        }
//
//        return 1;
    }

    public void syncStop() {
        lastPacketTime = 0L;
        setLastSyncTime(0L);
    }

    private void incSyncTimes() {
        syncTimes++;
    }

    private void decSyncTimes() {
        syncTimes--;
    }

    private void setLastSyncTime(long syncTime) {
        lastSyncTime = syncTime;
    }
}
