package org.gof.demo.battlesrv.stageObj;

import com.google.common.collect.HashMultiset;
import com.google.common.collect.Multiset;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.Port;
import org.gof.core.support.Param;
import org.gof.core.support.S;
import org.gof.core.support.TickTimer;
import org.gof.demo.battlesrv.support.*;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.character.HumanObjectMirr;
import org.gof.demo.worldsrv.character.MonsterObject;
import org.gof.demo.worldsrv.character.part.RagePart;
import org.gof.demo.worldsrv.character.part.SkillPassiveEffectVO;
import org.gof.demo.worldsrv.entity.Buff;
import org.gof.demo.worldsrv.entity.EntityUnitPropPlus;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.Unit;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.stage.StageObject;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.StageGameTypeKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventBridgeKey;
import org.gof.demo.worldsrv.support.observer.EventKey;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * 角色基类 包含移动、战斗等
 */
public abstract class UnitObject extends WorldObject {
	public UnitDataPersistance dataPers = new UnitDataPersistance();
	
	public Running running = new Running(this); // 玩家移动信息
	
	public boolean canMove = true; 	// 是否可移动
	public boolean canCastSkill = true; // 是否可以施放技能
	public boolean canAttack = true;	// 是否可以攻击
	public boolean castSkilling = false; // 是否正在释放技能
	public boolean canFindEnemy = true; //是否索敌(除了主动攻击之外的一个特殊判断)
	
	private boolean pause = false; //暂停，（目前状态，一个特殊的字段,比如副本结束的话设定暂停就不再索敌,也不再攻击）

	public boolean isCastLastSkilling = false;
	
	//Buff更新Timer
	public TickTimer timerBuffPulse = null;	
	public long lastBuffTime = 0;

	// 上次更新血量时间
	public long lastUpdateTime = 0;

	//回血的Timer
	public TickTimer timerRecovHPPluse = null;
	
	// unitObj的状态
	public Map<UnitObjectStateKey, TickTimer> state = new HashMap<>();
	public TickTimer timerStatePulse = null;

	/**
	 * 出生位置
	 */
	public Vector2D posBegin = new Vector2D();			//出生位置
	
	//从属关系
	public long parentObjectId = 0L;
	protected long teamBundleID = -1;

	public int order; //站位顺序
	public int index = 0; //当前波数的位置索引
	public int profession; //职业

	private long lastSyncAIMove = 0L;
	
	//normal状态下是否能移动
	public boolean canMoveNormal = true;

	//被动触发技能
	private TickTimer passiveSkillUpdate = null;
	private Param passiveSkillParam;
	
	public Vector2D normalStatePos;

	// 被动技能效果相关信息
	public SkillPassiveEffectVO skillPassiveEffectVO = new SkillPassiveEffectVO();
	
	//消散时间
	public long disappearTime = 0;


	//锁定的进攻目标
	public long lockedTarget = 0;

	//是否是免疫状态
	public boolean isImmunity = false;

	//上次选敌目标
	public long lastSelectedTarget = 0;

	public RagePart ragePart;//怒气

	/** 死亡的当前时间*/
	private long deathTime = 0;

	private boolean canRevive=true;//是否可以复活

	public long chaseStartTime;

	public int deadCount = 0;//死亡次数

	public boolean isDie = false;//是否死亡状态(目前玩家专用)

	private boolean openAI = true;
	// 是否观战
	public boolean isSpectators = false;

	private boolean inFighting;

	public void openAI(){
		openAI = true;
	}

	public void closeAI(){
		openAI = false;
	}


	public UnitObject(StageObject stageObj) {
		super(stageObj);
	}

	public void setInFighting(boolean isFighting) {
		inFighting = isFighting;
	}

	public boolean isInFighting() {
		return inFighting;
	}

	@Override
	public void startup() {
		super.startup();
	}

	public void pause() {
		this.pause = true;
	}

	public boolean isPause() {
		return this.pause;
	}

	public boolean isHumanObj() {
		return this instanceof HumanObject;
	}

	public boolean isHumanObjMirror() {
		return this instanceof HumanObjectMirr;
	}

	public long getHumanId() {
		if(isHumanObj() || isHumanObjMirror()) {
			return id;
		}
		return -1;
	}


	public HumanObject getHumanObj() {
		if(isHumanObj()) {
			return (HumanObject)this;
		}
		return null;
	}
	
	public UnitObject getParentObject() {
		if(stageObj == null){
			return null;
		}
		return stageObj.getUnitObj(parentObjectId);
	}
	
	@Override
	public void pulse(int deltaTime) {
		super.pulse(deltaTime);
	}
	
	/**
	 * 地图单元移动
	 * 
	 * @param timeCurr
	 *            当前时间g
	 */
	public void pulseMove(long timeCurr) {
		if (!canMove)
			return;
		if (!isInWorld())
			return;
		if (!running.isRunning())
			return;
		// 怪物处理一下配置是否能移动，canMove是会默认true且战斗在用
//		if(this instanceof MonsterObject){
//			MonsterObject monster = (MonsterObject)this;
////			if(!monster.conf.canMove){
////				return;
////			}
//		}
		// 单元移动了
		running._pulse(timeCurr);
	}
	
	/**
	 * 待施放技能队列中取可以施放的技能，放技能
	 */
	public void updateSkill(long curr) {
//		if(skillTempInfo == null){
//			return;
//		}
//		SkillExcute skillToExcute = skillTempInfo.skillToExcute;
//
//		// 如果没有前摇技能，则不管
//		if (skillToExcute == null)
//			return;
//
//		// 前摇时间没有结束
//		if (!skillToExcute.tickTimer.isOnce(curr))
//			return;
//
//		SkillParam skillParam = skillToExcute.tarPos;
//		skillTempInfo.skillToExcute = null;
//
//		SkillBook book = getSkillBook();
//		if (book == null) {
//			return;
//		}
//		SkillCommon skill = book.getSkill(skillToExcute.sn);
//
//		// 技能不存在
//		if(skill == null){
////			Log.fight.info("技能不存在 skillToExcute.sn={}",skillToExcute.sn);
//			if(skillParam.skillLevel<=1){
//				//设置技能等级
////				skillParam.skillLevel = SkillInbornManager.inst().getSkillLevel(this, skillParam.confSkill.sn);
//			}
//			//初始化技能
//			skill = new SkillCommon(this, skillParam.confSkill, skillParam.skillLevel);
//		}
//
//		this.removeState(UnitObjectStateKey.skill_shake);
////		Log.fight.info("取消 UnitObjectStateKey.skill_shake 时间 ObjTime={} ,是否正在施法状态={}",getTime(),state.containsKey(UnitObjectStateKey.cast_skilling));
//
//		skill.castSecond(skillToExcute.tarPos);
	}

	/**
	 * 强制单元停止
	 * 
	 */
	public void stop(int stopType) {
		//如果没有移动，就return 但是也要广播停止的类型
		if (!running.isRunning()) {
			return;
		}

		//强制同步移动
		this.running._pulse(this.getTime(), true);
		
		// 停止移动
		running._stop();

	}

	
	/**
	 * 强制单元停止
	 * @param dir 方向
	 */
	public void stop(Vector2D dir) {
		if (!running.isRunning())
			return;
		//强制同步移动
		this.running._pulse(this.getTime(), true);

		// 停止移动
		running._stop();

		if (this instanceof HumanObject) {
			HumanObject humanObj = (HumanObject)this;
			humanObj.moveChecker.syncStop();
		}
	}
	
	
	

	public void die(UnitObject killer, Param params) {
		Unit unit = getUnit();
		deadCount ++;

		Log.game.debug("怪物死亡 time={}",Port.getTime());
		
		unit.setHpCur(0);
		isDie = true;
		// 设置死亡时间
		setDeathTime(System.currentTimeMillis());

		setInWorld(false);


		// 停止移动
		stop(1);

		List<Long> targets = new ArrayList<>();

		if (!S.isBridge) {
			Event.fireEx(EventKey.UNIT_BE_KILLED, stageObj.sn, "killer", killer, "dead", this, "skillSn", params.get("skillSn"), "enmityList", targets);
		}else {
			Event.fireEx(EventBridgeKey.BRIDGE_UNIT_BE_KILLED, stageObj.sn, "killer", killer, "dead", this, "skillSn", params.get("skillSn"), "enmityList", targets);
		}

		// 设定消散时间
		disappearTime = getTime();

		stageObj.onUnitObjDie(killer,this);
	}


	/**
	 * 让unitObj进入某种状态，time为持续时间
	 * 
	 * @param stateKey
	 * @param time
	 */
	public void toState(UnitObjectStateKey stateKey, long time) {
//		Log.temp.info("toState :{} {}", stateKey.toString(), time);
		long curr = getTime();
		
		if (state.get(stateKey) != null) {
			TickTimer timer = state.get(stateKey);
			long timeLeft;
			if (timer != null) {
				timeLeft = timer.getTimeLeft(curr);
				timeLeft = Math.max(time, timeLeft);
				// 容错处理 ,状态最少一帧
				timeLeft = Math.max(timeLeft, 33);
				timer.start(curr,timeLeft);
			}
		} else {
			TickTimer timer = new TickTimer();
			// 容错处理 ,状态最少一帧
			time = Math.max(time, 33);
			timer.start(curr, time);
			state.put(stateKey, timer);
			if(stateKey == UnitObjectStateKey.immobilize) {
				Log.fight.info("添加施法状态 time={},持续时间={}",curr, time+getTime());
			}
		}
		
//		updateState(curr, true);
	}
	

	

	public void buffChange(int sn, int value) {
		Buff buff = dataPers.buffs.get(sn);
		if(buff == null)
			return;
		
		buff.setBuffValue(value);
	}

	public boolean isDie() {
		// 如果是玩家，多判断一下，避免被回血导致依然死亡无法复活
		return isDie || getUnit().getHpCur() <= 0;
	}

	public Unit getUnit() {
		return dataPers.unit;
	}
	
	public Map<Integer, Buff> getBuffs() {
		return dataPers.buffs;
	}

	public PropCalc getPropPlus() {
		List<EntityUnitPropPlus> excludeNameList = new ArrayList<>();
		return HumanManager.inst().getPropPlus(dataPers.unitPropPlus, excludeNameList);
	}

	/**
	 * 排除一级属性外的全部属性
	 * @return
	 */
	public PropCalcCommon getPropPlusWithoutOne() {
		UnitPropPlusMap humanPropPlus = dataPers.unitPropPlus;

		PropCalcCommon data = new PropCalcCommon();

		//遍历加成属性来累加数据
		for(EntityUnitPropPlus k : EntityUnitPropPlus.values()) {
			data.plus(humanPropPlus.dataMap.get(k.name()));
		}
		return data;
	}



	/**
	 * 获取战力计算的属性，去掉不需要计算的数值
	 * 忽略buff
	 * @return
	 */
	public PropCalcCommon getCombatPropPlus() {
		UnitPropPlusMap humanPropPlus = dataPers.unitPropPlus;


		PropCalcCommon data = new PropCalcCommon();

//		if(isFairMap){
//			//遍历加成属性来累加数据
//			for(EntityUnitPropPlus k : EntityUnitPropPlus.values()) {
//				if(k == EntityUnitPropPlus.buff) continue;
//				if(k != EntityUnitPropPlus.fair){
//					continue;
//				}
//
//				data.plus(humanPropPlus.dataMap.get(k.name()));
//			}
//		}else {
			//遍历加成属性来累加数据
			for(EntityUnitPropPlus k : EntityUnitPropPlus.values()) {
				if(k == EntityUnitPropPlus.buff) continue;
				data.plus(humanPropPlus.dataMap.get(k.name()));
			}
//		}


		return data;
	}


   public double nextDouble() {
       if(this.stageObj == null) {
    	   Log.temp.error("Error nextDouble stageObj == null");
    	   return 0;
       }
       return this.stageObj.randUtils.nextDouble();
    }

    public int nextInt(int range) {
        if(this.stageObj == null) {
     	   Log.temp.error("Error nextInt stageObj == null");
     	   return 0;
        }
        return this.stageObj.randUtils.nextInt(range);
    }
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		super.writeTo(out);
		out.write(parentObjectId);
		out.write(teamBundleID);
		out.write(order);		
		out.write(lastBuffTime);
		out.write(profession);
		out.write(isSpectators);
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		super.readFrom(in);
		parentObjectId = in.read();
		teamBundleID = in.read();
		order = in.read();
		lastBuffTime = in.read();
		profession = in.read();
		isSpectators = in.read();
	}

	/**
	 * 返回职业
	 * @return
	 */
	public abstract int getProfession();
	
	/**
	 * 返回阵营
	 * @return
	 */
	public abstract int getCamp();

	public void setTeamId(long teamId) {
		this.teamBundleID = teamId;
	}
	
	/**
	 * 返回小队
	 * @return
	 */
	public abstract long getTeamBundleId();
	
	/**
	 *
	 * @param teamId
	 */
	
	public int[] getImmuneInBorn(){
		return null;
	}

	public void setPassiveSkillEvent(long time, Object... params){
		this.passiveSkillParam = new Param(params);
		this.passiveSkillUpdate = new TickTimer(getTime(), time);
	}
	
	private void doPassiveSkill(){
		if(passiveSkillParam!=null)
			Event.fire(EventKey.SKILL_PASSIVE_CHECK, passiveSkillParam);
		
		passiveSkillUpdate = null;
	}
	


	public String getStageGameType(){
//		if(stageObj!=null)
//			return stageObj.confMap.gameType;
//		else
			return StageGameTypeKey.city.getS();
	}

	public boolean isRageFull() {
		return false;
	}

	public void addRage(int addRage){
		if(ragePart == null){
			Log.temp.info("===部分可以能没有怒气， id={}, obj={}", id, this);
			return;
		}
		ragePart.addRage(addRage);
		sendRageInfo();
	}

	public void reduceRage(int rage) {
		if (rage == 0) return;
		ragePart.reduceRage(rage);
		sendRageInfo();
	}

	public abstract  int getRage();
	
	
	public void sendTitleStatus(String status) {
//		SCStageObjectHeadInfo.Builder msg = SCStageObjectHeadInfo.newBuilder();
//		msg.setObjId(this.id);
//		msg.setInfo(status);
//		StageManager.inst().sendMsgToArea(msg, this.stageObj, this.getPosNow());
	}

	public void sendRageInfo() {
//		MsgFight.SCFightSkill.Builder scFightSkill = MsgFight.SCFightSkill.newBuilder();
//		List<Define.DRageInfo> rageInfoList = new ArrayList<>();
//		Define.DRageInfo.Builder ratkInfo = Define.DRageInfo.newBuilder();
//		ratkInfo.setUnitId(this.id);
//		ratkInfo.setRage(getRage());
//		rageInfoList.add(ratkInfo.build());
//		MsgFight.SCFightHpChg hpchg = scFightSkill.getHpChg();
//		hpchg = hpchg.toBuilder().addAllRageInfo(rageInfoList).build();
//		scFightSkill.setHpChg(hpchg);
//		StageManager.inst().sendMsgToArea(scFightSkill, this.stageObj, this.getPosNow());
	}




	/**
	 * 召唤怪物计数Map
	 */
	private Multiset<Integer> callMonstersMap = HashMultiset.create();

	/**
	 * 召唤一个怪物
	 * @param sceneCharacterSn
	 * @param level
	 * @param pos
	 * @param dir
	 * @param delayTime
	 * @return
	 */
	public MonsterObject callMonster(int sceneCharacterSn,int level, Vector2D pos ,Vector2D dir,long delayTime){
		MonsterObject monsterObj;
//		if(delayTime == 0){
//			monsterObj =  StageManager.inst().refreshMonster(sceneCharacterSn, level, stageObj,pos,dir, true);
//		}else{
//			monsterObj = StageManager.inst().refreshMonster(sceneCharacterSn, level, stageObj,pos,dir, false);
//			stageObj.addLazyMonster(monsterObj);
//		}
//
//		monsterObj.setBirthTime(getTime() + delayTime);
//		//设置他的创建者
//		monsterObj.fireObj = this;
		//设置计数
		callMonstersMap.add(sceneCharacterSn,1);
//		return  monsterObj;
		return null;
	}


//	/**
//	 * 召唤一个采集物
//	 * @param sceneCharacterSn
//	 */
//	public void callGatherObj(int sceneCharacterSn,Vector2D vector2D){
//		GatherObject gatherObject = GatherManager.inst().create(stageObj,sceneCharacterSn);
//		//设置他的创建者
//		gatherObject.fireObj = this;
//		//设置计数
//		callMonstersMap.add(sceneCharacterSn,1);
//
//		gatherObject.setPosNow(vector2D);
//		gatherObject.startup();
//		gatherObject.stageShow();
//	}

	/**
	 * 查看召唤怪物的数量
	 * @return
	 */
	public int getCallUnitCount(int sceneCharacterSn){
		return callMonstersMap.count(sceneCharacterSn);
	}

	/**
	 * 死亡的时候删除召唤者的限定
	 * @param sceneCharacterSn
	 */
	public void removeCallCount(int sceneCharacterSn){
		callMonstersMap.remove(sceneCharacterSn,1);
	}

	public long getDeathTime() {
		return this.deathTime;
	}

	public void setDeathTime(long deathTime) {
		this.deathTime = deathTime;
	}

	public boolean isCanRevive() {
		return canRevive;
	}

	public void setCanRevive(boolean canRevive) {
		this.canRevive = canRevive;
	}

	public void onInjured(UnitObject unitObject, long hpLoss) {}

	/** 
	 * 是否能释放不受控制的技能immune为true
	 * <AUTHOR>
	 * @Date 2021/7/2
	 * @Param 
	 */
	public boolean isCanSkillImmune(){
		long curr = Port.getTime();
		// 正在释放技能
		if(castSkilling){
			return false;
		}

		if (!state.isEmpty()) {
			for (Entry<UnitObjectStateKey, TickTimer> entry : state.entrySet()) {
				UnitObjectStateKey state = entry.getKey();
				TickTimer timer = entry.getValue();
				// 根据不同的状态进行不同的处理，同时如果时间到了则解除状态
				switch (state) {
					case cast_skilling:	//正在释放技能
					case skillback:		//技能击退
					case skill_sheep: //变羊
					case skill_shake:		//施法前摇
					case beatFly:		//击飞
					case fear:			//恐惧
						if (!timer.isOnce(curr)) {
							return false;
						}
						break;
					default:
						break;
				}
			}
		}
		return true;
	}

}
