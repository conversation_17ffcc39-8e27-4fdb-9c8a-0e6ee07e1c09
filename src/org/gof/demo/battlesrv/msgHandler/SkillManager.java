package org.gof.demo.battlesrv.msgHandler;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.battlesrv.manager.CombatChangeLog;
import org.gof.demo.battlesrv.manager.PropManager;
import org.gof.demo.battlesrv.support.PropCalc;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.angel.AngelManager;
import org.gof.demo.worldsrv.artifact.ArtifactManager;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.*;
import org.gof.demo.worldsrv.fate.FateManager;
import org.gof.demo.worldsrv.flyPet.FlyPetInfo;
import org.gof.demo.worldsrv.flyPet.FlyPetManager;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.EModule;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.PlanVo;
import org.gof.demo.worldsrv.jobs.JobsManager;
import org.gof.demo.worldsrv.mount.MountManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgSkill;
import org.gof.demo.worldsrv.msg.MsgSkill.skill_list_s2c;
import org.gof.demo.worldsrv.pet.PetData;
import org.gof.demo.worldsrv.pet.PetManager;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.relic.RelicManager;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.wing.WingManager;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 技能
 *
 * <AUTHOR>
 * @Date 2024/3/14
 * @Param
 */
public class SkillManager extends ManagerBase {

    public static final int lineupMax = 5;
    public static final int posMax = 5;


    public static final int skillPosIndex = 2;// 技能下标要-2
    public static final int skill_system_update_type_0 = 0;// 获得0
    public static final int skill_system_update_type_2 = 2;// 升级2


    public static SkillManager inst() {
        return inst(SkillManager.class);
    }


    public void _msg_skill_list_c2s(HumanObject humanObj) {
        skill_list_c2s(humanObj);
    }

    private void skill_list_c2s(HumanObject humanObj) {
        skill_list_s2c.Builder msg = skill_list_s2c.newBuilder();
        // 主动技能
        msg.addAllActiveSkills(getAllActiveSkill(humanObj));
        // 被动技能
        msg.addAllPassiveSkills(getAllPassiveSkill(humanObj));

        humanObj.sendMsg(msg);
    }

    public List<Define.p_active_skill> getAllActiveSkill(HumanObject humanObj){
        return getAllActiveSkill(humanObj, humanObj.getHuman2().getUseSkillLineup());
    }
    /** 
     * 获取玩家所有主动技能 
     * <AUTHOR>
     * @Date 2024/5/15
     * @Param 
     */
    public List<Define.p_active_skill> getAllActiveSkill(HumanObject humanObj, int lineup){
        List<Define.p_active_skill> activeSkillList = new ArrayList<>();
        List<Integer> skillSnList = humanObj.operation.skillData.lineupMap.get(lineup);
        Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillSnLvMap;
        Profession profession = humanObj.operation.profession;
        Map<Integer, Map<Integer, Long>> tabDelayMap = humanObj.operation.skillData.lineupDelayMap;
        Map<Integer, Long> posTimeMap = tabDelayMap.get(lineup);
        if(posTimeMap == null){
            posTimeMap = new HashMap<>();
            tabDelayMap.put(lineup, posTimeMap);
        }
        int delayTime = Utils.intValue(posTimeMap.get(1));
        // 主动技能
        activeSkillList.add(to_p_active_skill(profession.getJobSkillSn(), profession.getJobSkillLv(), 1, delayTime).build());

        if(skillSnList != null){
            for (int i = 0; i < skillSnList.size(); i++) {
                int skillSn = skillSnList.get(i);
                if (!isOpenSkillPos(humanObj.getHuman2().getRepSn(), i + skillPosIndex)) {
                    continue;
                }
                int lv = Utils.intValue(skillSnLvMap.get(skillSn));
                activeSkillList.add(to_p_active_skill(skillSn, lv, i + skillPosIndex,
                        Utils.intValue(posTimeMap.get(i + skillPosIndex))).build());
            }
        }
        return activeSkillList;
    }

    public List<Define.p_active_skill> getAllActiveSkill(Profession profession, Human2 human2){
        List<Define.p_active_skill> activeSkillList = new ArrayList<>();
        if(human2 == null){
            return activeSkillList;
        }
        int lineup = human2.getUseSkillLineup();
        SkillData skillData = new SkillData();
        skillData.initSkillDataOffline(human2);
        List<Integer> skillSnList = skillData.lineupMap.get(lineup);
        Map<Integer, Integer> skillSnLvMap = skillData.skillSnLvMap;
        Map<Integer, Map<Integer, Long>> tabDelayMap = skillData.lineupDelayMap;
        Map<Integer, Long> posTimeMap = tabDelayMap.get(lineup);
        if(posTimeMap == null){
            posTimeMap = new HashMap<>();
            tabDelayMap.put(lineup, posTimeMap);
        }
        int delayTime = Utils.intValue(posTimeMap.get(1));
        if(profession != null){
            // 主动技能
            activeSkillList.add(to_p_active_skill(profession.getJobSkillSn(), profession.getJobSkillLv(), 1, delayTime).build());
        }


        if(skillSnList != null){
            for (int i = 0; i < skillSnList.size(); i++) {
                int skillSn = skillSnList.get(i);
                if (!isOpenSkillPos(human2.getRepSn(), i + skillPosIndex)) {
                    continue;
                }
                int lv = Utils.intValue(skillSnLvMap.get(skillSn));
                activeSkillList.add(to_p_active_skill(skillSn, lv, i + skillPosIndex,
                        Utils.intValue(posTimeMap.get(i + skillPosIndex))).build());
            }
        }
        return activeSkillList;
    }

    /** 
     * 获取玩家所有被动技能
     * <AUTHOR>
     * @Date 2024/5/15
     * @Param 
     */
//    public List<Define.p_passive_skill> getAllPassiveSkill(HumanObject humanObj){
//        Human2 human = humanObj.getHuman2();
//        List<Define.p_passive_skill> passiveSkillList = new ArrayList<>();
//        // 被动技能
//        if(humanObj.operation.profession != null){
//            Map<Integer, Integer> passiveSkillSnMap = Utils.jsonToMapIntInt(humanObj.operation.profession.getPassiveSkillMap());
//            for (Map.Entry<Integer, Integer> entry : passiveSkillSnMap.entrySet()) {
//                passiveSkillList.add(to_p_passive_skill(entry.getKey(), entry.getValue()));
//            }
//        }
//        List<Define.p_passive_skill> petList = PetManager.inst().getPetPassiveSkillList(humanObj);
//        if(petList != null){
//            passiveSkillList.addAll(petList);
//        }
//
//        // TODO 理论上还有其他
//        List<Define.p_passive_skill> artifactSkill = ArtifactManager.inst().getPassSkill(humanObj);
//        if(artifactSkill != null){
//            passiveSkillList.addAll(artifactSkill);
//        }
//        List<Define.p_passive_skill> mountSkill = MountManager.inst().getPassSkill(humanObj);
//        if(mountSkill != null){
//            passiveSkillList.addAll(mountSkill);
//        }
//        List<Define.p_passive_skill> wingSkill = WingManager.inst().getPassSkill(humanObj);
//        passiveSkillList.addAll(wingSkill);
//
//        if(humanObj.relic != null){
//            List<Define.p_passive_skill> relicSkillList  = RelicManager.inst().getPassSkill(humanObj.relic);
//            if(relicSkillList != null && relicSkillList.size()!=0){
//                passiveSkillList.addAll(relicSkillList);
//            }
//        }
//        List<Define.p_passive_skill> fateSkillList = FateManager.inst().getPassSkill(human);
//        if (fateSkillList.size() != 0) {
//            passiveSkillList.addAll(fateSkillList);
//        }
//        // 科技被动
//        for (Map.Entry<Integer, Integer> entry : humanObj.operation.sciencePassiveMap.entrySet()) {
//            passiveSkillList.add(to_p_passive_skill(entry.getKey(), entry.getValue()));
//        }
//        return passiveSkillList;
//    }

    public List<Define.p_passive_skill> getAllPassiveSkill(HumanObject humanObj){
        return getAllPassiveSkill(humanObj, humanObj.getHumanExtInfo().getPlan());
    }
    public List<Define.p_passive_skill> getAllPassiveSkill(HumanObject humanObj, int planType){
        Human2 human2 = humanObj.getHuman2();
        List<Define.p_passive_skill> passiveSkillList = new ArrayList<>();
        PlanVo planVo = humanObj.operation.planVoMap.get(planType);
        if(planVo == null){
            planVo = new PlanVo(planType);
        }
        // 被动技能
        if(humanObj.operation.profession != null){
            Map<Integer, Integer> passiveSkillSnMap = Utils.jsonToMapIntInt(humanObj.operation.profession.getPassiveSkillMap());
            for (Map.Entry<Integer, Integer> entry : passiveSkillSnMap.entrySet()) {
                passiveSkillList.add(to_p_passive_skill(entry.getKey(), entry.getValue()));
                int skillSn = entry.getKey();
                if (GlobalConfVal.summonerSkillMap.containsKey(skillSn)) {
                    // 第四职业的被动技能，还要根据同伴决定技能效果，这个时候这个skillSn是个空技能
                    List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(planVo.getTab(PlanVo.TAB_PET));
                    Map<Integer, Integer> jobPetPassSkillMap = PetManager.inst().getJobPetPassSkillMap(skillSn, petSnList);
                    if (jobPetPassSkillMap != null) {
                        for (Map.Entry<Integer, Integer> jobEntry : jobPetPassSkillMap.entrySet()) {
                            passiveSkillList.add(to_p_passive_skill(jobEntry.getKey(), jobEntry.getValue()));
                        }
                    }
                }
            }
        }
        List<Define.p_passive_skill> petList = PetManager.inst().getPetPassiveSkillList(humanObj, planVo.getTab(PlanVo.TAB_PET));
        if(petList != null){
            passiveSkillList.addAll(petList);
        }

        if(humanObj.artifact.atf != null){
            int skillUse = planVo.getTab(PlanVo.TAB_ARTF);
            Map<Long,Long> skinMap = Utils.jsonToMapLongLong(humanObj.artifact.atf.getSkinLvMap());
            int lv = Utils.intValue(skinMap.getOrDefault((long)skillUse, 0L));
            int snLv =  ArtifactManager.inst().getArtifactSnLv(skillUse,lv);
            List<Define.p_passive_skill> artifactSkill = ArtifactManager.inst().getPassSkill(snLv, humanObj.artifact.atf.getLevel());
            if(artifactSkill != null){
                passiveSkillList.addAll(artifactSkill);
            }
        }

        if(humanObj.operation.mount != null){
            int skillUse = planVo.getTab(PlanVo.TAB_MOUNT);
            Map<Integer,Integer> skinLvMap = Utils.jsonToMapIntInt(humanObj.operation.mount.getSkinLvMap());
            int lv = skinLvMap.getOrDefault(skillUse,0);
            int snLv = MountManager.inst().getMountSnLv(skillUse,lv);
            List<Define.p_passive_skill> mountSkill = MountManager.inst().getPassSkill(snLv,humanObj.operation.mount.getLevel());
            if(mountSkill != null){
                passiveSkillList.addAll(mountSkill);
            }
        }
        if(humanObj.operation.wing != null){
            int skillUse = planVo.getTab(PlanVo.TAB_WING);
            Map<Integer, Integer> skinLvMap = Utils.jsonToMapIntInt(humanObj.operation.wing.getSkinLvMap());
            List<Define.p_passive_skill> wingSkill = WingManager.inst().getPassSkill(skillUse, skinLvMap.getOrDefault(skillUse, 0));
            passiveSkillList.addAll(wingSkill);

            int talentTab = planVo.getTab(PlanVo.TAB_WING_TALENT);
            List<Define.p_passive_skill> wingTalenSkill = WingManager.inst().getTalenPassSkill(humanObj.operation.wing, talentTab);
            passiveSkillList.addAll(wingTalenSkill);
        }
        if(humanObj.relic != null){
            int tabRelic = planVo.getTab(PlanVo.TAB_RELIC);
            List<Define.p_passive_skill> relicSkillList  = RelicManager.inst().getPassSkill(humanObj.relic, tabRelic);
            if(relicSkillList != null && relicSkillList.size()!=0){
                passiveSkillList.addAll(relicSkillList);
            }
        }
        List<Define.p_passive_skill> fateSkillList = FateManager.inst().getPassSkill(human2.getFateSkillMap());
        if (fateSkillList != null && !fateSkillList.isEmpty()) {
            passiveSkillList.addAll(fateSkillList);
        }
        // 科技被动
        for (Map.Entry<Integer, Integer> entry : humanObj.operation.sciencePassiveMap.entrySet()) {
            passiveSkillList.add(to_p_passive_skill(entry.getKey(), entry.getValue()));
        }

        Map<Integer, FlyPetInfo> petBriefMap = FlyPetManager.inst().getPlanPetBriefMap(humanObj.operation.human2);
        FlyPetInfo petBrief = petBriefMap.get(planType);
        if (petBrief != null) {
            passiveSkillList.addAll(FlyPetManager.inst().getFlyPetPassSkillList(petBrief));
        }
        //星将被动
        int tabAngel = planVo.getTab(PlanVo.TAB_ANGEL);
        List<Define.p_passive_skill> angelSkillList = AngelManager.inst().getPassiveSkillList(humanObj, tabAngel);
        if (angelSkillList != null && !angelSkillList.isEmpty()) {
            passiveSkillList.addAll(angelSkillList);
        }
        return passiveSkillList;
    }

    public List<Define.p_passive_skill> getAllPassiveSkill(Human2 human2, Profession prf, Artifact atf, Mount mount, Relic relic){
        List<Define.p_passive_skill> passiveSkillList = new ArrayList<>();
        // 被动技能
        if(prf != null){
            Map<Integer, List<Integer>> petLineupMap = Utils.jsonToMapIntListInt(human2.getPetLineupJSON());
            List<Integer> petSnList = petLineupMap.get(human2.getUsePetLineup());
            professionPassive(passiveSkillList, prf.getPassiveSkillMap(), petSnList);
        }
        int effectType = JobsManager.inst().getPetSpecialEffectType(prf.getJobSn());
        List<Define.p_passive_skill> petList = PetManager.inst().getPetPassiveSkillList(human2.getUsePetLineup(), Utils.jsonToMapIntInt(human2.getPetSnLvMap()),
                Utils.jsonToMapIntListInt(human2.getPetLineupJSON()), Utils.jsonToIntMapIntInt(human2.getPetSkinLineupJSON()), Utils.jsonToMapIntInt(human2.getPetSkinSnLvMap()), effectType);
        if(petList != null){
            passiveSkillList.addAll(petList);
        }
        // TODO 理论上还有其他
        if(atf != null){
            List<Define.p_passive_skill> artifactSkill = ArtifactManager.inst().getPassSkill(human2.getArtifactSkillSnLv(),atf.getLevel());
            if(artifactSkill != null){
                passiveSkillList.addAll(artifactSkill);
            }
        }


        if(mount != null){
            List<Define.p_passive_skill> mountSkill = MountManager.inst().getPassSkill(human2.getMountSkillSnLv(),mount.getLevel());
            if(mountSkill != null){
                passiveSkillList.addAll(mountSkill);
            }
        }

        int wingSn = WingManager.inst().getWingSn(human2.getWingSkillSnLv());
        int wingLv = WingManager.inst().getWingLv(human2.getWingSkillSnLv());
        List<Define.p_passive_skill> wingSkill = WingManager.inst().getPassSkill(wingSn, wingLv);
        passiveSkillList.addAll(wingSkill);

        List<Define.p_passive_skill> wingTalenSkill = WingManager.inst().getTalenPassSkill(human2.getWingPassiveMap());
        passiveSkillList.addAll(wingTalenSkill);

        if(relic != null){
            //遗迹方案<方案id，方案信息>map
            Map<Integer,Integer> relicMap = Utils.jsonToMapIntInt(relic.getRelicMap());

            JSONObject json = Utils.toJSONObject(relic.getTabMap());
            List<Integer> relics = null;
            for (Map.Entry<String, Object> entry : json.entrySet()) {
                if(relic.getTabCur() == Utils.intValue(entry.getKey())){
                    JSONObject jsonTemp = Utils.toJSONObject(entry.getValue().toString());
                    relics = jsonTemp.getJSONArray("r").toJavaList(Integer.class);
                    break;
                }
            }
            if(relics != null && !relics.isEmpty()){
                List<Define.p_passive_skill> relicSkillList  = RelicManager.inst().getPassSkill(relics, relicMap);
                if(relicSkillList != null && relicSkillList.size()!=0){
                    passiveSkillList.addAll(relicSkillList);
                }
            }
        }
        List<Define.p_passive_skill> fateSkillList = FateManager.inst().getPassSkill(human2.getFateSkillMap());
        if (fateSkillList != null && fateSkillList.size() != 0) {
            passiveSkillList.addAll(fateSkillList);
        }
        // 科技被动
        Map<Integer, Integer> sciencePassiveMap = Utils.jsonToMapIntInt(human2.getSciencePassiveMap());
        for (Map.Entry<Integer, Integer> entry : sciencePassiveMap.entrySet()) {
            passiveSkillList.add(to_p_passive_skill(entry.getKey(), entry.getValue()));
        }
        return passiveSkillList;
    }



    public List<Define.p_passive_skill> getAllPassiveSkill(JSONObject jo){//Human2 human, Profession prf, Artifact atf, Mount mount, Relic relic){
        List<Define.p_passive_skill> passiveSkillList = new ArrayList<>();
        int petLineup = jo.getIntValue(Human2.K.usePetLineup);
        Map<Integer, List<Integer>> petLineupMap = Utils.jsonToMapIntListInt(jo.getString(Human2.K.petLineupJSON));
        // 被动技能
        Map<Integer, Integer> passiveSkillSnMap = Utils.jsonToMapIntInt(jo.getString(Profession.K.passiveSkillMap));
        for (Map.Entry<Integer, Integer> entry : passiveSkillSnMap.entrySet()) {
            passiveSkillList.add(SkillManager.inst().to_p_passive_skill(entry.getKey(), entry.getValue()));
            if (GlobalConfVal.summonerSkillMap.containsKey(entry.getKey())) {
                // 第四职业的被动技能，还要根据同伴决定技能效果，这个时候这个skillSn是个空技能
                List<Integer> petSnList = petLineupMap.get(petLineup);
                Map<Integer, Integer> jobPetPassSkillMap = PetManager.inst().getJobPetPassSkillMap(entry.getKey(), petSnList);
                if (jobPetPassSkillMap != null) {
                    for (Map.Entry<Integer, Integer> jobEntry : jobPetPassSkillMap.entrySet()) {
                        passiveSkillList.add(SkillManager.inst().to_p_passive_skill(jobEntry.getKey(), jobEntry.getValue()));
                    }
                }
            }
        }

        int effectType = JobsManager.inst().getPetSpecialEffectType(jo.getIntValue(Profession.K.jobSn));
        List<Define.p_passive_skill> petSkillList = PetManager.inst().getPetPassiveSkillList(petLineup, Utils.jsonToMapIntInt(jo.getString(Human2.K.petSnLvMap)),
                petLineupMap, Utils.jsonToIntMapIntInt(jo.getString(Human2.K.petSkinLineupJSON)), Utils.jsonToMapIntInt(jo.getString(Human2.K.petSkinSnLvMap)), effectType);
        if (petSkillList != null && petSkillList.isEmpty()) {
            passiveSkillList.addAll(petSkillList);
        }

        // TODO 理论上还有其他
        List<Define.p_passive_skill> artifactSkill = ArtifactManager.inst().getPassSkill(jo.getIntValue(Human2.K.artifactSkillSnLv), jo.getIntValue(Artifact.K.level));
        if (artifactSkill != null) {
            passiveSkillList.addAll(artifactSkill);
        }
        List<Define.p_passive_skill> mountSkill = MountManager.inst().getPassSkill(jo.getIntValue(Human2.K.mountSkillSnLv), jo.getIntValue(Mount.tableName+Mount.K.level));
        if (mountSkill != null) {
            passiveSkillList.addAll(mountSkill);
        }


        int wingSn = WingManager.inst().getWingSn(jo.getIntValue(Human2.K.wingSkillSnLv));
        int wingLv = WingManager.inst().getWingLv(jo.getIntValue(Human2.K.wingSkillSnLv));
        List<Define.p_passive_skill> wingSkill = WingManager.inst().getPassSkill(wingSn, wingLv);
        passiveSkillList.addAll(wingSkill);


        //遗迹方案<方案id，方案信息>map
        Map<Integer,Integer> relicMap = Utils.jsonToMapIntInt(jo.getString(Relic.K.relicMap));

        int tabCur = jo.getIntValue(Relic.K.tabCur);
        JSONObject json = Utils.toJSONObject(jo.getString(Relic.K.tabMap));
        List<Integer> relics = null;
        for (Map.Entry<String, Object> entry : json.entrySet()) {
            if(tabCur == Utils.intValue(entry.getKey())){
                JSONObject jsonTemp = Utils.toJSONObject(entry.getValue().toString());
                relics = jsonTemp.getJSONArray("r").toJavaList(Integer.class);
                break;
            }
        }
        if(relics != null && !relics.isEmpty()){
            List<Define.p_passive_skill> relicSkillList  = RelicManager.inst().getPassSkill(relics, relicMap);
            if(relicSkillList != null && relicSkillList.size()!=0){
                passiveSkillList.addAll(relicSkillList);
            }
        }
        List<Define.p_passive_skill> fateSkillList = FateManager.inst().getPassSkill(jo.getString(Human2.K.fateSkillMap));
        if (fateSkillList.size() != 0) {
            passiveSkillList.addAll(fateSkillList);
        }
        // 科技被动
        Map<Integer, Integer> sciencePassiveMap = Utils.jsonToMapIntInt(jo.getString(Human2.K.sciencePassiveMap));
        for (Map.Entry<Integer, Integer> entry : sciencePassiveMap.entrySet()) {
            passiveSkillList.add(SkillManager.inst().to_p_passive_skill(entry.getKey(), entry.getValue()));
        }
        return passiveSkillList;
    }


    public void professionPassive(List<Define.p_passive_skill> passiveSkillList, String json, List<Integer> petSnList){
        // 被动技能
        if(json != null && !json.isEmpty()){
            Map<Integer, Integer> passiveSkillSnMap = Utils.jsonToMapIntInt(json);
            for (Map.Entry<Integer, Integer> entry : passiveSkillSnMap.entrySet()) {
                passiveSkillList.add(to_p_passive_skill(entry.getKey(), entry.getValue()));
                int skillSn = entry.getKey();
                if (GlobalConfVal.summonerSkillMap.containsKey(skillSn)) {
                    // 第四职业的被动技能，还要根据同伴决定技能效果，这个时候这个skillSn是个空技能
                    Map<Integer, Integer> jobPetPassSkillMap = PetManager.inst().getJobPetPassSkillMap(skillSn, petSnList);
                    if (jobPetPassSkillMap!= null) {
                        for (Map.Entry<Integer, Integer> jobEntry : jobPetPassSkillMap.entrySet()) {
                            passiveSkillList.add(to_p_passive_skill(jobEntry.getKey(), jobEntry.getValue()));
                        }
                    }
                }
            }
        }
    }

    public Define.p_passive_skill to_p_passive_skill(int skillSn, int level) {
        Define.p_passive_skill.Builder dInfo = Define.p_passive_skill.newBuilder();
        dInfo.setSkillId(skillSn);
        dInfo.setSkillLv(level);
        return dInfo.build();
    }

    public Define.p_active_skill.Builder to_p_active_skill(int skillSn, int lv, int pos, int delayTime) {
        Define.p_active_skill.Builder dInfo = Define.p_active_skill.newBuilder();
        dInfo.setSkillId(skillSn);
        dInfo.setSkillLv(lv);
        dInfo.setPosId(pos);
        dInfo.setDelayTime(delayTime);
        return dInfo;
    }

    public void sendMsg_skill_active_update_s2c(HumanObject humanObj) {
        MsgSkill.skill_active_update_s2c.Builder msg = MsgSkill.skill_active_update_s2c.newBuilder();
        SkillData skillData = humanObj.operation.skillData;
        int lineup = humanObj.getHuman2().getUseSkillLineup();
        if(lineup <= 0){
            lineup = 1;
            humanObj.getHuman2().setUseSkillLineup(lineup);
        }
        List<Integer> skillSnList = skillData.lineupMap.get(lineup);
        Map<Integer, Integer> skillSnLvMap = skillData.skillSnLvMap;
        Map<Integer, Map<Integer, Long>> tabDelayMap = skillData.lineupDelayMap;
        Map<Integer, Long> posTimeMap = tabDelayMap.get(lineup);

        Profession profession = humanObj.operation.profession;
        // 主动技能
        int delayTime = Utils.intValue(posTimeMap.get(1));
        msg.addActiveSkills(to_p_active_skill(profession.getJobSkillSn(), profession.getJobSkillLv(), 1, delayTime).build());
        // 主动技能
        for (int i = 0; i < skillSnList.size(); i++) {
            int skillSn = skillSnList.get(i);

            if (!isOpenSkillPos(humanObj.getHuman2().getRepSn(), i + skillPosIndex)) {
                continue;
            }
            int lv = Utils.intValue(skillSnLvMap.get(skillSn));
            msg.addActiveSkills(to_p_active_skill(skillSn, lv, i + skillPosIndex, Utils.intValue(posTimeMap.get(i + skillPosIndex))));
        }
        humanObj.sendMsg(msg);
    }


    public void sendMsg_skill_passive_update_s2c(HumanObject humanObj) {
        MsgSkill.skill_passive_update_s2c.Builder msg = MsgSkill.skill_passive_update_s2c.newBuilder();

        // 玩家被动
        Map<Integer, Integer> passiveSkillSnMap = Utils.jsonToMapIntInt(humanObj.operation.profession.getPassiveSkillMap());
        for (Map.Entry<Integer, Integer> entry : passiveSkillSnMap.entrySet()) {
            Define.p_passive_skill.Builder dPassive = Define.p_passive_skill.newBuilder();
            dPassive.setSkillId(entry.getKey());
            dPassive.setSkillLv(entry.getValue());
            msg.addUpdateList(dPassive);
            if (GlobalConfVal.summonerSkillMap.containsKey(entry.getKey())) {
                // 第四职业的被动技能，还要根据同伴决定技能效果，这个时候这个skillSn是个空技能
                List<Integer> petSnList = humanObj.operation.petData.lineupMap.get(humanObj.getHuman2().getUsePetLineup());
                Map<Integer, Integer> jobPetPassSkillMap = PetManager.inst().getJobPetPassSkillMap(entry.getKey(), petSnList);
                if (jobPetPassSkillMap != null) {
                    for (Map.Entry<Integer, Integer> jobEntry : jobPetPassSkillMap.entrySet()) {
                        dPassive = Define.p_passive_skill.newBuilder();
                        dPassive.setSkillId(jobEntry.getKey());
                        dPassive.setSkillLv(jobEntry.getValue());
                        msg.addUpdateList(dPassive);
                    }
                }
            }
        }
        // 上阵伙伴被动
        msg.addAllUpdateList(pet_passive_skill_list(humanObj));

        humanObj.sendMsg(msg);
    }

    public void sendMsg_skill_passive_update_s2c(HumanObject humanObj, List<Define.p_passive_skill> updateList, List<Define.p_passive_skill> delList) {
        MsgSkill.skill_passive_update_s2c.Builder msg = MsgSkill.skill_passive_update_s2c.newBuilder();
        if (updateList != null && delList != null && !updateList.isEmpty() && !delList.isEmpty()) {
            Set<Define.p_passive_skill> updateSet = new HashSet<>(updateList);
            delList.removeIf(skill -> updateSet.stream().anyMatch(
                    updatedSkill -> isSameSkill(skill, updatedSkill)
            ));
        }
        // 上阵伙伴被动
        if(updateList != null){
            msg.addAllUpdateList(updateList);
        }
        // 删掉伙伴被动
        if(delList != null){
            msg.addAllDeleteList(delList);
        }

        humanObj.sendMsg(msg);
    }

    private boolean isSameSkill(Define.p_passive_skill a, Define.p_passive_skill b) {
        return a.getSkillId() == b.getSkillId() && a.getSkillLv() == b.getSkillLv();
    }

    public List<Define.p_passive_skill> pet_passive_skill_list(HumanObject humanObj) {
        int tab = humanObj.getHuman2().getUsePetLineup();
        PetData petData = humanObj.operation.petData;
        List<Integer> petSnList = petData.lineupMap.get(tab);
        Map<Integer, Integer> petSkinSnMap = petData.petSkinLineupMap.getOrDefault(tab, new HashMap<>(0));
        int effectType = JobsManager.inst().getPetSpecialEffectType(humanObj.getProfession());
        return pet_passive_skill_list(petData.petSnLvMap, petSnList, petSkinSnMap, petData.petSkinLvMap, effectType);
    }

    public List<Define.p_passive_skill> pet_passive_skill_list(Map<Integer, Integer> petSnLvMap, List<Integer> petSnList,
                                                               Map<Integer, Integer> petSkinSnMap, Map<Integer, Integer> petSkinLvMap, int effectType) {
        List<Define.p_passive_skill> passiveList = new ArrayList<>();
        Map<Integer, Integer> typeEffectMap = new HashMap<>();
        for (int petSn : petSnList) {
            if (petSn <= 0) {
                continue;
            }
            int[][] effect = null;
            int skinSn = petSkinSnMap.getOrDefault(petSn, 0);
            int skinLv = petSkinLvMap.getOrDefault(skinSn, 0);
            if (skinSn > 0 && skinLv > 0) {
                ConfPetSkinlevel conf = ConfPetSkinlevel.get(skinSn, skinLv);
                if (conf != null) {
                    effect = effectType != 0 ? conf.equipEffect1 : conf.equipEffect;
                }
            }
            if (effect == null) {
                ConfPetlevel_0 conf = ConfPetlevel_0.get(petSn, petSnLvMap.get(petSn));
                if (conf == null) {
                    Log.temp.error("===ConfPetlevel_0 配表错误, petSn={}, lv={}", petSn, petSnLvMap.get(petSn));
                    continue;
                }
                if (conf.equipEffect == null) {
                    Log.temp.error("===ConfPetlevel_0 配表错误，equipEffect.length={}, sn={}, lv={}", conf.equipEffect.length, petSn, petSnLvMap.get(petSn));
                    continue;
                }
                effect = effectType != 0 ? conf.equipEffect1 : conf.equipEffect;
            }
            for (int[] ints : effect) {
                int effectSn = ints[0];
                int effectLv = ints[1];
                typeEffectMap.put(effectSn, Math.max(effectLv, typeEffectMap.getOrDefault(effectSn, 0)));
            }
        }
        for (Map.Entry<Integer, Integer> entry : typeEffectMap.entrySet()) {
            passiveList.add(to_p_passive_skill(entry.getKey(), entry.getValue()));
        }
        return passiveList;
    }

    /**
     * TODO 注意只能单个
     * @param petSnLvMap
     * @param petSn
     * @return
     */
    public List<Define.p_passive_skill> to_p_passive_skill(int petSn, int petLevel, int skinSn, int skinLevel, int effectType) {
        List<Define.p_passive_skill> passiveList = new ArrayList<>();
        if (petSn <= 0) {
            return passiveList;
        }
        if (skinSn > 0 && skinLevel > 0) {
            ConfPetSkinlevel conf = ConfPetSkinlevel.get(skinSn, skinLevel);
            if (conf == null) {
                Log.temp.error("===ConfPetSkinlevel 配表错误, skinSn={}, skinLv={}", skinSn, skinLevel);
                return passiveList;
            }
            if (effectType != 0) {
                for (int i = 0; i < conf.equipEffect1.length; i++) {
                    passiveList.add(to_p_passive_skill(conf.equipEffect1[i][0], conf.equipEffect1[i][1]));
                }
            } else {
                for (int i = 0; i < conf.equipEffect.length; i++) {
                    passiveList.add(to_p_passive_skill(conf.equipEffect[i][0], conf.equipEffect[i][1]));
                }
            }
        } else {
            ConfPetlevel_0 conf = ConfPetlevel_0.get(petSn, petLevel);
            if (conf == null) {
                return passiveList;
            }
            if (effectType != 0) {
                for (int i = 0; i < conf.equipEffect1.length; i++) {
                    passiveList.add(to_p_passive_skill(conf.equipEffect1[i][0], conf.equipEffect1[i][1]));
                }
            } else {
                for (int i = 0; i < conf.equipEffect.length; i++) {
                    passiveList.add(to_p_passive_skill(conf.equipEffect[i][0], conf.equipEffect[i][1]));
                }
            }
        }
        return passiveList;
    }

    public void sendMsg_skill_passive_update_s2c(HumanObject humanObj, List<Integer> snList) {
        MsgSkill.skill_passive_update_s2c.Builder msg = MsgSkill.skill_passive_update_s2c.newBuilder();
        Map<Integer, Integer> skillLvMap = humanObj.operation.skillData.skillSnLvMap;
        for (int sn : snList) {
            msg.addUpdateList(to_p_passive_skill(sn, Utils.intValue(skillLvMap.get(sn))));
        }
        humanObj.sendMsg(msg);
    }

    public void _msg_skill_system_info_c2s(HumanObject humanObj) {
        Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillSnLvMap;
        MsgSkill.skill_system_info_s2c.Builder msg = MsgSkill.skill_system_info_s2c.newBuilder();
        for (Map.Entry<Integer, Integer> entry : skillSnLvMap.entrySet()) {
            msg.addSkills(to_p_skill_system(entry.getKey(), entry.getValue()));
        }
        humanObj.sendMsg(msg);
    }

    private Define.p_skill_system to_p_skill_system(int skillSn, int level) {
        Define.p_skill_system.Builder dInfo = Define.p_skill_system.newBuilder();
        dInfo.setSkillId(skillSn);
        dInfo.setSkillLv(level);
        return dInfo.build();
    }

    private void addSkillOnekey(HumanObject humanObj) {
        List<Integer> skillSnList = new ArrayList<>();
        List<Integer> snList = new ArrayList<>(humanObj.operation.skillData.skillSnLvMap.keySet());
        for (int skillSn : snList) {
            if (isAddLvOnekey(humanObj, skillSn)) {
                skillSnList.add(skillSn);
            }
        }

        sendMsg_skill_system_update_s2c(humanObj, skill_system_update_type_2, skillSnList);
        sendMsg_skill_active_update_s2c(humanObj);

        updatePower(humanObj);
        updateSkillPorpCalc(humanObj);
    }

    private boolean isAddLvOnekey(HumanObject humanObj, int skillSn) {
        int level = humanObj.operation.skillData.skillSnLvMap.getOrDefault(skillSn, 0);
        ConfSkillLevel_0 confLv = ConfSkillLevel_0.get(skillSn, level);
        if (confLv == null) {
            Log.temp.error("===ConfSkillLevel_0 配表错误, skillSn={}, level ={}", skillSn, level);
            return false;
        }

        ConfSkillLevel_0 confLvNext = ConfSkillLevel_0.get(skillSn, level + 1);
        if (confLvNext == null) {
            Log.temp.error("===ConfSkillLevel_0 配表错误, skillSn={}, level ={}", skillSn, level + 1);
            return false;
        }

        ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, confLvNext.expend);
        if (!result.success) {
            return false;
        }
        int lvOld = level;
        while (result.success) {
            ProduceManager.inst().costIntArr(humanObj, confLvNext.expend, MoneyItemLogKey.同伴升级);
            level++;
            confLvNext = ConfSkillLevel_0.get(skillSn, level + 1);
            if (confLvNext == null) {
                Log.temp.error("===ConfSkillLevel_0 配表错误, skillSn={}, level ={}", skillSn, level + 1);
                break;
            }
            result = ProduceManager.inst().canCostIntArr(humanObj, confLvNext.expend);
        }
        humanObj.operation.skillData.skillSnLvMap.put(skillSn, level);

        if(!humanObj.getHuman3().isAddLvSkill()){
            humanObj.getHuman3().setAddLvSkill(true);
        }

        Event.fire(EventKey.TASK_CONDITION_TYPE, "humanObj", humanObj, "type", TaskConditionTypeKey.TASK_主线,
                "taskType", TaskConditionTypeKey.TASK_TYPE_13, "paramType", TaskConditionTypeKey.PARAM_TYPE_1, "addValue", level - lvOld);
        return true;
    }


    /**
     * 技能升级
     *
     * <AUTHOR>
     * @Date 2024/3/14
     * @Param
     */
    public void _msg_skill_lv_c2s(HumanObject humanObj, List<Integer> skillSnList) {
        for (int skillSn : skillSnList) {
            isAddLvOnekey(humanObj, skillSn);
        }
        sendMsg_skill_system_update_s2c(humanObj, skill_system_update_type_2, skillSnList);
        sendMsg_skill_active_update_s2c(humanObj);

        updatePower(humanObj);
        updateSkillPorpCalc(humanObj);
        humanObj.operation.skillData.saveData(humanObj);
    }

    public void sendMsg_skill_system_update_s2c(HumanObject humanObj, int type, List<Integer> skillSnList) {
        Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillSnLvMap;
        MsgSkill.skill_system_update_s2c.Builder msg = MsgSkill.skill_system_update_s2c.newBuilder();
        msg.setType(type);// TODO
        for (int skillSn : skillSnList) {
            msg.addSkills(to_p_skill_system(skillSn, Utils.intValue(skillSnLvMap.get(skillSn))));
        }
        humanObj.sendMsg(msg);
    }

    public int skillAddLv(HumanObject humanObj, int skillSn) {
        int num = 0;
        Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillSnLvMap;
        if (!skillSnLvMap.containsKey(skillSn)) {
            return num;
        }
        int level = skillSnLvMap.get(skillSn);
        ConfSkillLevel_0 conf = ConfSkillLevel_0.get(skillSn, level);
        if (conf == null) {
            Log.temp.error("===ConfSkillLevel_0 配表错误, skillSn={}, level ={}", skillSn, level);
            return num;
        }
        ConfSkillLevel_0 confLvNext = ConfSkillLevel_0.get(skillSn, level + 1);
        if (confLvNext == null) {
            Log.temp.error("===ConfSkillLevel_0 配表错误, skillSn={}, level ={}", skillSn, level + 1);
            return num;
        }
        ReasonResult result = ProduceManager.inst().canCostIntArr(humanObj, confLvNext.expend);
        if (!result.success) {
            return num;
        }
        ProduceManager.inst().costIntArr(humanObj, confLvNext.expend, MoneyItemLogKey.技能升级);
        level++;
        skillSnLvMap.put(skillSn, level);
        updatePower(humanObj);
        // TODO 属性
        updateSkillPorpCalc(humanObj);
        return 1;
    }

    public void updateSkillPorpCalc(HumanObject humanObj) {
        PropCalc propCalc = new PropCalc();
        Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillSnLvMap;
        for (Map.Entry<Integer, Integer> entry : skillSnLvMap.entrySet()) {
            int sn = entry.getKey();
            int lv = entry.getValue();
            ConfSkillLevel_0 conf = ConfSkillLevel_0.get(sn, lv);
            if (conf == null) {
                Log.temp.error("===ConfSkillLevel_0 配表错误, sn={}, level ={}", sn, lv);
                continue;
            }
            propCalc.plus(conf.ownEffect);
        }
        //Log.temp.info("==+{}", propCalc);
        humanObj.dataPers.unitPropPlus.setFieldRead(UnitPropPlus.K.skill, propCalc.toJSONStr());
        PropManager.inst().propCalc(humanObj, CombatChangeLog.技能);
    }


    /**
     * 图鉴激活
     *
     * <AUTHOR>
     * @Date 2024/3/14
     * @Param
     */
    public boolean skillIllustrated(HumanObject humanObj, int sn, int level) {
        ConfIllustrated_0 conf = ConfIllustrated_0.get(sn, level);
        if (conf == null) {
            Log.temp.error("===ConfIllustrated_0配表错误， sn={}, level={}", sn, level);
            return false;
        }
        ConfIllustrated_0 confNext = ConfIllustrated_0.get(sn, level + 1);
        if (confNext == null) {
            Log.temp.error("===ConfIllustrated_0配表错误， sn={}, level={}", sn, level+ 1);
            return false;
        }
        boolean result = HumanManager.inst().isMeetIllustrated(humanObj, conf.condition);
        if (!result) {
            return false;
        }
        int nowLv = humanObj.operation.skillData.skillIllustratedSnLvMap.getOrDefault(sn, 0);
        if (nowLv != level) {
            return true;// 因为外面是循环，一次点击升到能升为止
        }
        humanObj.operation.skillData.skillIllustratedSnLvMap.put(sn, nowLv + 1);
        return true;
    }

    public void _msg_skill_equip_c2s(HumanObject humanObj, List<Define.p_key_value> dInfoList) {
        int useSkillLineup = humanObj.getHuman2().getUseSkillLineup();
        Map<Integer, List<Integer>> lineupMap = humanObj.operation.skillData.lineupMap;
        List<Integer> snList = new ArrayList<>(lineupMap.get(useSkillLineup));
        while (snList.size() < SkillManager.posMax){
            snList.add(0);
        }
        Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillSnLvMap;

        for (Define.p_key_value dInfo : dInfoList) {
            int index = (int) dInfo.getK() - skillPosIndex;
            int sn = (int) dInfo.getV();

            if (index < 0 || index >= posMax) {
                Log.temp.error("===技能位置错误, index={}, sn={}", index, sn);
                continue;
            }
            snList.set(index, 0);// 先去掉原来的，避免技能位置改变
        }

        Set<Integer> useSnSet = new HashSet<>(snList); // 使用 Set 去重

        for (Define.p_key_value dInfo : dInfoList) {
            int index = (int) dInfo.getK() - skillPosIndex;
            int sn = (int) dInfo.getV();

            if(index < 0 || index >= posMax){
                Log.temp.error("===技能位置错误, index={}, sn={}", index, sn);
                continue;
            }

            if (sn <= 0) {
                snList.set(index, sn);
                continue;
            }
            if (useSnSet.contains(sn) || !skillSnLvMap.containsKey(sn)) {
                return;
            }
            useSnSet.add(sn);
            snList.set(index, sn);
        }
        lineupMap.put(useSkillLineup, snList);
        humanObj.getHuman2().setSkillLineupJSON(Utils.mapIntListIntToJSON(lineupMap));

        sendMsg_skill_tab_info_update_s2c(humanObj, useSkillLineup);
        sendMsg_skill_active_update_s2c(humanObj);

        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_65, 0);

        humanObj.operation.skillData.saveData(humanObj);
        updatePower(humanObj);
        HumanManager.inst().sendMsg_role_total_sp_info_s2c(humanObj, 0);
    }




    public void _msg_skill_tab_info_c2s(HumanObject humanObj) {
        MsgSkill.skill_tab_info_s2c.Builder msg = MsgSkill.skill_tab_info_s2c.newBuilder();
        msg.setTab(humanObj.getHuman2().getUseSkillLineup());
        Map<Integer, List<Integer>> lineupMap = humanObj.operation.skillData.lineupMap;
        for (int tab : lineupMap.keySet()) {
            msg.addTabList(to_p_skill_tab_info(humanObj, tab));
        }
        humanObj.sendMsg(msg);
        HumanManager.inst().sendMsg_role_total_sp_info_s2c(humanObj, 0);
    }

    public Define.p_skill_tab_info.Builder to_p_skill_tab_info(HumanObject humanObj, int tab) {
        Map<Integer, String> tabNameMap = Utils.jsonToMapIntString(humanObj.getHuman3().getSkillTabNameJSON());
        List<Integer> skillSnList = getSkillSnList(humanObj, tab);
        Define.p_skill_tab_info.Builder dInfo = Define.p_skill_tab_info.newBuilder();
        dInfo.setTab(tab);
        if (tabNameMap == null || !tabNameMap.containsKey(tab)) {
            dInfo.setName("");
        } else {
            dInfo.setName(tabNameMap.get(tab));
        }
        for (int i = 0; i < skillSnList.size(); i++) {
            Define.p_key_value.Builder posInfo = Define.p_key_value.newBuilder();
            posInfo.setK(i + skillPosIndex);
            posInfo.setV(skillSnList.get(i));
            dInfo.addPosInfo(posInfo);
        }
        // TODO

        Map<Integer, Map<Integer, Long>> tabDelayMap = humanObj.operation.skillData.lineupDelayMap;
        Map<Integer, Long> posTimeMap = tabDelayMap.get(tab);
        for (Map.Entry<Integer, Long> entry : posTimeMap.entrySet()) {
            dInfo.addDelayTimeList(HumanManager.inst().to_p_key_value(entry.getKey(), entry.getValue()));
        }
        return dInfo;
    }

    public List<Integer> getSkillSnList(HumanObject humanObj, int tab) {
        Map<Integer, List<Integer>> lineupMap = humanObj.operation.skillData.lineupMap;
        List<Integer> skillSnList = lineupMap.get(tab);
        if (skillSnList == null) {
            skillSnList = new ArrayList<>();
        }
        return skillSnList;
    }

    public void _msg_skill_choose_tab_c2s(HumanObject humanObj, int tab) {
        if (tab <= 0 || tab > lineupMax) {
            return;
        }
        humanObj.getHuman2().setUseSkillLineup(tab);
        SkillData skillData = humanObj.operation.skillData;
        if (!skillData.lineupMap.containsKey(tab)) {
            List<Integer> snList = new ArrayList<>();
            for (int m = 0; m < SkillManager.posMax; m++) {
                snList.add(0);
            }
            skillData.lineupMap.put(tab, snList);
        }

        MsgSkill.skill_choose_tab_s2c.Builder msg = MsgSkill.skill_choose_tab_s2c.newBuilder();
        msg.setNewTab(tab);
        humanObj.sendMsg(msg);

        sendMsg_skill_active_update_s2c(humanObj);

        humanObj.operation.skillData.saveData(humanObj);

        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_65, 0);
        updatePower(humanObj);
        HumanManager.inst().sendMsg_role_total_sp_info_s2c(humanObj, 0);
        HumanManager.inst().setPlanTab(humanObj, PlanVo.TAB_SKILL, tab);
    }

    public void _msg_skill_change_tab_name_c2s(HumanObject humanObj, int tab, String name) {
        Map<Integer, String> tabNameMap = Utils.jsonToMapIntString(humanObj.getHuman3().getSkillTabNameJSON());
        tabNameMap.put(tab, name);
        humanObj.getHuman3().setSkillTabNameJSON(Utils.mapIntStrToJSON(tabNameMap));

        MsgSkill.skill_change_tab_name_s2c.Builder msg = MsgSkill.skill_change_tab_name_s2c.newBuilder();
        msg.setTab(tab);
        msg.setName(name);
        humanObj.sendMsg(msg);
    }

    public void _msg_skill_set_delay_time_c2s(HumanObject humanObj, int tab, List<Define.p_key_value> setDelayTimeList) {
        // TODO
        int delayPosMax = humanObj.operation.skillData.delayPosMax;
        Map<Integer, Map<Integer, Long>> tabDelayMap = humanObj.operation.skillData.lineupDelayMap;
        Map<Integer, Long> delayMap = tabDelayMap.get(tab);
        if (delayMap == null) {
            delayMap = new HashMap<>();
            tabDelayMap.put(tab, delayMap);
        }
        for (Define.p_key_value info : setDelayTimeList) {
            int pos = Utils.intValue(info.getK());
            if (pos <= 0 || pos > delayPosMax || info.getV() < 0) {
                continue;
            }
            delayMap.put(Utils.intValue(info.getK()), info.getV());
        }
        humanObj.getHuman2().setSkillDelayJSON(Utils.mapIntMapIntLongToJSON(tabDelayMap));
        sendMsg_skill_set_delay_time_s2c(humanObj, tab, delayMap);
        sendMsg_skill_active_update_s2c(humanObj);

        humanObj.operation.skillData.saveData(humanObj);
    }

    private void sendMsg_skill_set_delay_time_s2c(HumanObject humanObj, int tab, Map<Integer, Long> delayMap) {
        MsgSkill.skill_set_delay_time_s2c.Builder msg = MsgSkill.skill_set_delay_time_s2c.newBuilder();
        msg.setTab(tab);
        for (Map.Entry<Integer, Long> entry : delayMap.entrySet()) {
            Define.p_key_value.Builder dInfo = Define.p_key_value.newBuilder();
            dInfo.setK(entry.getKey());
            dInfo.setV(entry.getValue());
            msg.addDelayTimeList(dInfo);
        }
        humanObj.sendMsg(msg);
    }


    public void updatePower(HumanObject humanObj) {
        long powerLv = 0;
        Map<Integer, Integer> skillSnLvMap = humanObj.operation.skillData.skillSnLvMap;
        // 主动技能
        for(Map.Entry<Integer, Integer> entry : skillSnLvMap.entrySet()){
            ConfSkillLevel_0 confSkillLevel_0 = ConfSkillLevel_0.get(entry.getKey(), entry.getValue());
            if(confSkillLevel_0 == null){
                continue;
            }
            powerLv += confSkillLevel_0.power;
        }

        // 被动
        Map<Integer, Integer> passiveSkillSnMap = Utils.jsonToMapIntInt(humanObj.operation.profession.getPassiveSkillMap());
        for(Map.Entry<Integer, Integer> entry : passiveSkillSnMap.entrySet()){
            ConfSkillLevel_0 confSkillLevel_0 = ConfSkillLevel_0.get(entry.getKey(), entry.getValue());
            if(confSkillLevel_0 == null){
                continue;
            }
            powerLv += confSkillLevel_0.power;
        }
        HumanManager.inst().updatePowerPar(humanObj, EModule.SkilLv, powerLv);

    }


    public void sendMsg_skill_tab_info_update_s2c(HumanObject humanObj, int tab) {
        MsgSkill.skill_tab_info_update_s2c.Builder msg = MsgSkill.skill_tab_info_update_s2c.newBuilder();
        msg.setTab(tab);
        List<Integer> skillSnList = getSkillSnList(humanObj, tab);
        for (int i = 0; i < skillSnList.size(); i++) {
            msg.addPosInfo(HumanManager.inst().to_p_key_value(i + skillPosIndex, skillSnList.get(i)));
        }
        humanObj.sendMsg(msg);
    }

    public boolean isOpenSkillPos(int repSn, int pos) {
        ConfSkillPos conf = ConfSkillPos.get(pos);
        if (conf == null) {
            return false;
        }
        if (conf.type == 0) {
            return true;
        }
        if (conf.condition == null) {
            return true;
        }
        int conditionType = conf.condition[0];
        if (conditionType == ParamKey.conditionType3) {
            int value = conf.condition[1];
            if (repSn > value) {
                return true;
            }
        }
        return false;
    }


    @Listener(EventKey.HUMAN_LOGIN_FINISH)
    public void _on_HUMAN_LOGIN_FINISH(Param param) {
        HumanObject humanObj = param.get("humanObj");
        checkSkillPos(humanObj);
    }

    public void checkSkillPos(HumanObject humanObj) {
        // 首次通关才处理
        int delayPosMax = humanObj.operation.skillData.delayPosMax;
        boolean isUp = false;
        for (int i = 2; i <= delayPosMax; i++) {
            if (humanObj.skillOpenPosList.contains(i)) {
                continue;
            }
            if (isOpenSkillPos(humanObj.getHuman2().getRepSn(), i)) {
                isUp = true;
                humanObj.skillOpenPosList.add(i);
            }
        }
        if (isUp) {
            sendMsg_skill_active_update_s2c(humanObj);
        }
    }

    public List<Define.p_attr_obj_list> to_p_attr_obj_list(List<Define.p_passive_skill> passiveSkillList,
                                                           List<Integer> petSnList, List<Integer> activeSkills, List<Define.p_key_value> attList){
        List<Define.p_attr_obj_list> p_attr_objListAll = new ArrayList<>();
        if (petSnList == null) {
            petSnList = new ArrayList<>();
        }
        Map<Integer, Map<Integer, Integer>> petSnKeyValueMap = new HashMap<>();
        Map<Integer, Map<Integer, Integer>> skillSnKeyValueMap = new HashMap<>();
        getPassiveMap(passiveSkillList, petSnList, activeSkills, petSnKeyValueMap, skillSnKeyValueMap);

        if(!petSnKeyValueMap.containsKey(0)){
            petSnKeyValueMap.put(0, null);
        }
        for(Map.Entry<Integer, Map<Integer, Integer>> entry : petSnKeyValueMap.entrySet()){
            int petSn = entry.getKey();
            // 1. 初始化属性列表（直接复制 attList）
            List<Define.p_key_value> attrList = new ArrayList<>();
            if(petSn == 0){//属性放0中，通用给所有伙伴加
                attrList.addAll(attList);
            }

            Map<Integer, Integer> keyValueMap = entry.getValue();
            if (keyValueMap == null || keyValueMap.isEmpty()) {
                p_attr_objListAll.add(HumanManager.inst().to_p_attr_obj_list(ParamKey.attrType_1, petSn, attrList));
                continue;
            }

            // 2. 用 HashMap 临时存储并合并相同 key 的 value（使用 Long 类型）
            Map<Integer, Long> mergedMap = new HashMap<>();

            // 2.1 先把 attrList 里的属性放进 mergedMap（getV() 是 long）
            for (Define.p_key_value attr : attrList) {
                int key = (int) attr.getK();  // 假设 getK() 返回的是 long，强转成 int（如果 key 是 int）
                long value = attr.getV();      // getV() 返回 long
                mergedMap.merge(key, value, Long::sum);  // 合并时用 Long::sum
            }

            // 2.2 再把 keyValueMap 里的属性合并进去（value 是 Integer，需转成 long）
            for (Map.Entry<Integer, Integer> kv : keyValueMap.entrySet()) {
                int key = kv.getKey();
                long value = kv.getValue();  // Integer 自动转 long
                mergedMap.merge(key, value, Long::sum);  // 相同 key 的 value 相加
            }

            // 3. 重新生成 attrList（确保 to_p_key_value 支持 long 参数）
            attrList.clear();
            for (Map.Entry<Integer, Long> kv : mergedMap.entrySet()) {
                int key = kv.getKey();
                long value = kv.getValue();
                attrList.add(HumanManager.inst().to_p_key_value(key, value).build()); // 假设 to_p_key_value 支持 long
            }

            // 4. 添加到结果
            p_attr_objListAll.add(HumanManager.inst().to_p_attr_obj_list(ParamKey.attrType_1, petSn, attrList));

        }
        for (Map.Entry<Integer, Map<Integer, Integer>> entry : skillSnKeyValueMap.entrySet()) {
            int skillSn = entry.getKey();
            Map<Integer, Integer> keyValueMap = entry.getValue();
            if (keyValueMap.size() == 0) {
                continue;
            }
            List<Define.p_key_value> attrList = new ArrayList<>();
            for (Map.Entry<Integer, Integer> keyValueEntry : keyValueMap.entrySet()) {
                int key = keyValueEntry.getKey();
                int value = keyValueEntry.getValue();
                attrList.add(HumanManager.inst().to_p_key_value(key, value).build());
            }
            p_attr_objListAll.add(HumanManager.inst().to_p_attr_obj_list(ParamKey.attrType_2, skillSn, attrList));
        }
        return p_attr_objListAll;
    }

    private void getPassiveMap(List<Define.p_passive_skill> passiveSkillList, List<Integer> petSnList,
                      List<Integer> activeSkills, Map<Integer, Map<Integer, Integer>> petSnKeyValueMap,
                      Map<Integer, Map<Integer, Integer>> skillSnKeyValueMap){
        if(petSnList == null){
            petSnList = new ArrayList<>();
        }

        //40331遗物的特殊处理
        ConfGlobal confGlobal = ConfGlobal.get("relic_passiveskill_seven");
        int enhanceSkillSn = confGlobal.value;
        Set<Integer> enhanceRelicSkill = Arrays.stream(confGlobal.intArray)
                .boxed()
                .collect(Collectors.toSet());
        int enhanceAttrValue = 0;
        Map<Integer, Integer> skillSnLvMap = new HashMap<>();
        for(Define.p_passive_skill dPassive : passiveSkillList){
            int passiveSkillId = dPassive.getSkillId();
            if(passiveSkillId == enhanceSkillSn){
                ConfSkillLevel_0 confSkillLevel_0 = ConfSkillLevel_0.get(dPassive.getSkillId(), dPassive.getSkillLv());
                if(confSkillLevel_0 != null && confSkillLevel_0.ownEffect != null
                        && confSkillLevel_0.ownEffect.length > 0
                        && confSkillLevel_0.ownEffect[0].length > 1){
                    enhanceAttrValue = confSkillLevel_0.ownEffect[0][1];
                }
            }
            skillSnLvMap.put(passiveSkillId, dPassive.getSkillLv());
        }

        for(Define.p_passive_skill dPassive : passiveSkillList){
            ConfSkillLevel_0 confSkillLevel_0 = ConfSkillLevel_0.get(dPassive.getSkillId(), dPassive.getSkillLv());
            if(confSkillLevel_0 == null){
                continue;
            }
            if(confSkillLevel_0.attrType == null){
                continue;
            }
            int attrType = confSkillLevel_0.attrType[0];
            if(attrType == ParamKey.attrType_1){// 1对伙伴加成
                if(petSnList == null ){
                    continue;
                }
                for(int i = 1; i < confSkillLevel_0.attrType.length; i++){
                    int petSnTemp = confSkillLevel_0.attrType[i];
                    if(petSnList.contains(petSnTemp) || petSnTemp == 0){
                        Map<Integer, Integer> keyValueMap = petSnKeyValueMap.computeIfAbsent(petSnTemp, k -> new HashMap<>());
                        int[][] arr = confSkillLevel_0.ownEffect;
                        for(int n = 0; n < arr.length; n++){
                            int[] intArr = arr[n];
                            for(int m = 0; m < intArr.length; m+=2){
                                int key = intArr[m];
                                int value = intArr[m + 1];
                                if(enhanceRelicSkill.contains(dPassive.getSkillId())){//4033遗物加成
                                    value = (int)(value * (1+enhanceAttrValue/10000.0));
                                }
                                int oldValue = keyValueMap.getOrDefault(key, 0);
                                keyValueMap.put(key, oldValue + value);
                            }
                        }
                    }
                }
            } else if(attrType == ParamKey.attrType_2){	// 2对技能加成
                for(int i = 1; i < confSkillLevel_0.attrType.length; i++){
                    int skillSn = confSkillLevel_0.attrType[i];
                    if(!activeSkills.contains(skillSn) && !skillSnLvMap.containsKey(skillSn)){
                        continue;
                    }
                    Map<Integer, Integer> keyValueMap = skillSnKeyValueMap.computeIfAbsent(skillSn, k -> new HashMap<>());
                    int[][] arr = confSkillLevel_0.ownEffect;
                    for(int n = 0; n < arr.length; n++){
                        int[] intArr = arr[n];
                        for(int m = 0; m < intArr.length; m+=2){
                            int key = intArr[m];
                            int value = intArr[m + 1];
                            if(enhanceRelicSkill.contains(dPassive.getSkillId())){//4033遗物加成
                                value = (int)(value * (1+enhanceAttrValue/10000.0));
                            }
                            int oldValue = keyValueMap.getOrDefault(key, 0);
                            keyValueMap.put(key, oldValue + value);
                        }
                    }
                }
            } else {
                Log.temp.error("====未定义类型和未加代码 attrType ={}", attrType);
                continue;
            }
        }

        if(enhanceAttrValue > 0){
            //遗物技能的技能属性加成
            ConfSkillLevel_0 confSkillLevel_0_1 = ConfSkillLevel_0.get(enhanceSkillSn, skillSnLvMap.get(enhanceSkillSn));//40331
            if(confSkillLevel_0_1 != null && confSkillLevel_0_1.attrType != null){
                int attrType1 = confSkillLevel_0_1.attrType[0];
                if (attrType1 == ParamKey.attrType_2){
                    for(int j = 1; j < confSkillLevel_0_1.attrType.length; j++){//2,4001,4002,4006,4013,4014,4015,4016,4021,4024,4026
                        enhanceSkillAttr(activeSkills, skillSnKeyValueMap,confSkillLevel_0_1.attrType[j], skillSnLvMap, enhanceAttrValue);
                    }
                }
            }
        }
    }

    private static void enhanceSkillAttr(List<Integer> activeSkills, Map<Integer, Map<Integer, Integer>> skillSnKeyValueMap, int skillSnTemp, Map<Integer, Integer> skillSnLvMap, int enhanceAttrValue) {
        if(!activeSkills.contains(skillSnTemp) && !skillSnLvMap.containsKey(skillSnTemp)){
            return;
        }
        ConfSkillLevel_0 confSkillLevel_0_2 = ConfSkillLevel_0.get(skillSnTemp, skillSnLvMap.get(skillSnTemp));//4013
        if(confSkillLevel_0_2 == null || confSkillLevel_0_2.ownEffect == null
                || confSkillLevel_0_2.attrType == null || confSkillLevel_0_2.attrType[0] != 2){
            return;
        }
        for(int k = 1; k < confSkillLevel_0_2.attrType.length; k++) {//2,1047
            int skillSnTemp2 = confSkillLevel_0_2.attrType[k];
            if(!activeSkills.contains(skillSnTemp2) && !skillSnLvMap.containsKey(skillSnTemp2)){//1047
                continue;
            }
            Map<Integer, Integer> keyValueMap = skillSnKeyValueMap.get(skillSnTemp2);
            if (keyValueMap == null) {
                continue;
            }
            int[][] arr = confSkillLevel_0_2.ownEffect;//2026,8000
            for (int[] intArr : arr) {
                for (int m = 0; m < intArr.length; m += 2) {
                    int key = intArr[m];
                    int value = intArr[m + 1];
                    int oldValue = keyValueMap.getOrDefault(key, 0);
                    keyValueMap.put(key, oldValue + (int)(value * enhanceAttrValue / 10000.0));
                }
            }
        }
    }

    public List<Define.p_attr_obj> to_p_attr_obj(List<Define.p_passive_skill> passiveSkillList,
                                                           List<Integer> petSnList, List<Integer> activeSkills,
                                                 List<Define.p_key_value> attList){
        List<Define.p_attr_obj> p_attr_objAll = new ArrayList<>();
        if(petSnList == null){
            petSnList = new ArrayList<>();
        }
        Map<Integer, Map<Integer, Integer>> petSnKeyValueMap = new HashMap<>();
        Map<Integer, Map<Integer, Integer>> skillSnKeyValueMap = new HashMap<>();
        getPassiveMap(passiveSkillList, petSnList, activeSkills, petSnKeyValueMap, skillSnKeyValueMap);
        if(!petSnKeyValueMap.containsKey(0)){
            petSnKeyValueMap.put(0, null);
        }
        for (Map.Entry<Integer, Map<Integer, Integer>> entry : petSnKeyValueMap.entrySet()) {
            int petSn = entry.getKey();
            // 1. 初始化属性列表（直接复制 attList）
            List<Define.p_key_value> attrList = new ArrayList<>();
            if(petSn == 0){//属性放0中，通用给所有伙伴加
                attrList.addAll(attList);
            }

            Map<Integer, Integer> keyValueMap = entry.getValue();
            if (keyValueMap == null || keyValueMap.isEmpty()) {
                p_attr_objAll.add(HumanManager.inst().to_p_attr_obj(ParamKey.attrType_1, petSn, attrList));
                continue;
            }

            // 2. 用 HashMap 临时存储并合并相同 key 的 value（使用 Long 类型）
            Map<Integer, Long> mergedMap = new HashMap<>();

            // 2.1 先把 attrList 里的属性放进 mergedMap（getV() 是 long）
            for (Define.p_key_value attr : attrList) {
                int key = (int) attr.getK();  // 假设 getK() 返回的是 long，强转成 int（如果 key 是 int）
                long value = attr.getV();      // getV() 返回 long
                mergedMap.merge(key, value, Long::sum);  // 合并时用 Long::sum
            }

            // 2.2 再把 keyValueMap 里的属性合并进去（value 是 Integer，需转成 long）
            for (Map.Entry<Integer, Integer> kv : keyValueMap.entrySet()) {
                int key = kv.getKey();
                long value = kv.getValue();  // Integer 自动转 long
                mergedMap.merge(key, value, Long::sum);  // 相同 key 的 value 相加
            }

            // 3. 重新生成 attrList（确保 to_p_key_value 支持 long 参数）
            attrList.clear();
            for (Map.Entry<Integer, Long> kv : mergedMap.entrySet()) {
                int key = kv.getKey();
                long value = kv.getValue();
                attrList.add(HumanManager.inst().to_p_key_value(key, value).build()); // 假设 to_p_key_value 支持 long
            }

            // 4. 添加到结果
            p_attr_objAll.add(HumanManager.inst().to_p_attr_obj(ParamKey.attrType_1, petSn, attrList));
        }

        for(Map.Entry<Integer, Map<Integer, Integer>> entry : skillSnKeyValueMap.entrySet()) {
            int skillSn = entry.getKey();
            Map<Integer, Integer> keyValueMap = entry.getValue();
            if (keyValueMap.size() == 0) {
                continue;
            }
            List<Define.p_key_value> attrList = new ArrayList<>();
            for (Map.Entry<Integer, Integer> keyValueEntry : keyValueMap.entrySet()) {
                int key = keyValueEntry.getKey();
                int value = keyValueEntry.getValue();
                attrList.add(HumanManager.inst().to_p_key_value(key, value).build());
            }
            p_attr_objAll.add(HumanManager.inst().to_p_attr_obj(ParamKey.attrType_2, skillSn, attrList));
        }

        return p_attr_objAll;
    }
}
