//package org.gof.demo.battlesrv.msgHandler;
//
//import org.gof.core.support.observer.MsgReceiver;
//import org.gof.demo.battlesrv.manager.PropManager;
//import org.gof.demo.battlesrv.stageObj.UnitObject;
//import org.gof.demo.battlesrv.support.UnitObjectStateKey;
//import org.gof.demo.battlesrv.support.Vector2D;
//import org.gof.demo.battlesrv.support.Vector3D;
//import org.gof.demo.seam.msg.MsgParam;
//import org.gof.demo.worldsrv.character.HumanObject;
//import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
//import org.gof.demo.worldsrv.msg.Define;
//import org.gof.demo.worldsrv.msg.Define.DVector2;
//import org.gof.demo.worldsrv.msg.MsgIds;
//import org.gof.demo.worldsrv.msg.MsgStage;
//import org.gof.demo.worldsrv.msg.MsgStage.CSStageMove;
//import org.gof.demo.worldsrv.msg.MsgStage.CSStageMoveStop;
//import org.gof.demo.worldsrv.stage.StageManager;
//import org.gof.demo.worldsrv.support.Log;
//
//import java.util.List;
//
//public class StageBattleMsgHandler {
//
//	/**
//	 * 玩家移动
//	 * @param param
//	 */
//	@MsgReceiver(CSStageMove.class)
//	public void onCSStageMove(MsgParam param) {
//		HumanObject humanObj = param.getHumanObject();
//		CSStageMove msg = param.getMsg();
//		//获得指定角色
//		if(humanObj.stageObj == null)
//			return;
//
//		UnitObject unitObj = humanObj;
//		if(unitObj.state.containsKey(UnitObjectStateKey.beatFly)){
//        	return;
//		}
//
//		//获得坐标
//		Vector3D dir = new Vector3D(msg.getDir());
//		//移动
//		Vector3D posBegin = new Vector3D(msg.getPosBegin());
////		Log.game.info("recv move request obj time {}", unitObj.getTime());
//
//		List<Vector3D> posEndList = Vector3D.parseFrom(msg.getPosEndList());
//
////		if (humanObj.stageObj instanceof org.gof.demo.worldsrv.melee.stage.StageObjectMeleeRiding
////				|| humanObj.stageObj instanceof org.gof.demo.worldsrv.seagod.StageObjectSeaGodInstance){
////			unitObj.move(posBegin, posEndList, dir, true, msg.getCtimestamp());
////		}else {
//			int result = humanObj.moveChecker.check(humanObj, posBegin, 2, posEndList);
//			if (result == 0) {
//				unitObj.move(posBegin, posEndList, dir, true, msg.getCtimestamp());
//			} else if (result == 1) {
//				StageManager.inst().pullTo(humanObj, humanObj.getPosNow(), false);
//			} else {
//				HumanGlobalServiceProxy.newInstance().kick(humanObj.id, 8507);
//			}
////		}
//	}
//
//	/**
//	 * 玩家移动停止
//	 * @param param
//	 * @return
//	 */
//	@MsgReceiver(CSStageMoveStop.class)
//	public void onCSStageMoveStop(MsgParam param) {
//		HumanObject humanObj = param.getHumanObject();
//		CSStageMoveStop msg = param.getMsg();
//		long sendId = msg.getSendId();
//		Vector2D posEnd = new Vector2D(msg.getPosEnd());
//
//		if(humanObj.stageObj == null) {
//			return;
//		}
//
//		UnitObject unitObj = humanObj.stageObj.getUnitObj(sendId);
//
//		if(unitObj == null) {
//			return;
//		}
//		//停止
//		unitObj.stop(dir);
//
////		Log.fight.info("当前坐标 [{}]:【{}，{}】|| 前端坐标 【{}，{}】}", unitObj.name, unitObj.getPosNow().x, unitObj.getPosNow().y, posEnd.x, posEnd.y);
//
//		if(!(posEnd.x == 0 && posEnd.y == 0)) {
//			Vector2D posNow = unitObj.getPosNow();
//			if(posNow == null || posEnd == null){
//				return;
//			}
//			double speedPos = unitObj.getUnit().getSpeed() * 0.4 / 100f;
//			if(unitObj.isHumanObj()){
////				PropManager.inst().speedLog(unitObj.getHumanObj(), unitObj.getUnit().getSpeed());
//				if(speedPos > 8){
//					Log.temp.error("===移动可能出bug了, humanId={}, name={}, speed={}, speedPos={}",
//							unitObj.id, unitObj.name, unitObj.getUnit().getSpeed(), speedPos);
//				}
//			}
//			if(unitObj.getPosNow().distance(posEnd) < speedPos) {
//				unitObj.setPosNow(posEnd);
//			} else {
//				if(unitObj.isHumanObj()){ //针对玩家位移技能穿光墙的特殊处理，完全信任客户端的逻辑
//					HumanObject hObj = (HumanObject)unitObj;
//					if(hObj.getStopByClient()>0 && hObj.getStopByClient()>hObj.getTime()){
//						//距离判断10m
////						if(hObj.getPosNow().distance(posEnd)<10){
////							unitObj.setPosNow(posEnd);
////						}
//					}
//					hObj.setStopByClient(0);
//				}
////				Log.fight.info("坐标不修正，当前坐标 [{}]:【{}，{}】|| 前端坐标 【{}，{}】}", unitObj.name, unitObj.getPosNow().x, unitObj.getPosNow().y, posEnd.x, posEnd.y);
//			}
//		}
//
////		Log.fight.info("停止移动，当前坐标 [{}]:【{}，{}】}", unitObj.name, unitObj.getPosNow().x, unitObj.getPosNow().y);
//
//	}
//
//	/**
//	 * 移动转向
//	 * <AUTHOR>
//	 * @Date 2023/6/5
//	 * @Param
//	 */
//	@MsgReceiver(MsgStage.CSStageMoveVeer.class)
//	public void onCSStageMoveVeer(MsgParam param) {
//		HumanObject humanObj = param.getHumanObject();
//		MsgStage.CSStageMoveVeer msg = param.getMsg();
//		// 获得指定角色
//		if(humanObj.stageObj == null) {
//			return;
//		}
//		UnitObject unitObj = humanObj;
//		if(unitObj.state.containsKey(UnitObjectStateKey.beatFly)){
//			return;
//		}
//		if(humanObj.getPosNow() == null){
//			return;
//		}
//
//		// 移动开始位置
//		Vector3D posBegin = new Vector3D(msg.getPosBegin());
//		// 移动完的最终方向坐标
//		Vector3D dir = new Vector3D(msg.getDir());
//		//目标路径坐标
//		List<Vector3D> posEndList = Vector3D.parseFrom(msg.getPosEndList());
//
//		Define.DVector2.Builder dvector2 = Define.DVector2.newBuilder();
//		dvector2.setX((float) dir.x);
//		dvector2.setY((float)dir.y);
//		// 转向时直接停止当前移动
//		humanObj.stop(dvector2.build());
//
//		double speedPos = unitObj.getUnit().getSpeed() * 0.4 / 100f;
//		Vector2D vector2D = humanObj.getPosNow();
//		if(vector2D.distance(posBegin.toVector2D()) > speedPos){
//			StageManager.inst().pullTo(humanObj, vector2D, false);
//			return;
//		}
//		// cd冷却防范
//		if(!humanObj.isMsgIdCD(MsgIds.CSStageMoveVeer, 0.1f)){
//			humanObj.setDirNow(dir.toVector2D());
//			return;
//		}
//		// 改变方向后再继续移动
//		unitObj.move(new Vector3D(vector2D.x,vector2D.y,0), posEndList, dir, true, msg.getCtimestamp());
//	}
//}
