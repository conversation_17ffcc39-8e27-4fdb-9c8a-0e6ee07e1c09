package org.gof.demo.battlesrv.support;

import org.gof.demo.worldsrv.global.GlobalConfVal;

import java.util.Map;

/**
 * 
 * <AUTHOR>
 * 所有Pct属性都是原来属性的百分比加成，主要用于装备和天赋的百分比提升
 *
 */
public enum PropKey {

	total_atk(1),
	atk(1001),
	total_hp(2),
	hp(1002),
	total_def(24),
	def(1024),
	pickaxe_max(1101),
	pickaxe_speed(2101),
	mineral_num(2104),
	science_speed(2105),
	farm_seed_add(1201),
	online_reward(2102),
	offline_reward(2103),
	offline_reward_time(1102),

	;
	
	private PropKey(int attributeSn) {
		this.attributeSn = attributeSn;
	}

	private int attributeSn;

	public int getAttributeSn() {
		return attributeSn;
	}
	
	/**
	 * 是否包含给定的Key的对应值
	 * <AUTHOR>
	 * @Date 2024/2/26
	 * @Param keyValue （属性表sn对应的Key值，例如 sn=2, keyValue为total_hp）
	 */
	public static boolean contains(String keyValue) {
		return GlobalConfVal.propSnStringMap.containsValue(keyValue);
	}
	
	public static int getAttributeSn(String name) {
		for(Map.Entry<Integer, String> entry : GlobalConfVal.propSnStringMap.entrySet()) {
			if(entry.getValue().equals(name))
				return entry.getKey();
		}
		return 0;
	}

}
