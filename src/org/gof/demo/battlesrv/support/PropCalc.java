package org.gof.demo.battlesrv.support;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.msg.Define;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 属性计算，只供PropKey中枚举的字段使用
 */
public class PropCalc extends PropCalcBase<Integer, BigDecimal> {
	public PropCalc() {
		
	}
	
	public PropCalc(String json) {
		super(json);
	}

	public PropCalc(List<Define.p_key_value> list) {
		super(list);
	}

	@Override
	protected Integer toKey(String key) {
		return Utils.intValue(key);
	}

	@Override
	protected BigDecimal toValue(Object value) {
		return new BigDecimal(value.toString());
	}

	@Override
	protected boolean canDiscard(Object value) {
		if(toValue(value).intValue() == 0) {
			return true;
		}
		return false;
	}

	/**
	 * 属性乘法加成
	 * @param perMyriad
	 * @return
	 */
	public PropCalc mul(PropCalc perMyriad) {
		Map<Integer, BigDecimal> perMyriadMap = perMyriad.datas;
		for(Map.Entry<Integer, BigDecimal> entry : perMyriadMap.entrySet()) {
			Integer k = entry.getKey();
			BigDecimal v = entry.getValue();
			mul(k, 1+v.floatValue()/10000f);
		}
		return this;
	}
}
