package org.gof.demo.battlesrv.support;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.msg.Define;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.Map.Entry;

/**
 * 通用属性累加
 *
 */
public abstract class PropCalcBase<K, V> {
	protected final Map<K, V> datas = new HashMap<>();
	
	protected abstract K toKey(String key);
	protected abstract V toValue(Object value);
	/**
	 * 是否能被销毁
	 * @param value
	 * @return
	 */
	protected abstract boolean canDiscard(Object value);
	
	public PropCalcBase() {

	}
	
	public PropCalcBase(String json) {
		if (StringUtils.isBlank(json)) {
			return;
		}
		JSONObject data = JSON.parseObject(json);
		for (Entry<String, Object> entry : data.entrySet()) {
			K k = this.toKey(entry.getKey());
			V v = this.toValue(entry.getValue());

			datas.put(k, v);
		}
	}

	public PropCalcBase(List<Define.p_key_value> dPropList) {
		if (dPropList == null || dPropList.size() == 0) {
			return;
		}
		for (Define.p_key_value dProp : dPropList) {
			K k = this.toKey(String.valueOf(dProp.getK()));
			V v = this.toValue(dProp.getV());
			datas.put(k, v);
		}
	}
	
	/**
	 * 转换为可储存的文本格式
	 * @return
	 */
	public String toJSONStr() {
		//去除所有可以忽略的选择
		Iterator<Map.Entry<K, V>> it = datas.entrySet().iterator();  
        while(it.hasNext()){  
        	Map.Entry<K, V> entry=it.next();
        	if(canDiscard(entry.getValue())) {
        		it.remove();
        	}
        }

		return Utils.toJSONString(datas);
	}
	
	/**
	 * 累加属性加成
	 * @param plus
	 * @return
	 */
	public PropCalcBase<K, V> plus(PropCalcBase<K, V> plus) {
		Map<K, V> datasNew = plus.datas;
		for(Entry<K, V> entry : datasNew.entrySet()) {
			K k = entry.getKey();
			V v = entry.getValue();
			
			//累加属性
			plus(k, v);
		}
		
		return this;
	}
	
	/**
	 * 累加属性加成
	 * @param
	 * @return
	 */
	public PropCalcBase<K, V> plus(K key, V value) {
		BigDecimal valueNew = getBigDecimal(key).add((BigDecimal) value);
		datas.put(key, this.toValue(valueNew));
		return this;
	}
	
	/**
	 * 累加属性加成
	 * @param
	 * @return
	 */
	public PropCalcBase<K, V> plus(String json) {
		if(StringUtils.isBlank(json)) return this;
		JSONObject data = JSON.parseObject(json);
		for (Entry<String, Object> entry : data.entrySet()) {
			K k = this.toKey(entry.getKey());
			V v = this.toValue(entry.getValue());
			plus(k, v);
		}
		return this;
	}
	
	public PropCalcBase<K, V> plus(Map<String, BigDecimal> map) {
		if(map == null || map.size() == 0) return this;
		for (Entry<String, BigDecimal> entry : map.entrySet()) {
			K k = this.toKey(entry.getKey());
			V v = this.toValue(entry.getValue());
			plus(k, v);
		}
		return this;
	}

	public PropCalcBase<K, V> plusMap(Map<K, V> map) {
		if (map == null || map.size() == 0) return this;
		for (Entry<K, V> entry : map.entrySet()) {
			K k = entry.getKey();
			V v = entry.getValue();
			plus(k, v);
		}
		return this;
	}
	
	/**
	 * 累加属性加成<String[],int[]>
	 * @param propKey
	 * @param propValue
	 * @return
	 */
	public PropCalcBase<K, V> plus(String[] propKey, int[] propValue) {
		if(propKey != null && propValue != null && propKey.length == propValue.length) {
			for(int i = 0 ; i < propKey.length ; i++) {
				K k = this.toKey(propKey[i]);
				V v = this.toValue(propValue[i]);
				plus(k, v);
			}
		}
		return this;
	}

	public PropCalcBase<K, V> plus(int[][] propArr) {
		if(propArr != null) {
			for(int i = 0 ; i < propArr.length ; i++) {
				for(int m = 0 ; m < propArr[i].length ; m+=2) {
					K k = this.toKey(String.valueOf(propArr[i][m]));
					V v = this.toValue(propArr[i][m + 1]);
					plus(k, v);
				}
			}
		}
		return this;
	}

	public PropCalcBase<K, V> plus(String propKey, int propValue) {
		if(propKey != null ) {
				K k = this.toKey(propKey);
				V v = this.toValue(propValue);
				plus(k, v);
		}
		return this;
	}
	
	/**
	 * 累加属性加成<String[],float[]>
	 * @param propKey
	 * @param propValue
	 * @return
	 */
	public PropCalcBase<K, V> plus(String[] propKey, float[] propValue) {
		if(propKey != null && propValue != null && propKey.length == propValue.length) {
			for(int i = 0 ; i < propKey.length ; i++) {
				K k = this.toKey(propKey[i]);
				V v = this.toValue(propValue[i]);
				plus(k, v);
			}
		}
		return this;
	}
	
	/**
	 * 减去属性加成<String[],int[]>
	 * @param propKey
	 * @param propValue
	 * @return
	 */
	public PropCalcBase<K, V> minus(String[] propKey, int[] propValue) {
		if(propKey != null && propValue != null && propKey.length == propValue.length) {
			for(int i = 0 ; i < propKey.length ; i++) {
				K k = this.toKey(propKey[i]);
				V v = this.toValue(propValue[i]);
				minus(k, v);
			}
		}
		return this;
	}
	
	/**
	 * 减去属性加成<String[],float[]>
	 * @param propKey
	 * @param propValue
	 * @return
	 */
	public PropCalcBase<K, V> minus(String[] propKey, float[] propValue) {
		if(propKey != null && propValue != null && propKey.length == propValue.length) {
			for(int i = 0 ; i < propKey.length ; i++) {
				K k = this.toKey(propKey[i]);
				V v = this.toValue(propValue[i]);
				minus(k, v);
			}
		}
		return this;
	}
	
	/**属性系数
	 * @param key
	 * @param value
	 * @return
	 */
	public PropCalcBase<K, V> mul(K key, float value) {
		BigDecimal valueNew = getBigDecimal(key).multiply(new BigDecimal(String.valueOf(value)));
		datas.put(key, this.toValue(valueNew));
		return this;
	}
	
	/**
	 * 属性系数
	 * @param propKey
	 * @param propValue
	 * @return
	 */
	public PropCalcBase<K, V> mul(String[] propKey, float[] propValue) {
		if(propKey != null && propValue != null && propKey.length == propValue.length) {
			for(int i = 0 ; i < propKey.length ; i++) {
				K k = this.toKey(propKey[i]);
				float v = propValue[i];
				mul(k, v);
			}
		}
		return this;
	}

	/**
	 * 属性系数
	 * @param propValue
	 * @return
	 */
	public PropCalcBase<K, V> mul(float propValue) {
		Set<K> keySet = datas.keySet();
        for (K k: keySet) {
			BigDecimal valueNew = getBigDecimal(k).multiply(BigDecimal.valueOf(propValue));
            datas.put(k, this.toValue(valueNew));
        }
		return this;
	}
	/**
	 * 属性系数避免精度丢失
	 * @param propValue
	 * @return
	 */
	public PropCalcBase<K, V> multiply(float propValue) {
		Set<K> keySet = datas.keySet();
		for (K k : keySet) {
			BigDecimal currentValue = getBigDecimal(k); // 获取当前值（BigDecimal）
			BigDecimal multipliedValue = currentValue.multiply(BigDecimal.valueOf(propValue)); // 乘法计算
			V newValue = toValue(multipliedValue); // 转换回 V 类型
			datas.put(k, newValue); // 更新值
		}
		return this; // 链式调用支持
	}

	/**
	 * 减去属性加成
	 * @param plus
	 * @return
	 */
	public PropCalcBase<K, V> minus(PropCalcBase<K, V> plus) {
		Map<K, V> datasNew = plus.datas;
		for(Entry<K, V> entry : datasNew.entrySet()) {
			K k = entry.getKey();
			V v = entry.getValue();
			
			//累加属性
			minus(k, v);
		}
		
		return this;
	}
	
	/**
	 * 减去属性加成
	 * @param
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public PropCalcBase<K, V> minus(K key, V value) {
		BigDecimal valueNew = getBigDecimal(key).subtract(new BigDecimal(value.toString()));
		datas.put(key, (V)valueNew);
		
		return this;
	}	
	
	/**
	 * 减去属性加成
	 * @param
	 * @return
	 */
	public PropCalcBase<K, V> minus(String json) {
		if(StringUtils.isBlank(json)) return this;
		JSONObject data = JSON.parseObject(json);
		for (Entry<String, Object> entry : data.entrySet()) {
			K k = this.toKey(entry.getKey());
			V v = this.toValue(entry.getValue());
			minus(k, v);
		}
		return this;
	}
	
	/**
	 * 移除某个属性
	 * @param key
	 * @return
	 * 2013-5-22
	 */
	public PropCalcBase<K, V> remove(K key) {
		datas.remove(key);
		return this;
	}
	
	public PropCalcBase<K, V> removeAll() {
		datas.clear();
		return this;
	}
	
	/**
	 * 设置累加属性
	 * @param
	 * @return
	 */
	public PropCalcBase<K, V> put(K key, V value) {
		datas.put(key, value);
		
		return this;
	}
	

	/**
	 * 获取int型数值
	 * @param key
	 * @return
	 */
	public int getInt(K key) {
		V v = datas.get(key);
		if(v == null) return 0;
		
		return Utils.intValue(v.toString());
	}
	
	/**
	 * 获取BigInteger型数值
	 * @param key
	 * @return
	 */
	public BigInteger getBigInteger(K key) {
		V v = datas.get(key);
		if(v == null) return BigInteger.ZERO;
		
		return new BigInteger(v.toString());
	}

	public BigDecimal getBigDecimal(K key) {
		V v = datas.get(key);
		if(v == null) return BigDecimal.ZERO;

		return new BigDecimal(v.toString());
	}


	/**
	 * 获取当前键值的Map对象
	 * @return
	 */
	public Map<K,V> getDatas() {
		return datas;
	}
	
	@Override
	public String toString() {
		return toJSONStr();
	}

	public List<Define.p_key_value> dPropList(){
		List<Define.p_key_value> dPropList = new ArrayList<>();
		Iterator<Map.Entry<K, V>> it = datas.entrySet().iterator();
		while(it.hasNext()){
			Map.Entry<K, V> entry=it.next();
			Define.p_key_value.Builder dProp = Define.p_key_value.newBuilder();
			dProp.setK(((Integer)entry.getKey()).longValue());
			dProp.setV(((BigDecimal)entry.getValue()).longValue());
			dPropList.add(dProp.build());
		}
		return dPropList;
	}

}
