package org.gof.demo.battlesrv.support;

/**
 * unitObj的状态
 *
 */
public enum UnitObjectStateKey {
	skillback(0, true),					//技能击退
	stun(1, true),						//眩晕
	immobilize(2, true),				//冻结
	silence(3, true),						//沉默
	skill_shake(4, false),				//施法前摇
	skill_sheep(5, true),				//变羊
	skill_hypnosis(6, true),			//催眠
	cast_skilling(7, false),			//正在释放技能
	charm(8, true),						//魅惑
	cast_locked(9, false),				//技能锁定，释放时候不能移动
	coil(10, false),                    //缠绕.可以做动作，只是不能移动
	stiffness(11, true),                //硬直
	beatFly(12, true), 					//击飞
	weak(13, true)	,					//虚弱
	suppress(14,true)	,//压制
	skill_action(15,true),//动作时间 前摇+激发
	god(16,true),
	realBody(17, true),// 武魂真身
	hatred(18, true),// 增加仇恨
	fear(19, true), // 恐惧
	godless(20, true), // 类似无敌但可以被打断
	;
	
	private final int type;
	private final boolean limit;
	
	private UnitObjectStateKey(int type, boolean limit) {
		this.type = type;
		this.limit = limit;
	}

	public boolean isLimit() {
		return limit;
	}

	public int getType() {
		return type;
	}
	
	public static UnitObjectStateKey getType(int idx){
		return UnitObjectStateKey.values()[idx];
	}
}
