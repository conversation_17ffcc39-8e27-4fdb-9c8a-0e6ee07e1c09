package org.gof.demo.battlesrv.manager;

import org.gof.core.support.ManagerBase;
import org.gof.demo.battlesrv.stageObj.UnitObject;



public class UnitManager extends ManagerBase {
    /**
     * 获取实例
     *
     * @return
     */
    public static UnitManager inst() {
        return inst(UnitManager.class);
    }


    public void propCalc(UnitObject unitObj) {
        PropManager.inst().propCalc(unitObj, CombatChangeLog.未设置);
    }

    public void propCalc(UnitObject unitObj,CombatChangeLog log) {
        PropManager.inst().propCalc(unitObj,log);
    }


}