package org.gof.demo.battlesrv.battle;

import org.gof.core.Port;
import org.gof.demo.battlesrv.battle.chapter.*;
import org.gof.demo.battlesrv.battle.enumVo.RunMode;
import org.gof.demo.battlesrv.battle.enumVo.RunState;
import org.gof.demo.worldsrv.instance.ConfChapterVO;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.msg.Define;

import java.util.List;

/**
 * @program: game2
 * @author: Mr.wugz
 * @create: 2024-06-06 18:14
 **/
public class BattleMainServer extends BattleMain {
    public int result = 0;
    private long lastUpdateFrameTime;// 上次桢更新的时间

    public BattleMainServer(int e)//e = 0
    {
        super(e);
    }

    public void start2(BattleData t, int e, List<Define.p_battle_operator> r)//r = null
    {
        this.data.chapterId = t.chapterId;
        this.data.chapterType = t.chapterType;
        this.data.chapterMode = 0;
        if (t.chapterType == InstanceConstants.PARKCROSSPVPCHAPTER_26)//CrossParkPvp
            this.chapter = new ChapterCrossParkPvpServer(this);
        else if (t.chapterType == InstanceConstants.LEAGUEGVECHAPTER_7)
            this.chapter = new ChapterTeam20Server(this);
        else if (t.chapterType == InstanceConstants.KFWARCHAPTER_17)
            this.chapter = new ChapterCrossWarServer(this);
        else if (t.chapterType == InstanceConstants.KFWARCHAPTERMONSTER_18)
            this.chapter = new ChapterCrossWarMonsterServer(this);
        else if (t.chapterType == InstanceConstants.FAMILYBRAWLCHAPTER_14)
            this.chapter = new ChapterGvgServer(this);
        else if (t.chapterType == InstanceConstants.KUNGFURACE_CHAPTER_33)
            this.chapter = new ChapterDoublePvpServer(this);
        this.chapter.setChapter(this.data.chapterId, true);
        ConfChapterVO a = this.chapter.config;
        this.playerAuto = 1 != e;
        if (1 == e && null != r) {
            this.runMode = RunMode.Check;
        }
        this.data.playerList = t.playerList;
        this.data.openServerDay = t.openServerDay;
        this.data.buffs = t.buffs;
        this.data.monster = t.monster;
        this.data.playerImage = t.playerImage;
        this.frameCount = 0;
        this.runState = RunState.Running;
        this.chapter.init();
        this.chapter.loadData(0);
        this.chapter.start();
        this.unitMgr.updateDepth();
        this.lastUpdateFrameTime = Port.getTime();
    }

    public void start(BattleData t, Object e)//e = null
    {
        this.start2(t, 0, null);
    }

    public void update(float t) {
        if (this.runState != RunState.Stop)
            this.gameLogic(t);
    }

    /**
     * 不传入更新祯时长，现在服务器update的调用不能保证一定0.033f执行一次
     */
    public void update() {
        long currTime = Port.getTime();
        long diffTime = currTime - lastUpdateFrameTime;
        lastUpdateFrameTime = currTime;
        update(diffTime * 1.0f / 1000L);
    }

    public void toResult(int t, boolean e) {
        if (this.runState != RunState.Running)
            return;
//        this.unitMgr.battleOver();
//        this.unitMgr.clear();
        this.runState = RunState.Pause;
        this.result = t;
    }

    @Override
    protected void gc() {
    }

    public void playSound(int t) {
    }
}
