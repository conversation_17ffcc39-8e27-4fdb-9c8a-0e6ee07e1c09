package org.gof.demo.battlesrv.battle;


import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.module.MetaAttrib;
import org.gof.demo.battlesrv.battle.module.UnitData;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.support.Log;

import java.util.*;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-06-06 18:13
 **/
public class BattleDataFill {

    public static UnitData setMonster(Define.p_battle_monster t) {
        UnitData i = new UnitData();
        i.attribs = new HashMap<>();
        i.setConfig(ConfUnit.get((long) t.getId()));
        i.skillList = new ArrayList<>();
        for (Integer sn : GlobalConfVal.propNameSnModule1Map.values()) {
            {
                ConfAttribute s = ConfAttribute.get(sn);
                MetaAttrib f = new MetaAttrib(s, null);
                f.setBaseValue(getAttrib(s.sn, t.getAttrListList()));
                i.attribs.put(AttribDefine.fromValue(s.sn), f);
            }
        }
        List<Define.p_key_value> lst = t.getAttrListList();
        for (Define.p_key_value d : lst) {
            MetaAttrib f = i.attribs.get(AttribDefine.fromValue((int) d.getK()));
            f.setBaseValue(d.getV());
        }
        int[] passiveSkills = i.config().passiveSkills != null ? i.config().passiveSkills : new int[0];
        for (int b : passiveSkills) {
            UnitMgr.addSkill(i, b, 1);
        }
        if (t.getHp() > 0) i.currenHp = t.getHp();
        return i;
    }

    public static void setPlayerList(Define.p_battle_role t, PlayerData i, int a) {
        UnitData n = new UnitData();
        n.attribs = new HashMap<>();
        int o = ConfJobs.get(t.getFigure().getJobFigure()).model;
        ConfUnit f = ConfUnit.get(Utils.longValue(o));

        n.setConfig(f);
        n.name = t.getName();
        n.level = t.getLev();
        n.head = t.getHead();
        n.roleId = i.Id;
        setPlayerAttrib(t, n, a);
        n.idleIndex = 1;
        setPlayerEquip(t, n, o, a);
        i.units.add(n);
        Map<Integer, Map<Integer, Map<Long, Long>>> l = new HashMap<>();
        if (t.getAttrObjListList() != null && t.getAttrObjListList().size() > 0) {
            l.put(1, new HashMap<>());
            l.put(2, new HashMap<>());
            for (Define.p_attr_obj_list g : t.getAttrObjListList()) {
                int b = (int) g.getObj().getK();
                int p = (int) g.getObj().getV();
                Map<Integer, Map<Long, Long>> k = l.get(b);

                Map<Long, Long> map = k.get(p);
                if (map == null) {
                    map = new HashMap<>();
                    k.put(p, map);
                }
                for (int xhx = 0; xhx < g.getAttrListCount(); xhx++) {
                    map.put(g.getAttrList(xhx).getK(), g.getAttrList(xhx).getV());
                }
            }
        }

        setPlayerPets(i, t.getPetListList(), l.containsKey(1) ? l.get(1) : null);
        n.skillAttrInfo = l.containsKey(2) ? l.get(2) : null;
        setPlayerSkill(t, n);
        // 星将技能参数
        int[] _o = new int[3];
        if (t.getExtList() != null && t.getExtCount() > 0) {
            for (Define.p_key_value m : t.getExtList()) {
                switch ((int) m.getK()) {
                    case 1:
                        i.Pos = (int) m.getV();
                        break;
                    case 2:
                        long A = Math.round(Math.max(1, m.getV()) / 10000D * n.getAttribByInt(AttribDefine.hp));
                        n.currenHp = A;
                        n.initHp = A;
                        break;
                    case 3:
                    case 5:
                    case 7:
                        n.buffs.add((int) m.getV());
                        break;
                    case 4:
                        long P = Math.max(1, Math.min(m.getV(), n.getAttribByInt(AttribDefine.hp)));
                        n.currenHp = P;
                        n.initHp = P;
                        break;
                    case 6:
                        setPlayerFlyPet(i, (int)m.getV());
                        break;
                    case 9:
                        _o[0] = (int)m.getV();
                        break;
                    case 10:
                        _o[1] = (int)m.getV();
                        break;
                    case 11:
                        _o[2] = (int)m.getV();
                        break;
                }

            }
        }
        setPlayerPassiveSkill(t, n, a,_o);
    }

    private static void setPlayerAttrib(Define.p_battle_role t, UnitData i, int r) {
        ConfChapterType u = ConfChapterType.get(r);
        for (Integer sn : GlobalConfVal.propNameSnModule1Map.values()) {
            ConfAttribute v = ConfAttribute.get(sn);
            MetaAttrib c = new MetaAttrib(v, null);
            if (0 != u.pve || v.sn != AttribDefine.pve_dam.getValue() && v.sn != AttribDefine.pve_resist.getValue())
                c.setBaseValue(getAttrib(v.sn, t.getAttrListList()));
            else
                c.setBaseValue(0);
            i.attribs.put(AttribDefine.fromValue(v.sn), c);
        }
    }

    public static long getAttrib(int t, List<Define.p_key_value> i) {
        if (null == i || i.size() == 0)
            return 0;
        for (Define.p_key_value l : i) {
            if (l.getK() == t)
                return l.getV();
        }
        return 0;
    }

    private static void setPlayerEquip(Define.p_battle_role t, UnitData e, int i, int r)
    {
        e.mount = t.getFigure().getMountFigure();
        for(Define.p_key_value _r : t.getFigure().getEquipListList())
        {
            if (1 == _r.getK()){
                e.weapon = (int)_r.getV();
                break;
            }
        }
        if (t.getFigure().getArtifactFigure() > 0)
            e.weapon = t.getFigure().getArtifactFigure();
        for(Define.p_key_value _r : t.getFigure().getSkinListList())
        {
            if (2 == _r.getK()){
                e.setDress(i, (int)_r.getV(), t.getFigure().getGender());
                break;
            }
        }
    }

    private static long getPetAttrByAttrId(Map<Integer, Map<Long, Long>> t, int e, int i) {
        if (!t.containsKey(e)) {
            return 0;
        }

        Map<Long, Long> r = t.get(e);
        if (!r.containsKey((long) i)) {
            return 0;
        }
        return r.get((long) i);
        //return !t.TryGetValue((int)e, out var r) ? 0 : !r.TryGetValue(i, out var n) ? 0 : n;
    }

    private static float getPetFactAttrValue(Map<Integer, Map<Long, Long>> t, int e, int i, int a) {
        if (null == t)
            return e;
        float n = e + getPetAttrByAttrId(t, i, a) + getPetAttrByAttrId(t, 0, a);
        List<ConfAttribute> l = ConfAttribute.findBy(ConfAttribute.K.group, a);
//            var l = ConfAttribute.Array.Where(i => i.group == a).ToArray();
        if (null == l || l.size() <= 0)
            return n;
        for (int s = 0; s < l.size(); s++) {
            long u = getPetAttrByAttrId(t, i, l.get(s).sn) + getPetAttrByAttrId(t, 0, l.get(s).sn);
            float o = (u / 10000f) + 1;
            n = n * o;
        }
        return n;
    }

    private static void setPlayerSkill(Define.p_battle_role t, UnitData e) {
        if (e.skillList == null) e.skillList = new ArrayList<>();
        e.skillList.clear();
        List<Define.p_active_skill> n = t.getRoleSkill().getActiveSkillList();
        /*if(n.size() > 2){
            n.sort((a, b) -> a.getPosId() > b.getPosId() ? 1 : -1);
        } else {
            Collections.sort(n, (a, b) -> {
                int ret = 0;// 0默认相等
                if (a != null && b != null) {
                    // 倒序
                    if (a.getPosId() > b.getPosId()) {
                        ret = 1;
                    } else if (a.getPosId() < b.getPosId()) {
                        ret = -1;
                    } else {
                            ret = 0;
                    }
                }
                return ret;
            });
        }*/

//            n.sort((t, e) =>
//            {
//                return t.getPosId() > e.getPosId() ? 1 : -1;
//            });
        for (int l = 0; l < n.size(); l++) {
            Define.p_active_skill u = n.get(l);
            if (u.getSkillId() > 0) {
                UnitMgr.addSkill(e, u.getSkillId(), u.getSkillLv()).useDelay = u.getDelayTime() / 1000f;
            }
        }
        e.ativeSkills = n;
    }

    public static void setSkillEffect(Skill t, int e)
    {
        if (e > 0)
        {
            ConfAngelSkill i = ConfAngelSkill.get(e);
            if (null != i)
            {
                t.skillEffectList.add(i.skill_effect);
                t.skillDamList.add(i.skillPar);
            }
        }
    }

    private static void setPlayerPassiveSkill(Define.p_battle_role t, UnitData i, int r,int[] l) {
        for (Define.p_passive_skill c : t.getRoleSkill().getPassiveSkillList()) {
            if (c.getSkillId() > 0) {
                ConfSkill s = ConfSkill.get(c.getSkillId());
                if (checkPvpChapterTypeOk(s.chapter_type, r, s.if_chapter_type)){
                    Skill b = UnitMgr.addSkill(i, c.getSkillId(), c.getSkillLv());
                    if (l != null && l[0] == c.getSkillId())
                    {
                        if (b.skillEffectList == null)
                            b.skillEffectList =new ArrayList<>();
                        if (b.skillDamList == null)
                            b.skillDamList =new ArrayList<>();
                        b.skillEffectList.add(b.config.skillEffect2);
                        b.skillDamList.add(b.skillDam);
                        setSkillEffect(b, l[1]);
                        setSkillEffect(b, l[2]);
                    }
                }
            }
        }
    }

    private static boolean checkPvpChapterTypeOk(int[][] t, int e, int i) {
        if (null == t || t.length <= 0)
            return true;
        if (0 == i)
        {
            for (int a = 0; a < t.length; a++) {
                if (t[a][0] == e)
                    return true;
            }
            return false;
        }
        boolean l = true;
        for (int a = 0; a < t.length; a++) {
            if (t[a][0] == e)
                l = false;
        }
        return l;
    }

    private static void setPlayerPets(PlayerData t, List<Define.p_role_pet> i, Map<Integer, Map<Long, Long>> r) {
        if (i == null || i.size() <= 0)
            return;
        for (Define.p_role_pet f : i) {
            if (0 == f.getPetId())
                continue;
            ConfPet v = ConfPet.get((int) f.getPetId());
            ConfPetlevel_0 c = ConfPetlevel_0.get((int) f.getPetId(), f.getPetLev());
            UnitData d = new UnitData();
            d.attribs = new HashMap<>();
            d.roleId = t.Id;
            ConfUnit g = ConfUnit.get(Utils.longValue(f.getSkinId() > 0 ? ConfPetSkin.get(f.getSkinId()).unitId :v.unitId));
            d.setConfig(g);
            for (Integer sn : GlobalConfVal.propNameSnModule1Map.values()) {
                ConfAttribute k = ConfAttribute.get(sn);
                MetaAttrib xhx = new MetaAttrib(k, null);
                int value = c.getFieldValue(k.key) == null ? 0 : c.getFieldValue(k.key);
                xhx.setBaseValue(getPetFactAttrValue(r, value, f.getPetId(), k.sn));
                d.attribs.put(AttribDefine.fromValue(k.sn), xhx);
            }
            d.attribs.get(AttribDefine.hp).setBaseValue(t.units.get(0).attribs.get(AttribDefine.hp).getBaseValue());
            d.attribs.get(AttribDefine.att).setBaseValue(t.units.get(0).attribs.get(AttribDefine.att).getBaseValue());
            d.attribs.get(AttribDefine.partner_dam_extra).setBaseValue(t.units.get(0).attribs.get(AttribDefine.partner_dam_extra).getBaseValue());
            d.attribs.get(AttribDefine.skill_dam_extra).setBaseValue(t.units.get(0).attribs.get(AttribDefine.skill_dam_extra).getBaseValue());
            d.attribs.get(AttribDefine.skill_crit_rate).setBaseValue(t.units.get(0).attribs.get(AttribDefine.skill_crit_rate).getBaseValue());
            d.attribs.get(AttribDefine.skill_crit_dam).setBaseValue(t.units.get(0).attribs.get(AttribDefine.skill_crit_dam).getBaseValue());
            d.attribs.get(AttribDefine.boss_dam).setBaseValue(t.units.get(0).attribs.get(AttribDefine.boss_dam).getBaseValue());
            d.idleIndex = f.getPetPos() + 1;
            t.units.add(d);
        }
    }

    private static void setPlayerFlyPet(PlayerData t, int i) {
        ConfFly a = ConfFly.get(i);
        if (null != a)
        {
            ConfUnit r = ConfUnit.get((long)a.unitid);
            UnitData l = new UnitData();
            l.attribs = new HashMap<>();
            l.roleId = t.Id;
            l.setConfig(r);
            l.skillList = new ArrayList<>();
            for (Integer sn : GlobalConfVal.propNameSnModule1Map.values()) {
                ConfAttribute c = ConfAttribute.get(sn);
                MetaAttrib v = new MetaAttrib(c, null);
                v.setBaseValue(Utils.floatValue(r.getFieldValue(c.key)));
                l.attribs.put(AttribDefine.fromValue(c.sn), v);
            }
            l.idleIndex = 8;
            t.units.add(l);
        }
    }
}
