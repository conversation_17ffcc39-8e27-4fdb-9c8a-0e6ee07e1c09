package org.gof.demo.battlesrv.battle.bentity;
import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.module.UnitData;
import org.gof.demo.battlesrv.battle.module.aI.DPVPPlayerAI;
import org.gof.demo.battlesrv.battle.module.aI.IAIHandler;

public class UnitDPVPPlayer extends UnitHuman {
    public UnitDPVPPlayer(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public void SetHpBar(UnitData _data) {
        // 实现SetHpBar方法
    }

    @Override
    public IAIHandler newUnitAI(UnitData _data) {
        return new DPVPPlayerAI();
    }

}
