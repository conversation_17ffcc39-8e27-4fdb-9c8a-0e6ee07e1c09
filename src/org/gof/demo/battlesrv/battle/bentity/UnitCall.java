package org.gof.demo.battlesrv.battle.bentity;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.module.UnitData;

public class UnitCall extends UnitHuman {
    public float lifeTime;

    public UnitCall(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public void SetHpBar(UnitData _data) {

    }

    @Override
    public void OnLastUpdate(float deltaTime) {
        super.OnLastUpdate(deltaTime);
        if (-1 != this.lifeTime) {
            if (this.lifeTime <= 0)
                this.dead(true);
            else
                this.lifeTime = this.lifeTime - deltaTime;
        }
    }
}

