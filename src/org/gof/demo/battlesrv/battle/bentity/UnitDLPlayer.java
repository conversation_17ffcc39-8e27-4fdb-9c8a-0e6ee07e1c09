package org.gof.demo.battlesrv.battle.bentity;
import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.enumVo.DmgType;
import org.gof.demo.battlesrv.battle.module.UnitData;
import org.gof.demo.battlesrv.battle.module.aI.DLPlayerAI;
import org.gof.demo.battlesrv.battle.module.aI.IAIHandler;
import org.gof.demo.battlesrv.battle.module.aI.T20PlayerAI;

public class UnitDLPlayer extends UnitHuman {
    public UnitDLPlayer(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public void SetHpBar(UnitData _data) {
        // 实现SetHpBar方法
    }

    @Override
    public IAIHandler newUnitAI(UnitData _data) {
        return new DLPlayerAI();
    }

//    @Override
//    public void onHpAction(long t, long i) {
//        // 实现onHpAction方法
//    }
//
//    @Override
//    public void onAddDamage(HpAction t, DmgType i, long n) {
//        // 实现onAddDamage方法
//    }
}
