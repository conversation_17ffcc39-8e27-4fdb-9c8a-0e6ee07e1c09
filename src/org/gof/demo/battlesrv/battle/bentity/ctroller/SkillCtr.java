package org.gof.demo.battlesrv.battle.bentity.ctroller;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.module.UnitData;
import org.gof.demo.battlesrv.battle.UnitMgr;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;
import org.gof.demo.battlesrv.battle.enumVo.UnitType;
import org.gof.demo.worldsrv.config.ConfSkill;
import org.gof.demo.worldsrv.config.ConfSkilleffcet;
import org.gof.demo.worldsrv.support.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SkillCtr {
    public class SkillEffect {
        public int id;
        public int num;
        public int bullet;
        public SkillRunner runner;
        public int triggerType;
        public int limit;
        public int useType;
        public String[] param5;

        public SkillEffect(int id, int num, int bullet, SkillRunner runner, int triggerType, int limit, int useType, String[] param5) {
            this.id = id;
            this.num = num;
            this.bullet = bullet;
            this.runner = runner;
            this.triggerType = triggerType;
            this.limit = limit;
            this.useType = useType;
            this.param5 = param5;
        }
    }

    protected List<SkillRunner> list = new ArrayList<>();
    protected UnitHuman _owner;
    public List<SkillEffect> skillEffects = new ArrayList<>();
    protected boolean _isLoadPassive = false;
    protected boolean _isLoadPetPassive = false;
    protected boolean _isLoadFlyPetPassive = false;
    protected List<SkillRunner> _passiveRunners = new ArrayList<>();
    protected Map<Integer, Long> _damageRecord = new HashMap<Integer, Long>();
    protected Map<Integer, Long> _HpChangeRecord = new HashMap<Integer, Long>();

    public boolean isLoadPassive() { return _isLoadPassive;};

    public void init(UnitHuman unit) {
        _owner = unit;
    }

    public void reset() {
        List<SkillRunner> t = this.list;
        for (int i = 0; i < t.size(); ++i) {
            SkillRunner n = t.get(i);
            n.stop();
            n.destroy();
        }
        this.list.clear();
        this.skillEffects.clear();
        this._damageRecord.clear();
        this._HpChangeRecord.clear();
        this._isLoadPassive = false;
        this._isLoadPetPassive = false;
    }

    public void loadPassiveSkill() {
        if (this._isLoadPassive)
            return;
        this._isLoadPassive = true;
        List<Skill> t = this._owner.data.passiveSkillList;
        if (null != t) {
            for (Skill r : t) {
                SkillRunner s = new SkillRunner();
                s.init(this._owner, r, false);
                s.start(true);
                this._passiveRunners.add(s);
                this.addRunner(s);
            }
        }
    }

    public void setRecordDamage(int t, long i)
    {
        this._damageRecord.put(t, i);
    }

    public long getRecordDamage(int t)
    {
        return this._damageRecord.getOrDefault(t, 0L);
    }

    public void setHpChange(int t, long i)
    {
        this._HpChangeRecord.put(t, i);
    }

    public long getHpChange(int t)
    {
        return this._HpChangeRecord.getOrDefault(t, 0L);
    }

    public void loadPetPassiveSkill() {
        if (this._isLoadPetPassive)
            return;
        this._isLoadPetPassive = true;

        List<Skill> t = this._owner.data.petPassiveSkillList;
        if (null != t && 0 != t.size()) {
            List<UnitHuman> e = this._owner.battleMain().unitMgr.findTarget(this._owner, TargetFilter.CastPartner, 0, this._owner.Position, true);
            for (Skill n : t) {
                loadPetPassiveSkill(n, e);
            }
        }
    }

    private void loadPetPassiveSkill(Skill t, List<UnitHuman> e) {
        int[][] i = t.config.addSkill;
        if (0 == i.length)
            return;
        List<Integer> r = new ArrayList<>();
        r.addAll(Utils.intArrToList(i[0]));

        for (UnitHuman n : e) {
            if (n.config().type == UnitType.Partner.getValue() && r.contains(n.config().petrace)) {
                Skill skill = UnitMgr.addSkill(n.data, i[1][0], 0);
                if (null != skill) {
                    skill.skillDam = t.skillDam;
                    skill.parentSkillId = t.config.sn;
                }
            }
        }
    }

    public void loadFlyPetPassiveSkill() {
        if (!_isLoadFlyPetPassive){
            _isLoadFlyPetPassive = true;
            List<Skill> t = this._owner.data.flyPetPassiveSkillList;
            if (null != t && !t.isEmpty()) {
                UnitHuman e = this._owner.flyPet;
                if (e != null) {
                    for (Skill s : t)
                    {
                        int[][] r = s.config.addSkill;
                        if (0 != r.length)
                        {
                            Skill l = UnitMgr.addSkill(e.data, r[1][0], 0);
                            l.skillDam = s.skillDam;
                            l.parentSkillId = s.config.sn;
                        }
                    }
                }
            }
        }
    }

    public void addChapterBuff(int t) {
        ConfSkill i = ConfSkill.get(t);
        if (null == i) {
            return;
        }
        Skill n = new Skill();
        n.config = i;
        n.level = 1;
        n.skillDam = i.skillPar;
        SkillRunner e = new SkillRunner();
        e.init(this._owner, n, false);
        e.start(true);
        this.addRunner(e);
    }

    private void _unloadPet(UnitHuman t, int i) {
        List<Skill> n = t.data.skillList;
        if (null != n && !n.isEmpty()) {
            for (int e = 0; e < n.size(); e++) {
                if (n.get(e).parentSkillId == i) {
                    n.remove(e);
                    break;
                }
            }
        }
    }

    public void unloadPetPassiveSkill() {
        if (this._isLoadPetPassive) {
            this._isLoadPetPassive = false;
            List<Skill> i = this._owner.data.petPassiveSkillList;
            if (null != i && 0 != i.size()) {
                List<UnitHuman> n = this._owner.battleMain().unitMgr.findTarget(this._owner, TargetFilter.CastPartner, 0, _owner.Position, true);

                for (Skill skill : i) {
                    for (UnitHuman unitHuman : n) {
                        if (unitHuman.config().type == UnitType.Partner.getValue())
                            this._unloadPet(unitHuman, skill.config.sn);
                    }
                }
            }
        }
    }


    public void unloadFlyPetPassiveSkill() {
        if (this._isLoadFlyPetPassive) {
            this._isLoadFlyPetPassive = false;
            List<Skill> i = this._owner.data.flyPetPassiveSkillList;
            if (null != i && !i.isEmpty()) {
                UnitHuman e = this._owner.flyPet;
                if (e != null) {
                    i.forEach(_i ->this._unloadPet(e, _i.config.sn));
                }
            }
        }
    }

    public void onUpdate(float t) {
        List<SkillRunner> i = this.list;
        for (int n = 0; n < i.size(); ++n) {
            SkillRunner e = i.get(n);
            e.onUpdate(t);
            if (e.isStop()) {
                e.stop();
                e.destroy();
                i.remove(n);
                n--;
            }
        }
        this.onSkillUpdate(t);
    }

    private void onSkillUpdate(float t) {
        UnitData i = this._owner.data;
        if (null != i.attack && 2 == i.attack.state) {
            float n = (1 / Math.max(.01f, i.getAttrib(AttribDefine.att_speed)));
            i.attack.currenCd = (i.attack.currenCd + t);
            i.attack.currenCd = Math.min(i.attack.currenCd, n);
            i.attack.state = i.attack.currenCd >= n ? 0 : 2;
        }
        if (null != i.skillList) {
            float e = i.getAttrib(AttribDefine.power_recovery);
            for (int a = 0; a < i.skillList.size(); ++a) {
                Skill l = i.skillList.get(a);
                if (2 == l.state) {
                    float o = ((l.config.powerRecovery * e) * t);
                    o = (o * this._owner.battleMain().skillCd);
                    l.currenPower = FixRandom.round(l.currenPower + i.getSkillFactAttrValue(o, l.config.sn, AttribDefine.power_recovery_buff.getValue()));
                    l.currenPower = Math.min(l.currenPower, l.config.maxPower);
                    l.currenCd = 0;
                    l.checkState();
                }
            }
        }
    }

    private int _findSkillEffect(int t) {
        for (int i = 0; i < this.skillEffects.size(); i++) {
            if (this.skillEffects.get(i).id == t)
                return i;
        }
        return -1;
    }

    public void addSkillEffect(int t, SkillRunner i, int n, int e, int r, String[] s) {
        int l = this._findSkillEffect(t);
        if(l==-1){
            SkillEffect o = new SkillEffect(t, 0, 0, i, n, e, r, s);
            if (0 == r) {
                ConfSkilleffcet u = ConfSkilleffcet.get(t);
                o.bullet = u.bullet;
            }
            this.skillEffects.add(o);
        }
    }

    public void removeSkillEffect(int t) {
        int i = this._findSkillEffect(t);
        if (-1 != i) {
            this.skillEffects.remove(i);
        }
    }

    public void addRunner(SkillRunner t) {
        this.list.add(t);
    }

    public Skill addSkill(int t) {
        Skill i = UnitMgr.newSkill(t, 1, this._owner.data);
        if(i == null){
            Log.temp.error("Skill not found t={}", t);
            return null;
        }
        i.triggerEffect = false;
        SkillRunner n = new SkillRunner();
        n.init(this._owner, i, true);
        n.start();
        this.addRunner(n);
        return i;
    }

    public void interrupt() {
        List<SkillRunner> t = this.list;
        for (int i = 0; i < t.size(); ++i) {
            t.get(i).interrupt();
        }
    }

    private void onBeHitAction(UnitHuman t, Object i, Object n) {
        if (1 != t.data.unitType().target) {
            List<SkillRunner> e = this.list;
            for (int r = 0; r < e.size(); ++r) {
                e.get(r).onBeHitAction(t, i, n);
            }
        }
    }

    private void onActTargetAction(UnitHuman t, Object i) {
        List<SkillRunner> n = this.list;
        for (int e = 0; e < n.size(); ++e) {
            SkillRunner r = n.get(e);
            if (r.isPassive())
                r.onActTargetAction(t, i);
        }
    }

    public void onDeadTargetAction() {
    }
}