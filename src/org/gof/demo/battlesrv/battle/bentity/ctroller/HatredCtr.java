package org.gof.demo.battlesrv.battle.bentity.ctroller;

import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.MathUtil;
import org.gof.demo.battlesrv.battle.enumVo.StateType;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;

import java.util.ArrayList;
import java.util.List;

public class HatredCtr {
    public class Hatred {
        public UnitHuman unit;
        public long _hatredValue;

        public Hatred(UnitHuman unit, long hatredValue) {
            this.unit = unit;
            this._hatredValue = hatredValue;
        }

        public long _hatredValue() {
            return this._hatredValue + this.unit.tauntValue;
        }
    }

    protected UnitHuman _owner;
    protected List<Hatred> _hatredList = new ArrayList<Hatred>();

    public int length() {
        return this._hatredList.size();
    }

    public void init(UnitHuman unit) {
        this._owner = unit;
    }

    public void reset() {
        this.clearHatredList();
    }

    public void onUpdate(float t) {
        UnitHuman e = this._owner;
        if (e.statectr().currentStateType() != StateType.Born) {
            if (0 != (e.config().hatred_type & BattleParamKey.UnitConfig_HATE_WARRING_CAST)) {
                List<UnitHuman> i = e.battleMain().unitMgr.findTarget(e, TargetFilter.Enemy, e.data.warningRange(), e.Position, true);
                for (int n = 0; n < i.size(); ++n) {
                    UnitHuman r = i.get(n);
                    if (!r.dead) {
                        if (0 == (r.config().hatred_type & BattleParamKey.UnitConfig_HATE_WARRING_TARGET))
                            continue;
                        this.insertWarningRange(r);
                    }
                }
            }
            for (int d = 0; d < this._hatredList.size(); ++d) {
                if (this._hatredList.get(d).unit.dead) {
                    //u.free(this._hatredList[d]);
                    this._hatredList.remove(d);
                    d--;
                }
            }
        }
    }

    public void insertWarningRange(UnitHuman t) {
        if (this._owner.dead || this.findIndex(t) >= 0)
            return;
        this._hatredList.add(new Hatred(t, 1));
    }

    public void addSkillHatred(UnitHuman t, long e) {
        UnitHuman i = this._owner;
        if (t != i) {
            if (i.dead || e <= 0 || 0 != (i.config().hatred_type & BattleParamKey.UnitConfig_HATE_SKILL_CAST) && 0 != (t.config().hatred_type & BattleParamKey.UnitConfig_HATE_SKILL_TARGET))
                this.addHatred(t, e);
        }
    }

    public void addHurtHatred(UnitHuman t) {
        addHurtHatred(t, 1);
    }

    public void addHurtHatred(UnitHuman t, long e) {
        UnitHuman i = this._owner;
        if (t != i) {
            if (i.dead || 0 != (i.config().hatred_type & BattleParamKey.UnitConfig_HATE_HURT_CAST) && 0 != (t.config().hatred_type & BattleParamKey.UnitConfig_HATE_HURT_TARGET))
                this.addSkillHatred(t, Math.max(e, 1));
        }
    }

    public void addHatred(UnitHuman t, long e) {
        List<Hatred> i = this._hatredList;
        int n = this.findIndex(t);
        if (-1 == n)
            i.add(new Hatred(t, e));
        else {
            Hatred a = i.get(n);
            a._hatredValue = a._hatredValue() + e;
        }
    }

    public void clearHatredList() {
        for (int e = 0; e < this._hatredList.size(); ++e)
            this._hatredList.set(e, null);
        this._hatredList.clear();
    }

    public int findIndex(UnitHuman t) {
        for (int e = 0; e < this._hatredList.size(); ++e) {
            if (this._hatredList.get(e).unit == t)
                return e;
        }
        return -1;
    }

    public UnitHuman getMaxHatredUnit() {
        List<Hatred> t = this._hatredList;
        if (t.size() == 0)
            return null;
        Hatred e = t.get(0);
        UnitHuman i = this._owner;
        for (int n = 1; n < t.size(); ++n) {
            Hatred a = t.get(n);
            if (a.unit.dead)
                continue;
            if (e.unit.dead)
                e = a;
            else {
                if (a._hatredValue() == e._hatredValue()) {
                    if (MathUtil.DistanceX(i.Position, a.unit.Position) < MathUtil.DistanceX(i.Position, e.unit.Position))
                        e = a;
                } else if (a._hatredValue() > e._hatredValue())
                    e = a;
            }
        }
        return e.unit;
    }

    public List<Hatred> getHatredList() {
        return this._hatredList;
    }


}
