package org.gof.demo.battlesrv.battle.bentity.ctroller;

import org.gof.core.support.Utils;
import org.gof.core.support.function.GofFunction0;
import org.gof.core.support.function.GofFunction1;
import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.worldsrv.config.ConfUnitModel;
import org.gof.demo.worldsrv.support.Log;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

public class AnimatorCtr {

    public void doAction(IAction<int[]> iAction, int[] param) {
        if (iAction == null) {
            return;
        }
        iAction.execute(param);
    }

    public void doAction(IAction iAction) {
        if (iAction == null) {
            return;
        }
        iAction.execute();
    }

    private List<GofFunction1<int[]>> OnAction = new ArrayList<>();
    private List<GofFunction0> OnEnd = new ArrayList<>();

    // 添加事件监听器的方法
    public void addActionListener(GofFunction1<int[]> listener) {
        OnAction.add(listener);
    }

    // 移除事件监听器的方法
    public void removeActionListener(GofFunction1<int[]> listener) {
        OnAction.remove(listener);
    }

    // 触发事件的方法
    public void triggerActionEvent(int[] parameters) {
        for (GofFunction1<int[]> listener : OnAction) {
            listener.apply(parameters);
        }
    }

    // 添加事件监听器的方法
    public void addActionListenerEnd(GofFunction0 listener) {
        OnEnd.add(listener);
    }

    // 移除事件监听器的方法
    public void removeActionEndListener(GofFunction0 listener) {
        OnEnd.remove(listener);
    }

    // 触发事件的方法
    public void triggerActionEventEnd() {
        List<GofFunction0> list = new ArrayList(OnEnd);
        for (GofFunction0 listener : list) {
            listener.apply();
        }
    }

    protected String _aniName = BattleParamKey.AniNames_Run;
    protected boolean _reset = false;
    protected int[][] _currenModeState;
    protected float _curFrame;
    protected float _speed = 1;
    protected int _static = 1;
    protected List<Integer> _indexList = new ArrayList<>();
    protected float _stateSpeed = 1;
    protected boolean _changeData = false;
    protected UnitHuman _owner;

    public ConfUnitModel config;

    public void setConfig(ConfUnitModel conf) {
        this.config = conf;
    }

    public String aniAddName;

    public String getAniAddName() {
        return aniAddName;
    }

    public void setAniAddName(String aniAddName) {
        this.aniAddName = aniAddName;
    }

    public float speed;
    public String aniName;
    public boolean aniReset;
    public boolean changeData;

    public void setSpeed(float speed) {
        this.speed = speed;
    }

    public void setAniName(String aniName) {
        this.aniName = aniName;
    }

    public void setAniReset(boolean aniReset) {
        this.aniReset = aniReset;
    }

    public void setChangeData(boolean changeData) {
        this.changeData = changeData;
    }

    public float timeScale() {
        return this._speed * (this._static * this._stateSpeed);
    }

    public float curFrame() {
        return _curFrame;
    }

    public AnimatorCtr() {
        this._aniName = "run";
        this._reset = false;
        this._speed = 1;
        this._static = 1;
        this._indexList = new ArrayList<>();
        this._stateSpeed = 1;
        this._changeData = false;
    }

    public void init(UnitHuman t) {
        this._owner = t;
        this.config = t.data.modelConfig();
    }

    public void reset() {
        OnAction.clear();
        OnEnd.clear();

        this._currenModeState = null;
        this._indexList.clear();
        this._curFrame = 0;
        this._static = 1;
        this._stateSpeed = 1;
        this.speed = 1;
    }

    public boolean hasState(String t) {
        return this._aniName.equals(t);
    }

    public void changeState(String t) {
        changeState(t, true, 1);
    }

    public void changeState(String t, boolean e, float i)// e= true, i=1
    {
//        Log.temp.info("changeState {} 限制改变施放者动作 {} {}", this.config.sn,t,i);
        this.refreshModeState(t);
        this._stateSpeed = i;
        this._aniName = t;
        this._reset = e;
        this._changeData = true;
    }

    public void refreshModeState(String t) {
        this._currenModeState = getModeState(t);
        this._stateSpeed = 1;
        this._indexList.clear();
        this._curFrame = 0;
    }

    private Class<?> clazz;
    private int[][] getModeState(String fieldName) {
        try {
            if(clazz == null) {
                clazz = this.config.getClass();
            }
            Field field = clazz.getField(fieldName);
            return (int[][]) field.get(this.config);
        } catch ( Exception ignored) {

        }
        return null;
    }

    public void onUpdate(float t) {
        if (0 == this.timeScale())
            return;
        if (null != this._currenModeState) {
            int[][] e = this._currenModeState;
            int[] i = e[0];
            if (this._indexList.size() < i.length - 1) {
                for (int n = 1; n < i.length; n++) {
                    if (!this._indexList.contains(n)) {
                        int r = i[n];
                        if (this._curFrame >= r) {
                            this._indexList.add(n);
                            triggerActionEvent(e[1]);
                            break;
                        }
                    }
                }
            }

            if (this._curFrame >= i[0]) {
                this._curFrame = 0;
                this._currenModeState = null;
                triggerActionEventEnd();
                return;
            }

            this._curFrame = Math.min(this._curFrame + this.timeScale(), i[0]);
//            Log.temp.info("onUpdate {} _curFrame {}", this.config.sn,_curFrame);
        } else
            this._curFrame = (this._curFrame + this.timeScale());
    }
}