package org.gof.demo.battlesrv.battle.bentity.ctroller;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.state.*;
import org.gof.demo.battlesrv.battle.enumVo.StateType;

import java.util.ArrayList;
import java.util.List;

public class StateCtr {
    protected UnitHuman _owner;
    public State currentState;
    public boolean lockCurrenState;
    protected List<State> _stateList = new ArrayList<>();
    protected int _exitCount = 0;
    public int banSkill = 0;
    public int banAct = 0;
    public int dizz = 0;
    protected int Static = 0;
    public int invincible = 0;
    public int bound = 0;
    public int notControlled = 0;
    public int beControlled = 0;
    public int notGetDamage = 0;
    public boolean needChangeState = true;

    public State currentState() {
        return currentState;
    }

    public void setCurrentState(State value) {
        currentState = value;
    }

    public StateType currentStateType() {
        return null == this.currentState ? StateType.Dead : this.currentState.type;
    }

    public StateCtr() {
        _stateList = new ArrayList<>();
        _stateList.add(new UnitIdleState(this, 1));
        _stateList.add(new UnitMoveState(this, 3));
        _stateList.add(new UnitHitState(this, 2));
        _stateList.add(new UnitSkillState(this, 4));
        _stateList.add(new UnitDizzState(this, 5));
        _stateList.add(new UnitDeadState(this, 99));
        _stateList.add(new UnitBornState(this, 90));
        _stateList.add(new UnitOverState(this, 100));
        _stateList.add(new UnitHitThrowState(this, 8));
    }

    public void init(UnitHuman t) {
        if (this._owner != t) {
            this._owner = t;
            for (State state : _stateList) {
                state._owner = t;
            }
        }
    }

    public UnitHuman getOwner(){
        return this._owner;
    }

    public void reset() {
        this.banSkill = this.Static = this.dizz = 0;
        this.invincible = this.bound = this.notControlled = this.beControlled = this.notGetDamage = 0;
        this.currentState = null;
        this.needChangeState = true;
        this.lockCurrenState = false;
    }

    public void onUpdate(float t) {
        if (this.dizz > 0 && this.currentState.type != StateType.Dizz)
            this.changeStateType(StateType.Dizz);

        if (null != this.currentState)
            this.currentState.onUpdate(t);
    }

    private void changeState(State t) {
        if (this.checkState(t)) {
            this._exitCount++;
            int e = this._exitCount;
            if (this.currentState != null) {
                this.currentState.onExit();
            }
            if (e == this._exitCount) {
                this._exitCount--;
                this.currentState = t;
                this.currentState.onEnter();
            } else
                this._exitCount--;
        }
    }

    public State changeStateType(StateType t) {
        State e = this.getState(t);
        this.changeState(e);
        return e;
    }

    private boolean checkStateType(StateType t) {
        State e = this.getState(t);
        return this.checkState(e);
    }

    public boolean checkState(State t) {
        return !(this.currentState == t && !this.currentState.isRepeat) && (!!this.needChangeState && !(this.lockCurrenState && this.currentState.priority() > t.priority()));
    }

    public <T extends State> T getState(StateType t) {
        for (State state : _stateList) {
            if (state.type == t) {
                return (T) state;
            }
        }
        return null;
    }
}
