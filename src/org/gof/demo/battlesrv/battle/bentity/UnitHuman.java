package org.gof.demo.battlesrv.battle.bentity;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.*;
import org.gof.demo.battlesrv.battle.bentity.ctroller.*;
import org.gof.demo.battlesrv.battle.module.*;
import org.gof.demo.battlesrv.battle.module.aI.IAIHandler;
import org.gof.demo.battlesrv.battle.module.aI.PlayerAI;
import org.gof.demo.battlesrv.battle.module.buff.Buff;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.module.state.UnitDeadState;
import org.gof.demo.battlesrv.battle.module.state.UnitHitThrowState;
import org.gof.demo.battlesrv.battle.module.state.UnitIdleState;
import org.gof.demo.battlesrv.battle.enumVo.*;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfBuff;
import org.gof.demo.worldsrv.config.ConfUnit;
import org.gof.demo.worldsrv.config.ConfUnitModel;
import org.gof.demo.worldsrv.config.ConfMount;
import org.gof.demo.worldsrv.entity.Unit;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.support.Log;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-05-28 12:17
 **/
public class UnitHuman {
    public BattleMain battleMain;

    public BattleMain battleMain() {
        return battleMain;
    }

    public int unitId;
    public int teamId = 0;
    public UnitData data;
    public Object flag;
    private IAIHandler aiRoot;
    public boolean isCallType = false;
    public UnitHuman parent;
    public UnitHuman flyPet;
    public boolean shamUnit = false;
    public boolean dead = false;
    public StateCtr statectr;
    public AnimatorCtr animatorctr;
    public MovementCtr movectr;
    public SkillCtr skillctr;
    public HatredCtr hatredCtr;
    public BuffCtr buffCtr;
    public int radiusSize = 0;
    public int skillAutoDis;
    public int normalActCount = 0;
    public SkillRunner currenSkillRunner;
    public long tauntValue = 0;
    public TargetUnit wantTarget;
    public TargetUnit curTarget;
    private TargetPoint nexPoint;
    public TargetSkill wantSkill;
    private TargetSkill curSkill;
    private List<Skill> unlimitSkill = new ArrayList<>();
    private Skill skillToSkill;

    public void setSkillToSkill(Skill skill) {
        skillToSkill = skill;
    }

    public int stopAI = 0;
    public boolean destroy = false;
    private List<HpAction> healthActions = new ArrayList<>();

    public List<Define.p_guild_boss_combat> hpActionLogList = new ArrayList<>();
    public List<Define.p_battle_hp_state> hpStateLogList = new ArrayList<>();


    private List<UnitHuman> counterList = new ArrayList<>();
    private List<ReturnAction> _returnList;
    public Vector2D[] bindPos; // 假设Vector2是一个已经定义的类
    public float curSecond = 0;
    public long hurtCount = 0;
    public int hurtNumCount = 0;
    public float aiTime = 0;

    public Vector2D D;
    private List<Integer> P = new ArrayList<>(Arrays.asList(1, 3));

    public float attackDistance() {
        return this.data.attribs.get(AttribDefine.att_range).getValue();
    }

    public ConfUnit config() {
        return this.data.config();
    }

    public Vector2D Position;

    public int direction;

    public boolean isDestroy() {
        return this.destroy;
    }

    public boolean isDead() {
        return this.dead;
    }

    public boolean visible;

    public TargetSkill curSkill() {
        return this.curSkill;
    }

    public StateCtr statectr() {
        return statectr;
    }

    public void setCurSkill(TargetSkill value) {
        if (this.curSkill != value) {
            if (null != this.curSkill)
                TargetSkill.free(this.curSkill);
            this.curSkill = value;
        }
    }

    public TargetSkill wantSkill() {
        return this.wantSkill;
    }

    public void setWantSkill(TargetSkill value) {
        if (this.wantSkill != value) {
            if (null != this.wantSkill) {
                if (null != value && this.wantSkill.getPriority() > value.getPriority()) {
                    TargetSkill.free(value);
                    return;
                }
                TargetSkill.free(this.wantSkill);
            }
            this.wantSkill = value;
        }
    }

    public TargetPoint nextPoint() {
        return this.nexPoint;
    }

    public void setNextPoint(TargetPoint value) {
        {
            if (this.nexPoint != value) {
                if (null != this.wantSkill) {
                    if (null != value && this.wantSkill.getPriority() > value.getPriority()) {
                        TargetPoint.free(value);
                        return;
                    }
                    TargetPoint.free(this.nexPoint);
                }
                this.nexPoint = value;
            }
        }
    }

    public TargetUnit curTarget() {
        return this.curTarget;
    }

    public void setCurTarget(TargetUnit value) {
        if (this.curTarget != value) {
            if (null != this.curTarget) {
                TargetUnit.free(this.curTarget);
            }
            this.curTarget = value;
        }
    }

    public TargetUnit wantTarget() {
        return this.wantTarget;
    }

    public void setWantTarget(TargetUnit value) {
        if (this.wantTarget != value) {
            if (null != this.wantTarget) {
                if (null != value && this.wantTarget.getPriority() > value.getPriority()) {
                    TargetUnit.free(value);
                    return;
                }
                TargetUnit.free(this.wantTarget);
            }
            this.wantTarget = value;
        }
    }

    public int stopAI() {
        return this.stopAI;
    }

    public void setStopAI(int value) {
        this.stopAI = Math.max(0, value);
        if (this.stopAI > 0)
            this._stop();
    }

    public int depth;

    public int getHatredType() {
        return this.config().hatred_type;
    }

    public int weapon()
    {
        return this.data.weapon;
    }

    public UnitHuman(BattleMain battleMain) {
        this.unlimitSkill = new ArrayList<>();
        this.healthActions = new ArrayList<>();
        this.battleMain = battleMain;
    }

    public void init(UnitData _data) {
        this.data = _data;
        this.dead = false;
        this.destroy = false;

        BattleMain i = this.battleMain;
        ConfUnitModel confUnitModel = this.data.modelConfig();

        this.radiusSize = confUnitModel.radius;
        this.SetHpBar(_data);

        if (this.statectr == null) {
            this.statectr = new StateCtr();
            this.animatorctr = new AnimatorCtr();
            this.movectr = new MovementCtr();
            this.skillctr = new SkillCtr();
            this.hatredCtr = new HatredCtr();
            this.buffCtr = new BuffCtr();
        }
        this.statectr.init(this);
        this.animatorctr.init(this);
        this.movectr.init(this);
        this.skillctr.init(this);
        this.hatredCtr.init(this);
        this.buffCtr.init(this);

        this.aiRoot = newUnitAI(_data);
        this.aiRoot.cast = this;

        this.bindPos = new Vector2D[3];
        this.bindPos[0] = confUnitModel.bp_lead == null ? new Vector2D() : new Vector2D(confUnitModel.bp_lead[0], confUnitModel.bp_lead[1]);
        this.bindPos[1] = confUnitModel.bp_bottom == null ? new Vector2D() : new Vector2D(confUnitModel.bp_bottom[0], confUnitModel.bp_bottom[1]);
        this.bindPos[2] = confUnitModel.bp_top == null ? new Vector2D() : new Vector2D(confUnitModel.bp_top[0], confUnitModel.bp_top[1]);

        this.statectr.changeStateType(StateType.Idle);
    }

    public void SetHpBar(UnitData _data) {
    }

    public IAIHandler newUnitAI(UnitData _data) {
        return ClassFactory.aiMap(_data.config().ai);
    }

    public void OnUpdate(float deltaTime) {
        this.animatorctr.onUpdate(deltaTime);
        this.statectr.onUpdate(deltaTime);
        this.movectr.onUpdate(deltaTime);
        this.skillctr.onUpdate(deltaTime);
        this.buffCtr.onUpdate(deltaTime);
    }


    public void OnLastUpdate(float deltaTime) {
        this._onCounterAction();
        this._onHpAction();
        if (this.data.currenHp > 0){
            this._onSkillReturnAction();
            this._checkSkillCounter();
        }
        this.skillctr.loadPassiveSkill();
        this.skillctr.loadPetPassiveSkill();
        this.skillctr.loadFlyPetPassiveSkill();
        if (this.flyPet != null)
        {
            for (int i = AttribDefine.min.getValue(); i <= AttribDefine.max.getValue(); i++){
                MetaAttrib attrib = this.flyPet.data.attribs.get(AttribDefine.fromValue(i));
                if(attrib!=null){
                    attrib.setAttribValue(this.data.attribs.get(AttribDefine.fromValue(i)));
                }
            }
            if (this.data.currenHp > 0) this.flyPet.data.currenHp = this.data.currenHp;
        }
        if (this.curSecond >= 0.5f)
            this.curSecond -= 0.5f;
    }

    private void _onCounterAction() {
        if (counterList == null)
            return;
        for (int i = 0; i < counterList.size(); i++) {
            UnitHuman counter = counterList.get(i);
            if (!counter.dead && !counter.destroy) {
                SkillRunner skillRunner = new SkillRunner();
                skillRunner.init(this, this.data.counterAttack, true);
                skillRunner.setLockTarget(counter);
                skillRunner.start();
                skillctr.addRunner(skillRunner);
            }
        }
        counterList.clear(); // 清空列表
    }

    private void _onSkillReturnAction()
    {
        if (_returnList == null)
            return;
        for (int i = 0; i < _returnList.size(); i++){
            ReturnAction e = _returnList.get(i);
            if (!e.unit.dead && !e.unit.destroy)
            {
                Skill r = UnitMgr.newSkill(e.skill, e.lv, this.data);
                SkillRunner h = new SkillRunner();
                h.init(this, r, true);
                h.setLockTarget(e.unit);
                h.start();
                this.skillctr.addRunner(h);
            }
        }
        _returnList.clear();
    }

    private void _checkSkillCounter() {
        List<Buff> buffs = this.buffCtr.getBuffByType(BuffGroupType.SKILL_COUNTER.getValue());
        for (Buff a : buffs) {
            int r = a.config().param1;
            float h = a.config().param2;
            float l = 1;
            int u = 1;
            long d = this.hurtCount + this.skillctr.getHpChange(a.config().sn);
            switch (r) {
                case 1:
                    float c = Math.round(10000f * (this.hurtCount / this.data.getAttrib(AttribDefine.hp)));
                    if (c < h){
                        u = 0;
                        this.skillctr.setHpChange(a.config().sn, d);
                        continue;
                    }
                    u = (int)Math.floor(c / h);
                    this.skillctr.setHpChange(a.config().sn, FixRandom.roundInt(d - u * (this.data.getAttribByInt(AttribDefine.hp) * h / 10000f)));
                    l = FixRandom.round(c / h);
                    break;
                case 2:
                    if (this.hurtNumCount < h)
                        continue;
                    this.hurtNumCount = Math.round(this.hurtNumCount - h);
                    break;
                case 3:
                    long f = FixRandom.roundInt(10000 * FixRandom.round(d / this.data.getAttrib(AttribDefine.hp)));
                    if (f < h)
                    {
                        u = 0;
                        this.skillctr.setHpChange(a.config().sn, d);
                        continue;
                    }
                    u = (int)Math.floor(f / h);
                    this.skillctr.setHpChange(a.config().sn, FixRandom.roundInt(d - u * (this.data.getAttribByInt(AttribDefine.hp) * h / 10000)));
                    break;
            }
            for (int p = u; p > 0; p--) {
                int i = (int) a.config().param3;
                Skill k = UnitMgr.newSkill(i, 1, this.data);
                k.counterDamage = l;
                k.triggerEffect = false;
                SkillRunner skillRunner = new SkillRunner();
                skillRunner.init(this, k, true);
                skillRunner.start();
                this.skillctr.addRunner(skillRunner);
            }
        }
        this.hurtCount = 0;
    }

    public void onHpAction(long t, long i) {

    }

    public void onAddDamage(HpAction t, DmgType i, long e) {

    }

    private void _onHpAction() {
        List<HpAction> t = this.healthActions;
        long e = this.data.getAttribByInt(AttribDefine.hp);

        if (this.curSecond >= 0.5f) {
            double _n=e * this.data.getAttrib(AttribDefine.hp_recovery) * this.battleMain.treatDecay;
            long n = Math.round(_n);
            if (n > 0) {
                this.data.currenHp = this.data.currenHp + n;
                this.data.currenHp = Math.min(this.data.currenHp, e);
            }

            if (0 != t.size()) {
                long a = this.data.currenHp;
                long r = this.data.shieldHp;
                List<Buff> d = this.buffCtr.getBuffByType(BuffGroupType.BLOCK.getValue());
                List<Buff> h = r > 0 ? this.buffCtr.getBuffByType(BuffGroupType.SHIELD.getValue()) : null;
                List<Buff> l = this.data.getBuffState(SpBuffState.HpChangeTriger) > 0 ? this.buffCtr.getBuffByType(BuffGroupType.HP_CHANGE_TRIGER.getValue()) : null;
                List<Buff> u = this.data.getBuffState(SpBuffState.TotalDamageTrigger) > 0 ? this.buffCtr.getBuffByType(BuffGroupType.TOTAL_DAMAGE_TRIGGER.getValue()) : null;

                List<Buff> f = this.buffCtr.getBuffByType(BuffGroupType.IMMUNE_DEATH.getValue());
                List<Buff> b = this.buffCtr.getBuffByType(BuffGroupType.REMAKE_HP.getValue());
                List<Buff> v = this.buffCtr.getBuffByType(BuffGroupType.ATTRIB_CONDITION.getValue());

                for (Buff c : v) {
                    c.updateAttrib();
                }

                long C = 0L;
                boolean H = false;
                for (int i = 0; i < t.size(); i++)
                {
                    HpAction M = t.get(i);
                    long D = M.health;

                    //Log.temp.info("===m_atkId={}, m_def={}, curTarget={}, {} {}", M.attackerId, M.defenseId, this.curTarget.getType(), this.curTarget.getUnit().data.roleId);

                    switch (M.healthType) {
                        case Hurt:
                        case Hurt_Crit:
                        case Hurt_Ret:
                        case Hurt_Share_Damage:
                        case Hurt_Share_Damage_Crit:
                        case Hurt_Double:
                        case Hurt_Double_Crit:
                        case Hurt_Bleed:
                            if (this.battleMain.runningToPart)
                                continue;
                            D = Math.max(Math.round((double) (D / this.battleMain.injuryReduce)), 1);
                            M.health = D;
                            H = P.contains(M.skillId);
                            if (h != null && this.data.shieldHp > 0) {
                                long B = D;
                                for (Buff L : h) {
                                    D -= L.onShieldAction(D);
                                    if (this.data.shieldHp <= 0)
                                        break;
                                }
                            }

                            if (D > 0) {
                                if (d != null && d.size() > 0)
                                    D -= d.get(0).onShieldAction(D);
                                C += D;
                                this.data.currenHp = this.data.currenHp - D;
                                if (!this.data.remakeLock)
                                {
                                    boolean j = false;
                                    for (Buff N : b)
                                    {
                                        if (N.checkTarget(this.data.currenHp))
                                            j = true;
                                    }

                                    if (!j)
                                    {
                                        for (Buff x : f)
                                            x.checkTarget(this.data.currenHp);
                                    }
                                }

                                if (this.data.immuneDeath|| this.data.remakeLock)
                                    this.data.currenHp = Math.max(this.data.currenHp, 1);
                                else
                                    this.data.currenHp = Math.max(this.data.currenHp, 0);
                                this.data.totalDamage = this.data.totalDamage + D;
                                if (l != null && this.data.currenHp > 0) {
                                    for (Buff R : l)
                                        R.onDamage(this.data.currenHp);
                                }

                                if (u != null && this.data.currenHp > 0) {
                                    for (Buff E : u)
                                        E.onTotalDamage(M.attackerId);
                                }
                            }

                            if (this.battleMain.data.chapterType == org.gof.demo.worldsrv.instance.InstanceConstants.LEAGUEGVECHAPTER_7) {
                                Define.p_guild_boss_combat.Builder bossData = Define.p_guild_boss_combat.newBuilder();
                                bossData.setAtkId(M.attackerId);
                                bossData.setDefType(this.data.unitType().sn);// 1玩家 5boss
                                bossData.setDefId(this.data.roleId);
                                bossData.setHurt(D);
                                bossData.setIsKill(this.dead || (this.data.unitType().sn == 1 && this.data.currenHp <= 0) ? 1 : 0);
                                bossData.setType(1);// 1 伤害 0治疗
                                hpActionLogList.add(bossData.build());
                                this.battleMain.addAttackCauseDamage(M.attackerId, D);
                            }
                            break;
                        case Treat:
                        case Treat_Crit:
                        case Skill_Hpsteal:
                        case Act_Hpsteal:
                            if (M.healthType == HealthType.Treat)
                                D = Math.round((double)(D * this.battleMain.treatDecay));
                            C -= -D;
                            this.data.currenHp = this.data.currenHp + D;
                            this.data.currenHp = Math.min(this.data.currenHp, e);

                            if(this.battleMain.data.chapterType == org.gof.demo.worldsrv.instance.InstanceConstants.LEAGUEGVECHAPTER_7) {
                                Define.p_guild_boss_combat.Builder bossDataTreat = Define.p_guild_boss_combat.newBuilder();
                                bossDataTreat.setAtkId(M.attackerId);
                                bossDataTreat.setDefType(this.data.unitType().sn);// 1玩家 5 boss
                                bossDataTreat.setDefId(this.data.roleId);
                                bossDataTreat.setHurt(D);
                                bossDataTreat.setIsKill(this.dead ? 1 : 0);
                                bossDataTreat.setType(0);// 1 伤害 0治疗
                                hpActionLogList.add(bossDataTreat.build());
                            }
                            break;
                        case Miss:
                            break;
                    }
                    if (this.data.currenHp <= 0)
                        break;
                }
                t.clear();

                this.data.currenHp = Math.max(this.data.currenHp, 0);
                if (this.data.currenHp <= 0 && !this.isCallType && this.battleMain.data.recordType > 0 && this.battleMain.data.recordWin == this.data.roleId)
                    this.data.currenHp = 1;

                long F = this.data.currenHp - a;
                if (F < 0) {
                    this.hurtCount = this.hurtCount + Math.abs(F);
                    this.onHpAction(C, F);
                }
                Log.battle.debug("_onHpAction {} {}",this.unitId,this.data.currenHp);
                if (this.data.currenHp <= 0) {
                    this.dead(true);
                } else if (this.statectr.notControlled <= 0 && F < 0) {
                    if (this.statectr.currentStateType() == StateType.Idle && H) {
                        if (this.statectr.currentState instanceof UnitIdleState) {
                            if (((UnitIdleState) (this.statectr.currentState)).freeNum >= 2)
                                this.statectr.changeStateType(StateType.Hit);
                        }

                    }
                }

                if (!this.dead && this.battleMain.data.chapterType == org.gof.demo.worldsrv.instance.InstanceConstants.LEAGUEGVECHAPTER_7) {
                    if (this instanceof UnitCall) {
                        // 召唤物不用给客户端同步血量
                        return;
                    }
                    Define.p_battle_hp_state.Builder hpState = Define.p_battle_hp_state.newBuilder();
                    hpState.setId(0);
                    hpState.setUnitType(this.data.unitType().sn);// ParamKey.unitType_1 或 ParamKey.unitType_5
                    hpState.setRoleId(this.data.roleId);
                    if (this instanceof UnitBoss) {
                        UnitBoss boss = (UnitBoss) this;
                        hpState.setUnitIndex(boss.getIndex());
                    } else {
                        hpState.setUnitIndex(1);
                    }

                    hpState.setHp(this.data.currenHp);
                    hpStateLogList.add(hpState.build());
                }

//                Console.WriteLine("帧 " + this.battleMain.frameCount + ", 战斗对象 " + this.data.unitId + ", 血量 " + this.data.currenHp + ",总伤害 " + C + " ,状态 " + this.statectr.currentStateType);
//                Log.temp.error("帧 " + this.battleMain.frameCount + ", 战斗对象 " + this.data.unitId + ", 血量 " + this.data.currenHp + ",总伤害 " + C + " ,状态 " + this.statectr.currentStateType());
            }
        }
    }

    public void idle() {
        if (!this.animatorctr.hasState(BattleParamKey.UnitConfig_ANIMATOR_IDLE))
            this.animatorctr.changeState(BattleParamKey.UnitConfig_ANIMATOR_IDLE);
        this.statectr.lockCurrenState = false;
    }

    public void destroy() {
        if (this.destroy)
            return;
        this.destroy = true;
        this.dead = true;
        this._stop();
        this.statectr.reset();
        this.animatorctr.reset();
        this.movectr.reset();
        this.skillctr.reset();
        this.hatredCtr.reset();
        this.buffCtr.reset();
        this.battleMain.unitMgr.destroyUnit(this);
    }

    public void destroyImmediate() {
    }

    private void _stop() {
        this.wantSkill = null;
        this.curSkill = null;
        this.wantTarget = null;
        this.curTarget = null;
        this.setNextPoint(null);
        this.movectr.stopMove();
        this.skillctr.interrupt();
        this.buffCtr.reset();
        this.currenSkillRunner = null;
    }

    public boolean throwHit(float t, float i, int e) {
        if (this.statectr.currentStateType() == StateType.HitThrow)
            return false;
        if (this.dead)
            return false;
        if (this.statectr.invincible > 0)
            return false;
        if (this.statectr.notControlled > 0)
            return false;
        this.statectr.changeStateType(StateType.HitThrow);
        ((UnitHitThrowState) this.statectr.getState(StateType.HitThrow)).setThrowHit(t, i, e);
        return true;
    }

    public void dead(boolean value)//value = true
    {
        if (this.dead)
            return;

        this.skillctr.onDeadTargetAction();

        this._stop();
        this.dead = true;

        this.changeDeadState(value);
    }

    public void hit(UnitHuman unit, long hp, HealthType type) {
       hit(unit,hp,type,0);
    }

    public void hit(UnitHuman unit, long hp, HealthType type, int skillId) {
        HpAction action = new HpAction(null == unit ? 0 : unit.unitId, this.unitId, hp, type, skillId);
        this.healthActions.add(action);
//        if (null != unit && unit.data.roleId == 600010000000000004L) {
//            Log.battle.error("[gve]战斗==" + Utils.toJSONString(action));
//        }
    }

    private Vector2D _getBindPos(float t, float i) {
        if (this.data.mount > 0) {
            int[] e = ConfMount.get(this.data.mount).binds[this.data.modelConfig().mountPos - 1];
            D = D.Sum(new Vector2D((t * this.direction) + (e[0] * this.direction),i + e[1]));
        }
        else{
            D = D.Sum(new Vector2D(t * this.direction,i));
        }
        return D;
    }

    public Vector2D getBindPos(BindType bindType) {
        D = this.Position;
        if (bindType.getValue() >0 && null != this.bindPos[bindType.getValue() - 1]) {
            Vector2D i = this.bindPos[bindType.getValue() - 1];
            return this._getBindPos((float) i.x, (float) i.y);
        }
        return D;
    }

    public Vector2D getBindPosByAction(int[] t) {
        D = this.Position;
        if (null != t)
            return _getBindPos(t[0], t[1]);
        Vector2D i = this.bindPos[BindType.bp_lead.getValue() - 1];
        return this._getBindPos((float) i.x, (float) i.y);
    }

    public void addReturnInfo(UnitHuman t, int i, int e)
    {
        if (_returnList == null)
            _returnList = new ArrayList<>();
        for (int n = 0; n < _returnList.size(); n++)
        {
            ReturnAction item = _returnList.get(n);
            if (item.unit.equals(t) && item.skill == i && item.lv == e)
                return;
        }
        ReturnAction item = new ReturnAction();
        item.unit =t;
        item.skill =i;
        item.lv =e;
        _returnList.add(item);
    }

    public void addCounter(UnitHuman unit) {
        if (counterList == null)
            counterList = new ArrayList<>();
        if (!counterList.contains(unit))
            counterList.add(unit);
    }

    public void changeDeadState() {
        changeDeadState(true);
    }

    public void changeDeadState(boolean value) {
        if (this.statectr.currentStateType() != StateType.Dead) {
            this.statectr.changeStateType(StateType.Dead);

            UnitDeadState deadState = this.statectr.getState(StateType.Dead);
            if (deadState != null) {
                deadState.setParam(value);
            } else {
                Log.temp.error("====changeDeadState出错");
            }
        }
    }

    public void applySkill(boolean t)// t=false
    {
        TargetSkill i = this.wantSkill;
        if (i != null) {
            this.curSkill = i;
            this.wantSkill = null;
        }
        if (null != this.curSkill && null != this.curSkill.getSkill() && 0 != this.curSkill.getSkill().state)
            this.curSkill = null;
        if (t)
            this.curSkill = null;
    }

    public void applyPostion() {
        TargetPoint t = this.nexPoint;
        if (t != null) {
            if (t.priority != TargetPoint.TP_Move)
                this.movectr.stopMove();
            this.Position = t.nextPos;
            t = null;
        }
    }

    public void applyTarget(boolean value)//value = false
    {
        TargetUnit i = this.wantTarget;
        if (null != i) {
            this.curTarget = i;
            this.wantTarget = null;
        }
        TargetUnit e = this.curTarget;
        if (null != e) {
            if (null != e.getUnit() && (e.getUnit().dead || e.getUnit().isDestroy())) {
                this.curTarget = null;
                return;
            }
            if (value && e.getType() == TargetUnit.T_POSITION && e.getPriority() == TargetUnit.TP_AUTO_FIND) {
                this.curTarget = null;
            }
        }
    }

    public Skill checkTarget() {
        if (null == this.curTarget)
            return null;
        if (null != this.curSkill) {
            if (0 != this.curSkill.getSkill().state)
                return null;
            return this.curSkill.getSkill();
        }
        if (null == this.data.attack)
            return null;
        TargetUnit t = this.curTarget;
        if (null != t && t.getType() == TargetUnit.T_UNIT) {
            if (t.getUnit().dead) {
                this.curTarget = null;
                return null;
            }
            if (0 != this.data.attack.state)
                return null;
            float i = MathUtil.DistanceX(this.Position, t.getUnit().Position);
            i = i - (this.radiusSize + t.getUnit().radiusSize);
            if (i <= this.attackDistance() && 0 == this.statectr.banAct)
                return this.data.attack;
        }
        return null;
    }

    public void onThink(float t) {
        if (this.dead)
            return;

        this.curSecond = /*Mathf.Round*/(this.curSecond + t);
        this.applyPostion();
        this.hatredCtr.onUpdate(t);
        this.aiTime = /*Mathf.Round*/(this.aiTime + t);
        if (this.canThink()) this.think(t);

        if (0 == this.statectr.beControlled && 0 == this.statectr.dizz && this.unlimitSkill.size() > 0) {
            for (Skill r : this.unlimitSkill) {
                SkillRunner h = new SkillRunner();
                h.init(this, r, true);
                h.setLockTarget(null == this.curTarget ? null : this.curTarget.getUnit());
                h.start();
                this.skillctr.addRunner(h);
            }
            this.unlimitSkill.clear();
        }

        if (null != this.skillToSkill) {
            SkillRunner skillRunner = new SkillRunner();
            skillRunner.init(this, this.skillToSkill, true);
            skillRunner.setLockTarget(null == this.curTarget ? null : this.curTarget.getUnit());
            skillRunner.start();
            this.skillctr.addRunner(skillRunner);
            this.skillToSkill = null;
        }
    }

    public boolean canThink() {
        return null != this.aiRoot && this.stopAI <= 0 && this.statectr.beControlled <= 0
                && (this.statectr.currentStateType() == StateType.Idle || this.statectr.currentStateType() == StateType.Move);
    }

    public void think(float t) {
        this.aiRoot.handleAction();
    }

    public void addUnlimitSkill(Skill skill) {
        if (this.skillctr.isLoadPassive()) {
            if (!this.unlimitSkill.contains(skill)){
//                Log.battle.info("addUnlimitSkill {} {}",this.unitId, skill.config.sn);
                this.unlimitSkill.add(skill);
            }
        }
    }

    public List<HpAction> getHealthActions(){
        return healthActions;
    }
}
