package org.gof.demo.battlesrv.battle;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.google.protobuf.GeneratedMessageV3;
import com.sun.jna.Library;
import com.sun.jna.Memory;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.PointerByReference;
import org.gof.core.support.Sys;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.msg.*;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.StringZipUtils;

import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

public class BattleLib {


    // 定义接口映射动态库
    public interface BattleWrapper extends Library {
        // 动态设置文件扩展名
        String libExtension = Sys.isWin() ? ".dll" : Sys.isMac() ? ".dylib" : ".so";
        // 构建统一路径
        String libPath = Paths.get(System.getProperty("user.dir"), "libs", "BattleWrapper" + libExtension).toString();
        // 加载动态库
        BattleWrapper INSTANCE = Native.load(libPath, BattleWrapper.class);

        int HandleMsg(Pointer requestData, PointerByReference responseData);
    }

    /**
     * 通过proto3把函数参数序列化成二进制，调用c#的动态库函数
     * @param req
     * @return
     */
    private static GeneratedMessageV3 handleMsg(GeneratedMessageV3 req) {
        Pointer respPtr = null;
        try {
            byte[] requestBytes = BattleMsg.wrapWithHeader(req);
            Pointer requestData = new Memory(requestBytes.length);
            requestData.write(0, requestBytes, 0, requestBytes.length);

            // 调用 C# 方法（由 C# 分配内存）
            PointerByReference responseData = new PointerByReference();
            int ret = BattleWrapper.INSTANCE.HandleMsg(requestData, responseData);
            if (ret != 0) {
                Log.battle.error("DoBattle failed, ret={}", ret);
                return null;
            }
            // 从协议头解析长度（大端序）
            respPtr = responseData.getValue();
            return BattleMsg.parseMsg(respPtr);
        } catch (Exception e) {
            Log.battle.error(e.getMessage());
            return null;
        } finally {
            // 确保释放内存
            if (respPtr != null) {
                Native.free(Pointer.nativeValue(respPtr));
            }
        }
    }

    public static void mockKungFuRaceBattle() {
        String json = "UEsDBBQACAgIAAGYeFoAAAAAAAAAAAAAAAABAAAAMK2Y227jOBKG3yW3GcByItvKAHNBUpRNHSzR\n" +
                "h3bkxWLhKAktKbbi+CDZg333raIkJ9Pd2O0BNoDR1SyyeBD5fyz+ebN/eXm++b076N1ZXcM0jN9u\n" +
                "Vof8X8+rw+rm93/csPC4Oe8+7PPt9INREcWL7mUwOD+bppeaZqjg395QBODZ+15RMKYSQSZszaii\n" +
                "2l6zLdi2titWgO0wVQhKWQI2F2MSoY9O2Kr2KUHX9oBW6ZFMhMNCEnHCp1RmU7silKu56BJL1j7V\n" +
                "+g6tr6SJpOgLksfG5zkVYeg7Vo3PN8E3R1/pNu0GU/CtwSfyLScJ+NJJ7YtFLy5pHSvtjZr6xcSS\n" +
                "C6zvqVfuBOizWt9p0ozPN1+bMVT+uvZZqVn7ouDY+IzaF4vTgww5kVOqMnYgXFIqhsybdsmEcO1/\n" +
                "kjAOeaZltnLWxB4SDnb6zCdkNCQC7YNDiRiSAOssoQ6U6/op2MGQzPuMpITB2ucH2g1hPEoqubQL\n" +
                "waC/7cHr35eiT1XKoM6UXQqXESHzYnE+dQIawhjgG5ZbAnVk6jBKKMZYKyXJrBgykQgekWMI3zMl\n" +
                "SvZ8MdY29JGOlcD2KdR9IAH24TNqV9TGdobgwVxh3Qz8qldQbZdKWqe9jvEGMXZx4mKMN6hTTCFG\n" +
                "mT5CjPkH4WCrmLvnqTGaY/mST5h41+Xpik9smaGt0meov+SzwmGuKThNcB4VmxAd9zAh8s6Osd4d\n" +
                "p0y4Fxibn0gu1BbHcKRE7oJAj+0IdQu3BFuBncj96aUYZ7COfvHIYcbbyxzjDJyKmc4F+gtgjnYJ\n" +
                "ZVkIa6tCXPMxeeRR+vJ2UfBdsoldsfh0IhLjG8qShWfo+EZpQb+bEsY9YmMZcXe1hvWG9vOIh5m1\n" +
                "prgW2QLalxs9z+zRrvuF+nBGoplLddsi4oG3IBnMC89VVO6wL1NachPuPW1Dv6ePZITrYUK/Vaow\n" +
                "9gbiLSqyx9hbu7INhmuD5y8gt6HKRV8m1/3DVLajFZuun7yOVerx7K7jCfcj7rOkHkM54kGwwn4H\n" +
                "0P7kl5G2VSLX4ZzhGAYwnjdiYIwT9OtT3c4acc/Yod8iiTynuGezCvrc67iRAH+p21tlIj/0fs0M\n" +
                "WtEjlj1AX9koxzYmtJno7xzl0KZw0H8L/asJ7oMMdMguHB0TxhpJG/0daH9fr8Ut+B847uEIxiT2\n" +
                "0DDxqFSUj4vF1XaNydX2jLlUVlMnX3A9B1jzKRynEvYJnENzvWS2toUspEI9KGD9hGsI0Mp5o6mg\n" +
                "DSF8T189NvEY95TAeH2IV9q9a4zbMvlZjKSOAd8Q9vc0Ix5RrWbbvfykHnW9yu5xArpOJPod0Kz4\n" +
                "ttyrnCQFmeH5l1CfRHJYyd0yezmpZUoJzI04OxiT4ELSZCj/uPntho13PcP7RAmggzU/D34z0xzO\n" +
                "h2JOQSd87523KHn5gpIl2H6DEsTFUKODsrxGh8ZIUZcDYvQUlSNgiVB6DdliQ7bY+NZio1e2vrL1\n" +
                "EcCGg74Kjjf5K1KE00r9ukFKFGxbFLlNuxSRUiEGktqn0m2qAowFy74IQNa17PdjlHWFCLn8iBDS\n" +
                "ICQdjCo60T7a+DwO6Pl1vByeES8BjiM7SC5JBePwbw0Yhx3A2t1PYknWiqJ9BzabKKbLKZRXSte5\n" +
                "W+s6HO3thEtcXzz+CuT+zdzcv0sOUgafX5zKk9jr46JArrNJ0Gd6i25c5W76+piSQJ6jfY0QqREC\n" +
                "+I0Fr6V6BFJd+hlIhsBt9DFk2s4FH2+6+kiKAreXlqoUUFGuLWw3BnklDsqdAIl3d29E2xbUtSuO\n" +
                "EuTOBQ+9CcFj70J/obHUWIEYvZGB4weUvLiH/lxL/Qbm2cr+Zs0b2Y/FFuZcqrzBCuMwpgYrWvrh\n" +
                "CmRLG6Xf5y4cISzrcmrHYxy7HzdYge9SEZmyBFFlAnaKstTY6oGttgTXM+05lf2cvxSWxg3suTfr\n" +
                "YxlqPO0f+bj0cQ0vsIYRSlEAY/CIqdEB16HN8iOeIZrGHCTffn1fC5R0uE6xh4P+XtkMbLV/LqY4\n" +
                "R7xeva32Gj1dODNFmMaOjfiII+7ZqUb9OInqfmFPQP313uDaBrk2TkpLrwnlVY2mNcSfdqRGSar7\n" +
                "kibKZwaSPvQwdoixsztbyzvGnn8LGyk7AqaZzN5Bqn3LP/vw3TyQvtGXvVRjJcgGemyIFRfb11jp\n" +
                "iGCsUYJzITGO4QiyHVcKryHZEcbwSDXOrGZOUBewUZ41dkqoa/oYN+KfWFENVlR2abADZ06O4J6G\n" +
                "ZV1oM491GwXYiOcaKzCWguIezfrgn3Hth3F7KtBYAZRtS93+AfyJRmxkQnsx1H4Y0x5uXbaWY9QT\n" +
                "jXkQtkbSJ3birEX9bSLy7RMzhsZMFzTOot+uWHgnV7SYzicWiisWQDenuWywYCAWOp9/Vl1WfV8m\n" +
                "BCV4toFy/YAjLvpkrzLABb3U10WIs6Lcfqdd9pRH4S3dCI7YIxscVyz78rlckj/+uPnnbzfPL6+f\n" +
                "qUhgmzvX6xp9D/nBnxZvl2eaOmRqD9h5FxIF+wDiADJjuJyNcI4bpWKqywMJ5zWmulzKjkpiVqI9\n" +
                "l4Dzxo4l6FBs07+fdhjr7xmBV7+aA1dGBFbLj/+VdrDv0o7qa9qxpG3aQdB31/p2VcsF62+lHbfP\n" +
                "wY9cCIaaC+gvB8gn7e9gWkIuAq+SCyiLyQzsUIEdxyRDe7/gtIh1Hbj2wJxiaoMN1x5O8hiuoS4T\n" +
                "BHR5s1ONjmIKAtpVc8NPXx+WBW+5YZRFzQ3/VdztVp2WG3sXz+EvcMNLFbcbbribA2u50VzlkBtd\n" +
                "OW+4Ya+1FiE3vJW8csO/s7X+ITf8aUi1Df15s6Ou40La4z8V+kpOldysdmRbpx6YhrR6DmlIrefQ\n" +
                "F9xL4NqI5x/uY9ybrzRPYL650ulXNqqYHNn/N3Y8bD7GccuO7q19nNgNO+CM/zd2bPevz9NZww4/\n" +
                "m5jruGWHND/ZUVxe83nLDuNDXNnR9++kkzXscL27Kzt0vzU7Lu+maNlxu0t4y44OK1p2rAal2bIj\n" +
                "7KqkYQcbi6xlh2fYl5YdfNayA9emYYe57SxW8O1qdnzup+9Skn0zNmAA6HEnxfXMDtCe51SnBAdM\n" +
                "f4hOZUCb/cudTmFDEzTXsGt2wBpMNFtOyBn9zSICTBInzQ55Zcf5M2UJGrbMxQP0KzQb5pBSVD3N\n" +
                "DijbK9ynWQ/abKj2wzxckmh/iWzRMS1aj69mh7cft+zoXGij/VzCPQ61P8bvCJ20/ChqfoC+Qdwr\n" +
                "P/BMQwz8Vjl03saA+99PYrCDQ3+IAfc+fk1NUvZ8ZdD65+nNX1KTa4zIGP0sBmj5lxjvmkGg/2Pt\n" +
                "+/JXlwXfl+nUhmJqI7yMUkKH0d4XTH5Nb1aY3qTLS/qZ3riY3ky4yK/pjegnx94VT1NIaY6mGb7B\n" +
                "b/fVHo5KZan9rke4R0vMCPHFhntMwRTdEl9vlEEs9NkgDfh/tDl3C7AF2g73YrAl2kPuBVhHY6XF\n" +
                "EWbxWu7HQYuqx9Z336ZBQdDiiLfpzGeqE7e+kdO8gp1e9i2OXEDUUCPnxzTliqp3wNEUUeXPW1+/\n" +
                "9VnTFkeixdGlRdUDM+sUydvO7AZR7x9z7Btfsl6cNWH1y5d+BWP1y1f2CjZtXr6S2tYvX0+1fX35\n" +
                "sjem8dS+fM1m9ZVhEPqXqX6JEiDlsZYvvKZskkXqzGrk3PbiX0OOC1I/a5ATposvyKEtctaqRQ77\n" +
                "4NoPshtU0/bVq/Rzt331ehMJbV+9Ll39OoWvXsUc+6P4WnYcD3Vak094OD3VSComgNI5ub507f/2\n" +
                "S1ed5vz40vUWZRG3m5cutz94U0b70rUczpqXLqZ+fOkSKzUAiWpeuqxTVOrUDV+67t9kgxI774Bs\n" +
                "YblGSabrQ5puB+vOXr8gdqF+7sTNK5e+JtevXOPVN5q1r1z9x1raAVvi/G5ruRYg7bMp03I9v6Yv\n" +
                "PYiXfQS6T3wG6EtMAREZ9t48jYV+3YLr/t371inW8x9fs6Lj4vqa5Vn3bdrxcMol7hdMO5Z2g4sw\n" +
                "e6vrAi78YNjiok+SBhcsd1tc+PLpV3HBLw0uXBq2uNimpMVFdMXF2NRtEBdLu3nVcolOeUBGYRz6\n" +
                "ta4DbUaMw7WtxsKLk1/t1Rf79YudfLGfnLw4NTKsr+HF00fua7kOZL+0MKWALRILCjuhUBLOe3Zm\n" +
                "ZxA3wkvYsyDJrrwf2+9/eU2CdIWPxtfUoEy3s5fVRjzf/N5/eDAeDP3XHXTNnmH9+z9QSwcI0mBr\n" +
                "NFIMAADYGAAA\n";
        JSONObject jo = Utils.toJSONObject(StringZipUtils.unzip(json));
        if(jo == null){
            return;
        }
        long seed = jo.getLong("seed");
        List<Define.p_battle_role> atkBattleRoles = new ArrayList<>();
        List<Define.p_battle_role> defBattleRoles = new ArrayList<>();
        List<byte[]> joAtkArray = jo.getObject("atk_data", new TypeReference<List<byte[]>>() {});
        try{
            for(byte[] atkData : joAtkArray){
                Define.p_battle_role atkBattleRole = Define.p_battle_role.parseFrom(atkData);
                atkBattleRoles.add(atkBattleRole);
            }
        } catch (Exception e) {
            Log.battle.error("toVideoMsg 解析joAtkArray错误 joAtkArray={}", joAtkArray);
        }
        List<byte[]> joDefArray = jo.getObject("def_data", new TypeReference<List<byte[]>>() {});
        try{
            for(byte[] defData : joDefArray){
                Define.p_battle_role defBattleRole = Define.p_battle_role.parseFrom(defData);
                defBattleRoles.add(defBattleRole);
            }
        } catch (Exception e) {
            Log.battle.error("toVideoMsg 解析joDefArray错误 joDefArray={}", joDefArray);
        }
//        MsgBattle.battle_result_c2s resp = doKungFuRaceBattle(seed, atkBattleRoles, defBattleRoles);
//        if(resp == null){
//            return;
//        }
//        Log.battle.info("===doKungFuRaceBattle seed={} resp={}", seed, resp.getResult());
    }

    public static void mockArenaBattle() {
        String hexStr = "10, 91, CE, FD, B2, CD, 82, DD, D9, 09, 18, B6, 94, 80, C2, 06, 22, D4, 0C, 08, 88, C3, F5, AA, CA, B2, FA, A9, 08, 10, 88, 01, 18, 02, 20, F7, 0E, 28, 80, FF, E8, 27, 32, 05, 43, 65, 6C, 69, 61, 3A, 05, 08, 0B, 10, F1, 07, 4A, 3C, 0A, 02, 08, 01, 0A, 05, 08, 02, 10, F4, 10, 0A, 0D, 08, 03, 10, FF, FF, FF, FF, FF, FF, FF, FF, FF, 01, 0A, 02, 08, 04, 0A, 06, 08, 05, 10, 86, A3, 04, 10, 01, 18, 86, 0C, 20, A2, 06, 28, BE, 05, 30, 01, 3A, 04, 08, 02, 10, 1E, 40, F2, 07, 5A, 9E, 03, 0A, 07, 08, 01, 10, AB, 08, 18, 01, 0A, 07, 08, 02, 10, 9A, 08, 18, 06, 0A, 07, 08, 03, 10, A6, 08, 18, 01, 0A, 07, 08, 04, 10, 85, 08, 18, 16, 0A, 07, 08, 05, 10, FD, 07, 18, 0C, 0A, 0A, 08, 06, 10, 9C, 08, 18, 05, 20, AC, 1B, 12, 05, 08, B5, 10, 10, 01, 12, 05, 08, C5, 10, 10, 01, 12, 05, 08, 9A, 11, 10, 01, 12, 05, 08, B6, 10, 10, 01, 12, 05, 08, BA, 10, 10, 01, 12, 05, 08, BC, 10, 10, 01, 12, 05, 08, 84, 12, 10, 01, 12, 06, 08, EE, B3, 01, 10, 01, 12, 05, 08, C0, 17, 10, 0C, 12, 05, 08, D1, 17, 10, 03, 12, 05, 08, D4, 17, 10, 02, 12, 05, 08, C6, 17, 10, 02, 12, 06, 08, C9, EC, 01, 10, 04, 12, 06, 08, BD, EB, 01, 10, 02, 12, 05, 08, BE, 17, 10, 0A, 12, 05, 08, EE, 1E, 10, 02, 12, 06, 08, F9, 8D, 18, 10, 02, 12, 05, 08, 89, 28, 10, 01, 12, 06, 08, DB, 90, 03, 10, 01, 12, 06, 08, DC, 90, 03, 10, 01, 12, 06, 08, DD, 90, 03, 10, 01, 12, 05, 08, ED, 27, 10, 07, 12, 05, 08, 89, 27, 10, 07, 12, 05, 08, AE, 27, 10, 01, 12, 06, 08, EA, 8C, 01, 10, 01, 12, 06, 08, 91, 85, 01, 10, 14, 12, 06, 08, 92, 85, 01, 10, 14, 12, 06, 08, 93, 85, 01, 10, 05, 12, 06, 08, 94, 85, 01, 10, 01, 12, 06, 08, 95, 85, 01, 10, 0A, 12, 06, 08, 96, 85, 01, 10, 05, 12, 06, 08, 97, 85, 01, 10, 01, 12, 06, 08, 9B, 85, 01, 10, 0A, 12, 05, 08, B4, 1F, 10, 01, 12, 05, 08, AC, 1F, 10, 01, 12, 05, 08, A8, 1F, 10, 5E, 12, 05, 08, A4, 1F, 10, 5E, 12, 05, 08, B0, 1F, 10, 01, 12, 05, 08, BC, 1F, 10, 01, 12, 05, 08, F1, 2E, 10, 01, 12, 05, 08, F2, 2E, 10, 01, 12, 05, 08, F3, 2E, 10, 01, 12, 05, 08, F4, 2E, 10, 01, 12, 06, 08, B5, D4, 0E, 10, 06, 12, 06, 08, A5, B5, 01, 10, 06, 12, 06, 08, 8C, B6, 01, 10, 06, 12, 06, 08, C2, B4, 01, 10, 06, 12, 06, 08, 90, D3, 01, 10, 02, 62, 07, 08, 9D, 11, 10, 09, 18, 01, 62, 07, 08, B1, 17, 10, 01, 18, 02, 62, 0C, 08, EA, 16, 10, 2E, 18, 03, 20, 99, 75, 28, 05, 62, 07, 08, F5, 16, 10, 16, 18, 04, 62, 0C, 08, D9, 16, 10, 0E, 18, 05, 20, D9, 4F, 28, 01, 62, 07, 08, DF, 16, 10, 04, 18, 06, 72, 09, 08, 80, 08, 10, FE, CD, CC, 8E, 02, 72, 08, 08, 01, 10, C3, D8, BD, A7, 08, 72, 06, 08, 82, 08, 10, BB, 1A, 72, 09, 08, 02, 10, AA, E6, 89, 93, CA, 04, 72, 06, 08, 03, 10, C8, FB, 01, 72, 05, 08, 85, 08, 10, 01, 72, 05, 08, 86, 08, 10, 01, 72, 06, 08, 87, 08, 10, F0, 2E, 72, 07, 08, 88, 08, 10, 8E, A2, 14, 72, 07, 08, 89, 08, 10, F6, D8, 15, 72, 06, 08, 8A, 08, 10, F4, 03, 72, 06, 08, 8B, 08, 10, EE, 11, 72, 06, 08, 8D, 08, 10, 82, 0A, 72, 07, 08, 8E, 08, 10, 89, 89, 01, 72, 07, 08, 8F, 08, 10, EE, A5, 11, 72, 07, 08, 95, 08, 10, F7, FD, 04, 72, 06, 08, 96, 08, 10, D7, 76, 72, 07, 08, 97, 08, 10, A2, FF, 11, 72, 08, 08, 18, 10, FE, CD, CC, 8E, 02, 72, 06, 08, 99, 08, 10, D8, 04, 72, 07, 08, 99, 11, 10, A8, C3, 01, 72, 07, 08, 9A, 11, 10, A0, 9C, 01, 72, 06, 08, 9C, 08, 10, A0, 06, 72, 06, 08, 9D, 08, 10, D9, 11, 72, 05, 08, 9E, 08, 10, 15, 72, 06, 08, A1, 1F, 10, 8E, 10, 72, 06, 08, A3, 1F, 10, B8, 17, 72, 06, 08, A5, 1F, 10, DC, 0E, 72, 05, 08, B1, 09, 10, 0A, 72, 06, 08, B5, 10, 10, D8, 36, 72, 06, 08, B6, 10, 10, 88, 27, 72, 06, 08, B7, 10, 10, 88, 27, 72, 07, 08, B8, 10, 10, A8, C3, 01, 72, 07, 08, B9, 10, 10, A0, 9C, 01, 72, 09, 08, B9, 17, 10, B9, C8, 82, 9D, 01, 72, 08, 08, BA, 17, 10, 89, F3, B3, 6B, 72, 06, 08, BB, 17, 10, DC, 0B, 72, 05, 08, CD, 08, 10, 3C, 72, 06, 08, CE, 08, 10, A0, 38, 72, 08, 08, D0, 17, 10, B8, 82, D6, 7C, 72, 08, 08, D1, 0F, 10, 94, 87, C6, 1B, 72, 07, 08, D2, 0F, 10, 90, EF, 02, 72, 08, 08, D3, 0F, 10, C4, 96, A1, 16, 72, 07, 08, D4, 0F, 10, E4, AA, 02, 72, 08, 08, D5, 0F, 10, F4, E4, D2, 17, 72, 07, 08, D6, 0F, 10, DA, CD, 02, 72, 06, 08, D7, 0F, 10, DC, 0B, 72, 07, 08, D8, 0F, 10, D5, DA, 05, 72, 07, 08, DA, 0F, 10, F1, BD, 06, 72, 06, 08, E0, 0F, 10, A0, 1F, 72, 07, 08, E1, 0F, 10, 92, DB, 10, 72, 07, 08, E2, 0F, 10, A8, F7, 11, 72, 06, 08, E3, 0F, 10, E7, 1D, 72, 06, 08, E4, 0F, 10, D0, 0F, 72, 07, 08, E6, 0F, 10, F4, 8E, 0D, 72, 06, 08, E7, 0F, 10, C1, 14, 72, 06, 08, E8, 0F, 10, 88, 27, 72, 09, 08, E9, 07, 10, C3, D8, BD, A7, 08, 72, 0A, 08, EA, 07, 10, AA, E6, 89, 93, CA, 04, 72, 07, 08, EB, 07, 10, C8, FB, 01, 72, 06, 08, EC, 07, 10, 9E, 05, 72, 07, 08, ED, 07, 10, 98, D1, 0C, 72, 06, 08, ED, 0F, 10, E8, 07, 72, 07, 08, EE, 07, 10, E8, 8B, 07, 72, 06, 08, EE, 0F, 10, A4, 0D, 72, 06, 08, EF, 0F, 10, A1, 0D, 72, 06, 08, EF, 07, 10, 90, 4E, 72, 06, 08, F0, 07, 10, B4, 2A, 72, 06, 08, F1, 07, 10, AC, 02, 72, 06, 08, F2, 07, 10, AC, 02, 72, 06, 08, F3, 07, 10, AC, 02, 72, 05, 08, F4, 07, 10, 0D, 72, 06, 08, F5, 07, 10, B0, 6D, 72, 06, 08, F9, 07, 10, 9A, 05, 72, 06, 08, FA, 07, 10, D8, 1D, 72, 06, 08, FB, 07, 10, F4, 03, 72, 06, 08, FC, 07, 10, B3, 0C, 72, 06, 08, FD, 07, 10, EC, 1D, 72, 06, 08, FE, 07, 10, 96, 05, 72, 06, 08, FF, 07, 10, F8, 0E, 7A, 54, 0A, 02, 08, 01, 12, 06, 08, EC, 07, 10, 8E, 10, 12, 06, 08, D8, 0F, 10, B8, 17, 12, 06, 08, F8, 07, 10, DC, 0E, 12, 06, 08, F0, 0F, 10, F0, 10, 12, 06, 08, D7, 0F, 10, A4, 3A, 12, 06, 08, D8, 0F, 10, C8, 65, 12, 06, 08, F8, 07, 10, B8, 17, 12, 06, 08, EB, 07, 10, E8, 07, 12, 06, 08, EC, 07, 10, F4, 03, 12, 06, 08, EF, 07, 10, E8, 20, 7A, 27, 0A, 05, 08, 01, 10, F5, 16, 12, 06, 08, EC, 07, 10, 8E, 10, 12, 06, 08, D8, 0F, 10, B8, 17, 12, 06, 08, F8, 07, 10, DC, 0E, 12, 06, 08, FE, 07, 10, 90, 1C, 7A, 27, 0A, 05, 08, 01, 10, D9, 16, 12, 06, 08, EC, 07, 10, 8E, 10, 12, 06, 08, D8, 0F, 10, B8, 17, 12, 06, 08, F8, 07, 10, DC, 0E, 12, 06, 08, F0, 0F, 10, 8B, 25, 7A, 27, 0A, 05, 08, 01, 10, 9D, 11, 12, 06, 08, EC, 07, 10, 8E, 10, 12, 06, 08, D8, 0F, 10, B8, 17, 12, 06, 08, F8, 07, 10, DC, 0E, 12, 06, 08, F0, 0F, 10, 8B, 25, 7A, 0F, 0A, 05, 08, 02, 10, 85, 08, 12, 06, 08, E8, 0F, 10, 88, 27, 7A, 0F, 0A, 05, 08, 02, 10, FD, 07, 12, 06, 08, E8, 0F, 10, 88, 27, 80, 01, 86, 0C, 88, 01, 01, 92, 01, 05, 08, 06, 10, BA, 17, 92, 01, 04, 08, 06, 10, 08, 92, 01, 04, 08, 07, 10, 01, B2, 01, 50, 0A, 06, 08, E9, 07, 10, 88, 01, 0A, 05, 08, EE, 07, 10, 08, 0A, 05, 08, 81, 08, 10, 01, 0A, 03, 08, 82, 08, 0A, 05, 08, F6, 07, 10, 0B, 0A, 06, 08, F7, 07, 10, F1, 07, 0A, 08, 08, FC, 07, 10, 80, FF, E8, 27, 12, 0A, 08, EC, 07, 12, 05, 43, 65, 6C, 69, 61, 12, 09, 08, FA, 07, 12, 04, 4E, 4F, 2E, 31, 12, 03, 08, F8, 07, 2A, D4, 0A, 08, BB, DF, 95, B5, C5, 8F, F8, A9, 08, 10, 8B, 01, 18, 01, 20, B1, 10, 28, 8B, C6, CE, 26, 32, 06, E3, 82, 88, E3, 82, 88, 3A, 02, 08, 12, 4A, 2F, 0A, 02, 08, 01, 0A, 05, 08, 02, 10, B9, 10, 0A, 0D, 08, 03, 10, FF, FF, FF, FF, FF, FF, FF, FF, FF, 01, 0A, 02, 08, 04, 0A, 02, 08, 05, 10, 01, 18, FB, 0B, 30, 01, 3A, 04, 08, 02, 10, 1D, 5A, C0, 02, 0A, 0A, 08, 01, 10, A1, 08, 18, 01, 20, 88, 27, 0A, 07, 08, 02, 10, 9A, 08, 18, 06, 0A, 07, 08, 03, 10, A8, 08, 18, 07, 0A, 08, 08, 04, 10, F4, 07, 18, C8, 01, 0A, 0A, 08, 05, 10, FD, 07, 18, 0E, 20, 94, 23, 0A, 0A, 08, 06, 10, 9C, 08, 18, 05, 20, 88, 27, 12, 05, 08, E0, 0F, 10, 01, 12, 05, 08, D2, 0F, 10, 01, 12, 05, 08, D4, 0F, 10, 01, 12, 05, 08, E8, 0F, 10, 01, 12, 05, 08, ED, 0F, 10, 01, 12, 05, 08, C0, 17, 10, 0B, 12, 05, 08, C5, 17, 10, 01, 12, 05, 08, C7, 17, 10, 01, 12, 05, 08, BD, 17, 10, 05, 12, 05, 08, BE, 17, 10, 05, 12, 05, 08, FF, 27, 10, 01, 12, 05, 08, ED, 27, 10, 08, 12, 05, 08, 89, 27, 10, 08, 12, 05, 08, 91, 27, 10, 02, 12, 06, 08, E5, 8C, 01, 10, 01, 12, 06, 08, 81, 85, 01, 10, 01, 12, 06, 08, E9, 84, 01, 10, 0A, 12, 06, 08, FD, 84, 01, 10, 01, 12, 06, 08, FE, 84, 01, 10, 01, 12, 05, 08, B9, 1F, 10, 01, 12, 05, 08, A1, 1F, 10, 64, 12, 05, 08, BC, 1F, 10, 01, 12, 05, 08, AC, 1F, 10, 01, 12, 05, 08, A6, 1F, 10, 52, 12, 05, 08, AF, 1F, 10, 01, 12, 05, 08, F1, 2E, 10, 01, 12, 05, 08, F2, 2E, 10, 01, 12, 05, 08, F3, 2E, 10, 01, 12, 05, 08, F4, 2E, 10, 01, 12, 06, 08, EF, D3, 0E, 10, 08, 12, 06, 08, C3, B4, 01, 10, 08, 12, 06, 08, CF, B7, 01, 10, 08, 12, 06, 08, AC, B5, 01, 10, 08, 12, 06, 08, D9, B7, 01, 10, 08, 12, 06, 08, AA, D3, 01, 10, 03, 62, 0C, 08, D9, 16, 10, 0D, 18, 01, 20, D9, 4F, 28, 01, 62, 07, 08, DC, 16, 10, 05, 18, 02, 62, 07, 08, 81, 12, 10, 09, 18, 03, 62, 07, 08, DA, 16, 10, 03, 18, 04, 62, 07, 08, 9D, 11, 10, 08, 18, 05, 72, 09, 08, 80, 08, 10, CE, B2, BB, A7, 02, 72, 06, 08, 81, 08, 10, F3, 02, 72, 08, 08, 01, 10, FD, 88, C3, AD, 09, 72, 06, 08, 82, 08, 10, AF, 19, 72, 09, 08, 02, 10, 9A, D9, E5, A4, E7, 04, 72, 06, 08, 03, 10, C8, FB, 01, 72, 05, 08, 85, 08, 10, 01, 72, 05, 08, 86, 08, 10, 01, 72, 06, 08, 87, 08, 10, F0, 2E, 72, 07, 08, 88, 08, 10, A0, BD, 10, 72, 07, 08, 89, 08, 10, E0, DF, 17, 72, 06, 08, 8A, 08, 10, E7, 24, 72, 06, 08, 8B, 08, 10, 9E, 0F, 72, 06, 08, 8D, 08, 10, EE, 34, 72, 07, 08, 8E, 08, 10, EB, F0, 01, 72, 07, 08, 8F, 08, 10, F8, 9A, 14, 72, 07, 08, 95, 08, 10, BD, E1, 07, 72, 06, 08, 96, 08, 10, 9B, 68, 72, 07, 08, 97, 08, 10, D4, A7, 05, 72, 06, 08, 98, 08, 10, B8, 17, 72, 08, 08, 18, 10, CE, B2, BB, A7, 02, 72, 06, 08, 99, 08, 10, DC, 0B, 72, 07, 08, 99, 11, 10, A8, C3, 01, 72, 07, 08, 9A, 11, 10, A0, 9C, 01, 72, 06, 08, 9C, 08, 10, CC, 0A, 72, 06, 08, 9D, 08, 10, 82, 1F, 72, 05, 08, 9E, 08, 10, 14, 72, 03, 08, A3, 1F, 72, 05, 08, B1, 09, 10, 0A, 72, 06, 08, B5, 10, 10, D8, 36, 72, 06, 08, B6, 10, 10, 88, 27, 72, 06, 08, B7, 10, 10, 88, 27, 72, 07, 08, B8, 10, 10, A8, C3, 01, 72, 07, 08, B9, 10, 10, A0, 9C, 01, 72, 09, 08, B9, 17, 10, 8B, F8, B7, AA, 01, 72, 08, 08, BA, 17, 10, F1, AE, B6, 74, 72, 06, 08, BB, 17, 10, DC, 0B, 72, 05, 08, CD, 08, 10, 3C, 72, 06, 08, CE, 08, 10, A0, 38, 72, 08, 08, D0, 17, 10, F6, B0, B9, 7C, 72, 08, 08, D1, 0F, 10, FC, EE, 91, 22, 72, 07, 08, D2, 0F, 10, EE, B7, 02, 72, 08, 08, D3, 0F, 10, 84, A0, EB, 1F, 72, 07, 08, D4, 0F, 10, C4, CF, 01, 72, 08, 08, D5, 0F, 10, 9C, AD, E4, 1F, 72, 07, 08, D6, 0F, 10, DE, E3, 01, 72, 06, 08, D7, 0F, 10, DC, 0B, 72, 07, 08, D8, 0F, 10, B5, A4, 06, 72, 07, 08, DA, 0F, 10, BF, DC, 09, 72, 06, 08, E0, 0F, 10, FC, 2A, 72, 07, 08, E1, 0F, 10, 8A, 9E, 0F, 72, 07, 08, E2, 0F, 10, C4, 9E, 12, 72, 06, 08, E3, 0F, 10, CF, 2A, 72, 06, 08, E4, 0F, 10, E8, 07, 72, 07, 08, E6, 0F, 10, A8, B7, 0F, 72, 06, 08, E7, 0F, 10, A8, 14, 72, 06, 08, E8, 0F, 10, 90, 4E, 72, 09, 08, E9, 07, 10, FD, 88, C3, AD, 09, 72, 0A, 08, EA, 07, 10, 9A, D9, E5, A4, E7, 04, 72, 07, 08, EB, 07, 10, C8, FB, 01, 72, 06, 08, EC, 07, 10, 86, 1A, 72, 07, 08, ED, 07, 10, 84, 9F, 11, 72, 06, 08, ED, 0F, 10, E8, 07, 72, 07, 08, EE, 07, 10, D4, AA, 0A, 72, 06, 08, EE, 0F, 10, 87, 03, 72, 06, 08, EF, 0F, 10, A8, 14, 72, 06, 08, EF, 07, 10, D0, 5A, 72, 06, 08, F0, 07, 10, FB, 34, 72, 06, 08, F1, 07, 10, AC, 02, 72, 06, 08, F2, 07, 10, AC, 02, 72, 06, 08, F3, 07, 10, AC, 02, 72, 05, 08, F4, 07, 10, 0F, 72, 06, 08, F5, 07, 10, 8C, 79, 72, 06, 08, F9, 07, 10, ED, 24, 72, 06, 08, FA, 07, 10, F0, 15, 72, 06, 08, FB, 07, 10, 9C, 1D, 72, 06, 08, FC, 07, 10, A3, 17, 72, 06, 08, FD, 07, 10, 96, 1A, 72, 06, 08, FE, 07, 10, A8, 07, 72, 06, 08, FF, 07, 10, A1, 28, 7A, 11, 0A, 02, 08, 01, 12, 03, 08, D8, 0F, 12, 06, 08, D7, 0F, 10, 94, 23, 7A, 0F, 0A, 05, 08, 02, 10, A1, 08, 12, 06, 08, E8, 0F, 10, 88, 27, 7A, 0F, 0A, 05, 08, 02, 10, F4, 07, 12, 06, 08, E8, 0F, 10, 88, 27, 7A, 0F, 0A, 05, 08, 02, 10, A8, 08, 12, 06, 08, E8, 0F, 10, 88, 27, 7A, 0F, 0A, 05, 08, 02, 10, 9C, 08, 12, 06, 08, E8, 0F, 10, 88, 27, 7A, 0F, 0A, 05, 08, 02, 10, FD, 07, 12, 06, 08, E8, 0F, 10, 90, 4E, 80, 01, FB, 0B, 88, 01, 01, 92, 01, 05, 08, 06, 10, D5, 0F, 92, 01, 04, 08, 06, 10, 07, 92, 01, 04, 08, 07, 10, 01, B2, 01, 59, 0A, 06, 08, E9, 07, 10, 8B, 01, 0A, 05, 08, EE, 07, 10, 07, 0A, 05, 08, 81, 08, 10, 01, 0A, 03, 08, 82, 08, 0A, 05, 08, F6, 07, 10, 12, 0A, 03, 08, F7, 07, 0A, 08, 08, FC, 07, 10, 8B, C6, CE, 26, 12, 0B, 08, EC, 07, 12, 06, E3, 82, 88, E3, 82, 88, 12, 14, 08, FA, 07, 12, 0F, E3, 83, A9, E3, 82, B9, E3, 81, AE, E9, 9B, 86, E3, 81, 84, 12, 03, 08, F8, 07, 30, B6, 94, 80, C2, 06, 40, BB, DF, 95, B5, C5, 8F, F8, A9, 08";
        String[] hexValues = hexStr.split(", ");
        byte[] bytes = new byte[hexValues.length];
        for (int i = 0; i < hexValues.length; i++) {
            bytes[i] = (byte) Integer.parseInt(hexValues[i], 16);
        }
        MsgBattle.battle_result_c2s resp = doArenaBattle(bytes);
        if(resp == null){
            return;
        }
        Log.battle.info("===doArenaBattle resp={}", resp);
    }

    /**
     * 进行竞技场战斗
     * @param seed
     * @param atkData
     * @param defData
     * @return
     */
    public static MsgBattle.battle_result_c2s doArenaBattle(long seed, Define.p_arena_role atkData, Define.p_arena_role defData) {
        MsgArena.arena_video_play_s2c.Builder req = MsgArena.arena_video_play_s2c.newBuilder();
        req.setSeed(seed);
        req.setAtkData(atkData);
        req.setDefData(defData);
        return (MsgBattle.battle_result_c2s) handleMsg(req.build());
    }

    /**
     * 进行竞技场战斗
     * @param msg
     * @return
     */
    public static MsgBattle.battle_result_c2s doArenaBattle(byte[] msg){
        try{
            MsgArena.arena_video_play_s2c req = MsgArena.arena_video_play_s2c.parseFrom(msg);
            return (MsgBattle.battle_result_c2s) handleMsg(req);
        } catch (Exception e) {
            Log.battle.error("解析arena_video_play_s2c错误 msg={}", msg);
            return null;
        }
    }

    /**
     * 进行武道会战斗
     * @param seed
     * @param atkDataList
     * @param defDataList
     * @return
     */
    public static MsgBattle.battle_result_c2s doKungFuRaceBattle(long seed, List<Define.p_battle_role> atkDataList, List<Define.p_battle_role> defDataList) {
        MsgKungfuRace.kungfu_race_team_combat_s2c.Builder req = MsgKungfuRace.kungfu_race_team_combat_s2c.newBuilder();
        req.setSeed(seed);
        req.addAllAtkData(atkDataList);
        req.addAllDefData(defDataList);
        return (MsgBattle.battle_result_c2s) handleMsg(req.build());
    }

    /**
     * 进行跨服战pvp战斗
     * @param seed
     * @param chapterId
     * @param atkData
     * @param defData
     * @return
     */
    public static MsgBattle.battle_result_c2s doCrossWarPvpBattle(long seed, int chapterId, Define.p_battle_role atkData, Define.p_battle_role defData) {
        MsgScene.scene_battle_s2c.Builder req = MsgScene.scene_battle_s2c.newBuilder();
        req.setRandomSeed(seed);
        req.setId(chapterId);
        req.setType(InstanceConstants.KFWARCHAPTER_17);
        req.addRoles(atkData);
        req.addRoles(defData);
        return (MsgBattle.battle_result_c2s) handleMsg(req.build());
    }

    /**
     * 进行跨服战pve战斗
     * @param seed
     * @param chapterId
     * @param atkData
     * @param defData
     * @return
     */
    public static MsgBattle.battle_result_c2s doCrossWarPveBattle(long seed, int chapterId, Define.p_battle_role atkData, Define.p_battle_monster defData) {
        MsgScene.scene_battle_s2c.Builder req = MsgScene.scene_battle_s2c.newBuilder();
        req.setRandomSeed(seed);
        req.setId(chapterId);
        req.setType(InstanceConstants.KFWARCHAPTERMONSTER_18);
        req.addRoles(atkData);
        req.addMonsters(defData);
        return (MsgBattle.battle_result_c2s) handleMsg(req.build());
    }

    /**
     * 进行乱斗战斗
     * @param seed
     * @param atkData
     * @param defData
     * @return
     */
    public static MsgBattle.battle_result_c2s doGvgBattle(long seed, Define.p_battle_role atkData, Define.p_battle_role defData) {
        MsgGvg.gvg_play_video_s2c.Builder req = MsgGvg.gvg_play_video_s2c.newBuilder();
        req.setSeed(seed);
        req.setAtkData(atkData);
        req.setDefData(defData);
        return (MsgBattle.battle_result_c2s) handleMsg(req.build());
    }

    /**
     * gve战斗开始
     * @param seed
     * @param chapterId
     * @param atkDataList
     */
    public static void gveBattleStart(long seed, int chapterId, List<Define.p_battle_role> atkDataList) {
        MsgBattle.battle_gve_start_s2c.Builder req = MsgBattle.battle_gve_start_s2c.newBuilder();
        req.setSeed(seed);
        req.setChapterId(chapterId);
        req.addAllAtkData(atkDataList);
        handleMsg(req.build());
    }

    /**
     * gve战斗更新
     * @param seed
     * @param diffTime
     * @return
     */
    public static MsgBattle.battle_gve_update_c2s gveBattleUpdate(long seed, long diffTime) {
        MsgBattle.battle_gve_update_s2c.Builder req = MsgBattle.battle_gve_update_s2c.newBuilder();
        req.setSeed(seed);
        req.setDiffTime(diffTime);
        return (MsgBattle.battle_gve_update_c2s) handleMsg(req.build());
    }

    /**
     * gve战斗结束
     * @param seed
     * @return
     */
    public static MsgBattle.battle_gve_end_c2s gveBattleEnd(long seed) {
        MsgBattle.battle_gve_end_s2c.Builder req = MsgBattle.battle_gve_end_s2c.newBuilder();
        req.setSeed(seed);
        return (MsgBattle.battle_gve_end_c2s) handleMsg(req.build());
    }

    /**
     * 获取gve战斗信息
     * @param seed
     * @return
     */
    public static MsgBattle.battle_gve_info_c2s getGveBattleInfo(long seed) {
        MsgBattle.battle_gve_info_s2c.Builder req = MsgBattle.battle_gve_info_s2c.newBuilder();
        req.setSeed(seed);
        return (MsgBattle.battle_gve_info_c2s) handleMsg(req.build());
    }

    /**
     * 添加gve战斗玩家
     * @param seed
     * @param atkData
     * @param pos
     */
    public static void gveBattleAddPlayer(long seed, Define.p_battle_role atkData, int pos) {
        MsgBattle.battle_gve_add_player_s2c.Builder req = MsgBattle.battle_gve_add_player_s2c.newBuilder();
        req.setSeed(seed);
        req.setAtkData(atkData);
        req.setPos(pos);
        handleMsg(req.build());
    }

}

