package org.gof.demo.battlesrv.battle;

import com.google.protobuf.CodedInputStream;
import com.google.protobuf.GeneratedMessageV3;
import com.sun.jna.Pointer;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.msg.MsgIds;
import org.gof.demo.worldsrv.support.Log;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Arrays;

public class BattleMsg {
    // 可配置的头部字段长度
    public static final int SIZE_BYTES = 4;  // 长度字段字节数
    public static final int TYPE_BYTES = 4;  // 消息类型字段字节数
    public static final int HEADER_SIZE = SIZE_BYTES + TYPE_BYTES;

    /**
     * 编码：添加协议头（与sendMsg方法严格对齐）
     * @param message 消息体
     * @return 带协议头的完整数据包
     */
    public static byte[] wrapWithHeader(GeneratedMessageV3 message) {
        // ================== 2. 获取消息ID ==================
        int msgId = MsgIds.getIdByClass(message.getClass());

        // ================== 3. 序列化消息体 ==================
        byte[] body = message.toByteArray();

        // ================== 4. 构造协议头 ==================
        byte[] header = new byte[HEADER_SIZE];
        int totalLen = HEADER_SIZE + body.length; // 总长度=头+体

        // 写入长度字段（大端，对应sendMsg的Utils.intToBytes）
        Utils.intToBytes(header, 0, totalLen);     // 偏移0写入总长度
        Utils.intToBytes(header, SIZE_BYTES, msgId); // 偏移4写入消息ID

        // ================== 5. 合并头与消息体 ==================
        byte[] buffResult = new byte[header.length + body.length];
        System.arraycopy(header, 0, buffResult, 0, header.length);
        System.arraycopy(body, 0, buffResult, header.length, body.length);
        return buffResult;
    }


    /**
     * 解码：通过 ByteBuffer 零拷贝解析协议数据
     * @param respPtr 指向完整协议数据的指针（含协议头）
     * @return 解析后的消息对象
     */
    public static GeneratedMessageV3 parseMsg(Pointer respPtr) throws IOException {
        // ================== 1. 基础校验 ==================
        if (respPtr == null) {
            throw new IOException("Invalid pointer: null");
        }

        // ================== 2. 将指针映射为 ByteBuffer（大端序） ==================
        ByteBuffer buffer = respPtr.getByteBuffer(0, HEADER_SIZE) // 先读取协议头长度
                .order(ByteOrder.BIG_ENDIAN);

        // ================== 3. 解析协议头 ==================
        // 3.1 绝对位置读取总长度（不改变 position）
        int totalLen = buffer.getInt(0); // 前4字节为总长度
        if (totalLen < HEADER_SIZE) {
            throw new IOException("Invalid total length: " + totalLen);
        }

        // 3.2 扩展 ByteBuffer 范围到完整数据（若需要）
        if (totalLen > HEADER_SIZE) {
            buffer = respPtr.getByteBuffer(0, totalLen) // 重新映射完整数据
                    .order(ByteOrder.BIG_ENDIAN);
        }

        // 3.3 绝对位置读取消息ID（不改变 position）
        int msgId = buffer.getInt(SIZE_BYTES); // 偏移4字节读取消息ID

        // ================== 4. 定位消息体起始位置 ==================
        // 4.1 创建消息体视图（零拷贝切片）
        buffer.position(HEADER_SIZE);
        ByteBuffer bodyBuffer = buffer.slice();

        // 4.2 设置消息体的 limit（防止越界）
        bodyBuffer.limit(totalLen - HEADER_SIZE);

        // ================== 5. 反序列化消息体 ==================
        return MsgIds.parseFrom(msgId, CodedInputStream.newInstance(bodyBuffer));
    }

}
