package org.gof.demo.battlesrv.battle;

import org.gof.core.Port;
import org.gof.core.support.Time;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.chapter.BaseChapter;
import org.gof.demo.battlesrv.battle.module.UnitGroupCtr;
import org.gof.demo.battlesrv.battle.enumVo.BattleFlag;
import org.gof.demo.battlesrv.battle.enumVo.HealthType;
import org.gof.demo.battlesrv.battle.enumVo.RunMode;
import org.gof.demo.battlesrv.battle.enumVo.RunState;
import org.gof.demo.worldsrv.support.Log;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-06-06 18:13
 **/
public class BattleMain {
    public class CountInfo {
        public int frame;
        public long attack;
        public long def;
        public HealthType type;
        public long value;
        public int skillId;
        public int team;

        // 构造函数（如果需要的话）
        public CountInfo(int frame, long attack, long def, HealthType type, long value, int skillId, int team) {
            this.frame = frame;
            this.attack = attack;
            this.def = def;
            this.type = type;
            this.value = value;
            this.skillId = skillId;
            this.team = team;
        }
    }

    public int frameCount;
    public float timeScale;
    public float frameTime;
    public RunState runState;
    public RunMode runMode;
    private float _runTime;
    private float _logicFrameTime;
    public List<UnitGroupCtr> playerCtrs;
    public UnitGroupCtr mainCtr;
    public UnitGroupCtr showMainCtr;
    public long roleId;
    public UnitMgr unitMgr;
    public BattleData data;
    public BaseChapter chapter;
    public int collectRecord;
    public boolean playerAuto = false;
    public boolean runningToPart = false;
    public FixRandom random;
    public int battleFlag;
    public float injuryReduce;
    public float shieldDecay;
    public float treatDecay;
    public float seasonPveDamAdd;
    public boolean hitThrowDis = true;
    public int effectScale;
    public int skillCd;
    public boolean hideText;
    public boolean printLogFlag;
    private List<CountInfo> logCountMap;

    public int seed = (int) (Port.getTime() / Time.SEC);

    public float getRunTime() {
        return _runTime;
    }

    public BattleMain(int randomSeed)// randomSeed = 0 , int seed = Utils.getTimeSec();
    {
        this.frameCount = 0;
        this.timeScale = 1;
        this.frameTime = .033f;
        this.runState = RunState.Stop;
        this.runMode = RunMode.Main;
        this._runTime = 0;
        this._logicFrameTime = 0;
        this.playerCtrs = new ArrayList<>();
        this.playerAuto = false;
        this.runningToPart = false;
        this.battleFlag = BattleFlag.NONE.getValue();
        this.injuryReduce = 1;
        this.shieldDecay = 1;
        this.treatDecay = 1;
        this.hitThrowDis = true;
        this.effectScale = 1;
        this.skillCd = 1;
        this.hideText = false;
        this.printLogFlag = false;
        this.unitMgr = new UnitMgr(this);
        this.data = new BattleData(10001);

        this.seed = randomSeed > 0 ? randomSeed : Utils.random(0, 10000);
        this.random = new FixRandom(seed);
    }

    public void start() {
    }

    public void resetFactor() {
        this.injuryReduce = 1;
        this.shieldDecay = 1;
        this.treatDecay = 1;
    }

    public void stop() {
        this.runState = RunState.Stop;
        this.unitMgr.clear();
        this.chapter.destroy();
        for (UnitGroupCtr player : this.playerCtrs)
            player.clear();
        this.playerCtrs.clear();
    }

    private void _frameUpdate(float t) {
        if (this.runState == RunState.Running) {
            float e = this.frameTime;
            if (this._logicFrameTime >= e) {
                this.frameCount++;
                this.chapter.onUpdate(e);
                this.unitMgr.onUpdate(e, null);

                this._logicFrameTime -= e /*Mathf.Round(this._logicFrameTime - e)*/;
                if (t < 2 && this._logicFrameTime >= e)
                    this._frameUpdate(t + 1);
            }
        }
    }

    protected void gameLogic(float t) {
        //t = Mathf.Round(t);
        float i = /*Mathf.Round*/(t * this.timeScale);
        if (this.runState == RunState.Running) {
            this._runTime += i/*= Mathf.Round(this._runTime + i)*/;
            this._logicFrameTime += i/*= Mathf.Round(this._logicFrameTime + i)*/;
            this._frameUpdate(1);
        }
        this.onGameLogicUpdate(i);
        this.unitMgr.deferredDestroy();
    }

    public void onGameLogicUpdate(float t) {
    }

    public void reset_runTime() {
        this._runTime = 0;
    }

    protected void gc() {
    }

    public void playSound(int t) {
    }

    /**
     * 给攻击方记录自己打出的伤害
     */
    public void addAttackCauseDamage(int attackId, long causeDamage) {
        UnitHuman unit = unitMgr.getUnit(attackId);
        if (unit == null) {
            return;
        }
        unit.data.totalCauseDamage += causeDamage;
    }
}
