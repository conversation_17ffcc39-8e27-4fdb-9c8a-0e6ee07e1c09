package org.gof.demo.battlesrv.battle;

import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.demo.battlesrv.battle.module.UnitData;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-06-06 18:16
 **/
public class PlayerData implements ISerilizable {
    public long Id;
    public int Pos = 1;
    public List<UnitData> units;
    public int team;


    public PlayerData() {

    }

    // 构造函数
    public PlayerData(long id) {
        Id = id;
        units = new ArrayList<UnitData>();
    }


    @Override
    public void writeTo(OutputStream out) throws IOException {

    }

    @Override
    public void readFrom(InputStream in) throws IOException {

    }
}
