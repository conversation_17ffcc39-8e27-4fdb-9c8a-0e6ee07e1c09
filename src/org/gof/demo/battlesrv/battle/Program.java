package org.gof.demo.battlesrv.battle;

import org.gof.demo.battlesrv.battle.enumVo.RunState;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.support.Log;

public class Program {

    public static void main(String[] args) {
//        Console.WriteLine(DateTimeOffset.Now.ToUnixTimeSeconds());
        BattleData a = new BattleData();
        a.seed = 0;
        a.chapterId = 140001;
        a.chapterType = InstanceConstants.FAMILYBRAWLCHAPTER_14; //ChapterType.Gvg;
        //a.chapterId = 100001;
        //a.chapterType = ChapterType.CrossParkPvp;
        String role1 = "{\"Id\":120120000000000000,\"Name\":\"player15M78T\",\"HasName\":true,\"Lev\":1,\"HasLev\":true,\"Job\":1001,\"HasJob\":true,\"RoleSkill\":{\"ActiveSkill\":[{\"PosId\":1,\"SkillId\":1041,\"SkillLv\":1,\"DelayTime\":0},{\"PosId\":2,\"SkillId\":0,\"SkillLv\":0,\"DelayTime\":0},{\"PosId\":3,\"SkillId\":0,\"SkillLv\":0,\"DelayTime\":0},{\"PosId\":4,\"SkillId\":0,\"SkillLv\":0,\"DelayTime\":0},{\"PosId\":5,\"SkillId\":0,\"SkillLv\":0,\"DelayTime\":0},{\"PosId\":6,\"SkillId\":0,\"SkillLv\":0,\"DelayTime\":0}],\"PassiveSkill\":[]},\"PetList\":[],\"AttrList\":[{\"K\":1024,\"V\":20},{\"K\":1,\"V\":504000014},{\"K\":2,\"V\":504000259},{\"K\":3,\"V\":9000},{\"K\":1029,\"V\":1},{\"K\":1030,\"V\":1},{\"K\":1031,\"V\":6000},{\"K\":1032,\"V\":10000},{\"K\":1033,\"V\":10000},{\"K\":1038,\"V\":10000},{\"K\":1039,\"V\":10000},{\"K\":2001,\"V\":2400},{\"K\":2003,\"V\":2400},{\"K\":1045,\"V\":10000},{\"K\":2005,\"V\":2400},{\"K\":24,\"V\":4},{\"K\":1054,\"V\":20},{\"K\":1001,\"V\":2100000060},{\"K\":1002,\"V\":2100001080},{\"K\":1003,\"V\":9000},{\"K\":1005,\"V\":20000},{\"K\":1006,\"V\":10000},{\"K\":1009,\"V\":300},{\"K\":1010,\"V\":1300},{\"K\":1011,\"V\":1300},{\"K\":1013,\"V\":10000}],\"AttrObjList\":[],\"Figure\":{\"EquipList\":[{\"K\":1,\"V\":0},{\"K\":2,\"V\":0},{\"K\":3,\"V\":0},{\"K\":5,\"V\":0}],\"HairFigure\":8,\"JobFigure\":1001,\"MountFigure\":1,\"ArtifactFigure\":0,\"Gender\":1,\"SkinList\":[{\"K\":2,\"V\":0}],\"CurrentTitle\":0},\"ManualOperator\":1716784164,\"Operators\":[],\"Ext\":[{\"K\":1,\"V\":1}],\"Head\":{\"Id\":0,\"FrameId\":0,\"Url\":\"\"},\"LeftHp\":100}";
        String role2 = "{\"Id\":120120000026400000,\"Name\":\"player98L4R1\",\"HasName\":true,\"Lev\":1,\"HasLev\":true,\"Job\":0,\"HasJob\":true,\"RoleSkill\":{\"ActiveSkill\":[{\"PosId\":1,\"SkillId\":1041,\"SkillLv\":1,\"DelayTime\":0},{\"PosId\":2,\"SkillId\":0,\"SkillLv\":0,\"DelayTime\":0},{\"PosId\":3,\"SkillId\":0,\"SkillLv\":0,\"DelayTime\":0},{\"PosId\":4,\"SkillId\":0,\"SkillLv\":0,\"DelayTime\":0},{\"PosId\":5,\"SkillId\":0,\"SkillLv\":0,\"DelayTime\":0},{\"PosId\":6,\"SkillId\":0,\"SkillLv\":0,\"DelayTime\":0}],\"PassiveSkill\":[]},\"PetList\":[],\"AttrList\":[{\"K\":1024,\"V\":20},{\"K\":1,\"V\":14},{\"K\":2,\"V\":259},{\"K\":3,\"V\":9000},{\"K\":1029,\"V\":1},{\"K\":1030,\"V\":1},{\"K\":1031,\"V\":6000},{\"K\":1032,\"V\":10000},{\"K\":1033,\"V\":10000},{\"K\":1038,\"V\":10000},{\"K\":1039,\"V\":10000},{\"K\":2001,\"V\":2400},{\"K\":2003,\"V\":2400},{\"K\":1045,\"V\":10000},{\"K\":2005,\"V\":2400},{\"K\":24,\"V\":4},{\"K\":1054,\"V\":20},{\"K\":1001,\"V\":60},{\"K\":1002,\"V\":1080},{\"K\":1003,\"V\":9000},{\"K\":1005,\"V\":20000},{\"K\":1006,\"V\":10000},{\"K\":1009,\"V\":300},{\"K\":1010,\"V\":1300},{\"K\":1011,\"V\":1300},{\"K\":1013,\"V\":10000}],\"AttrObjList\":[],\"Figure\":{\"EquipList\":[{\"K\":1,\"V\":0},{\"K\":2,\"V\":0},{\"K\":3,\"V\":0},{\"K\":5,\"V\":0}],\"HairFigure\":8,\"JobFigure\":1001,\"MountFigure\":0,\"ArtifactFigure\":0,\"Gender\":1,\"SkinList\":[{\"K\":2,\"V\":0}],\"CurrentTitle\":0},\"ManualOperator\":1716784164,\"Operators\":[],\"Ext\":[{\"K\":1,\"V\":2}],\"Head\":{\"Id\":0,\"FrameId\":0,\"Url\":\"\"},\"LeftHp\":100}";

        a.playerList.put(1L, new PlayerData(1L));
        a.playerList.put(2L, new PlayerData(2L));
        Define.p_battle_role.Builder AtkData = Define.p_battle_role.newBuilder();//Newtonsoft.Json.JsonConvert.DeserializeObject<p_battle_role>(role1);
        BattleDataFill.setPlayerList(AtkData.build(), a.playerList.get(1L), a.chapterType);
        Define.p_battle_role.Builder DefData = Define.p_battle_role.newBuilder(); // Newtonsoft.Json.JsonConvert.DeserializeObject<p_battle_role>(role2);
        BattleDataFill.setPlayerList(DefData.build(), a.playerList.get(2L), a.chapterType);

        BattleMainServer i = new BattleMainServer((int) a.seed);
        i.start(a, null);
        while (i.runState == RunState.Running)
            i.update(i.frameTime);
        long e = i.chapter.getBattleMain().showMainCtr.player.data.currenHp;
        long c = i.chapter.getBattleMain().mainCtr.player.data.currenHp;

//        Console.WriteLine("winer is " + (0 == i.result ? AtkData.Id : DefData.Id));
//        Console.WriteLine(DateTimeOffset.Now.ToUnixTimeSeconds());
    }
}


