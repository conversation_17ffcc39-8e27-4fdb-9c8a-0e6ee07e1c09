package org.gof.demo.battlesrv.battle.module.aI;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.BattleFlag;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;
import org.gof.demo.battlesrv.battle.module.TargetSkill;
import org.gof.demo.battlesrv.battle.module.skill.Skill;

import java.util.List;

public class T20BossAI extends IAIHandler {
    protected boolean startSkill() {
        UnitHuman t = this.cast;
        if ((t.battleMain().battleFlag & (int) BattleFlag.NOT_HURT.getValue()) == 0) {
            List<Skill> e = t.data.skillList;
            if (e == null || e.isEmpty())
                return false;
            for (Skill o : e) {
                if (o.state == 0) {
                    if (t.battleMain().unitMgr.findTarget(t, TargetFilter.Enemy, o.config.autoDis, t.Position, true).isEmpty())
                        continue;
                    t.setWantSkill(TargetSkill.toSkill(o, TargetSkill.TP_AutoFind));
                    return true;
                }
            }
        }
        return false;
    }
}
