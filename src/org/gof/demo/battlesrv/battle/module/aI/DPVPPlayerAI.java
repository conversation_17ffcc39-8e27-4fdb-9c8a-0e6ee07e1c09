package org.gof.demo.battlesrv.battle.module.aI;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.bentity.ctroller.HatredCtr;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;
import org.gof.demo.worldsrv.entity.Unit;

import java.util.ArrayList;
import java.util.List;

public class DPVPPlayerAI extends IAIHandler {

    @Override
    public void handleAction() {
        UnitHuman t = this.getTarget();
        if (null == t)
        {
            this.cast.wantTarget = null;
            this.cast.curTarget = null;
            this.startSkill();
            return;
        }
        this.startSkill();
        this.selectTarget(t);
    }

    @Override
    protected UnitHuman getTarget() {
        UnitHuman e = this.cast;
        List<HatredCtr.Hatred> n = e.hatredCtr.getHatredList();
        List<HatredCtr.Hatred> r = new ArrayList<>();
        for (HatredCtr.Hatred a : n)
        {
            if (!a.unit.isDead())
            {
                if (a.unit.isCallType)
                    return a.unit;
                if (a.unit.Position.y == e.Position.y)
                    r.add(a);
            }
        }
        boolean l = e.curTarget != null && e.curTarget.getUnit() != null && !e.curTarget.getUnit().isDead();
        if (r.isEmpty() && l)
        {
            r = null;
            return e.curTarget.getUnit();
        }
        if (!r.isEmpty() && l)
        {
            boolean f = false;
            for (HatredCtr.Hatred h : r)
            {
                if (!h.unit.isDead() && h.unit == e.curTarget.getUnit())
                {
                    f = true;
                    break;
                }
            }
            if (f)
            {
                r = null;
                return e.curTarget.getUnit();
            }
        }
        if (r.isEmpty())
            r.addAll(n);
        UnitHuman g = null;
        for (int d = 0; !r.isEmpty() && d < 20;)
        {
            int v = e.battleMain.random.randomInt(0, r.size());
            UnitHuman p = r.get(v).unit;
            d++;
            if (!p.isDead())
            {
                g = p;
                break;
            }
        }
        r = null;
        return g;
    }

    @Override
    protected boolean startSkill() {
        UnitHuman t = this.cast;
        if (!t.battleMain.playerAuto)
        {
            if (t.battleMain.showMainCtr.player == t)
                return false;
        }
        else if (!t.battleMain.playerAuto)
            return false;
        List<Skill> e = t.data.skillList;
        if (null == e || e.isEmpty())
            return false;
        for (Skill s : e)
        {
            if (t.aiTime >= s.useDelay && 0 == s.state)
            {
                if (t.battleMain.unitMgr.findTarget(t, TargetFilter.Enemy, s.config.autoDis + t.skillAutoDis, t.Position).isEmpty())
                    continue;
                t.addUnlimitSkill(s);
            }
        }
        return false;
    }
}

