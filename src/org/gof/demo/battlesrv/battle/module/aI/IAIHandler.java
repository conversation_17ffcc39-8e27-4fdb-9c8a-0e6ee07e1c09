package org.gof.demo.battlesrv.battle.module.aI;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.TargetUnit;

public abstract class IAIHandler {
    public UnitHuman cast;

    protected boolean selectTarget(UnitHuman t) {
        if (null == t)
            return false;
        UnitHuman n = this.cast;
        n.setWantTarget(TargetUnit.toUnit(t));
        return !(null == n.wantTarget() || n.wantTarget().getUnit() != t);
    }

    public void handleAction() {
        UnitHuman t = this.getTarget();
        this.startSkill();
        this.selectTarget(t);
    }

    protected UnitHuman getTarget() {
        return this.cast.hatredCtr.getMaxHatredUnit();
    }

    protected abstract boolean startSkill();
}

