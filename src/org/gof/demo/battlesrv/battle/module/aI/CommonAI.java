package org.gof.demo.battlesrv.battle.module.aI;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;

import java.util.List;

public class CommonAI extends IAIHandler {
    @Override
    protected boolean startSkill() {
        UnitHuman t = this.cast;
        List<Skill> e = t.data.skillList;
        if (null == e || 0 == e.size())
            return false;
        for (Skill a : e) {
            if (0 == a.state) {
                if (0 == t.battleMain().unitMgr.findTarget(t, TargetFilter.Enemy,
                        a.config.autoDis + t.skillAutoDis, t.Position, true).size())
                    continue;
                t.addUnlimitSkill(a);
                return true;
            }
        }
        return false;
    }
}

