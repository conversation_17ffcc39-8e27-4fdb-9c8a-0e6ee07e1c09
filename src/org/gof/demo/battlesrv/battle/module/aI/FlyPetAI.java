package org.gof.demo.battlesrv.battle.module.aI;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.TargetSkill;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;

import java.util.List;

public class FlyPetAI extends IAIHandler {

    @Override
    public void handleAction() {
        UnitHuman t = this.cast;
        List<UnitHuman> n = t.battleMain.unitMgr.findTarget(t, TargetFilter.Enemy, t.attackDistance(), t.Position);
        if (!n.isEmpty()) this.selectTarget(n.get(0));
        this.startSkill();
    }

    @Override
    protected boolean startSkill() {
        UnitHuman t = this.cast;
        List<Skill> e = t.data.skillList;
        if (null == e || e.isEmpty())
            return false;
        for(Skill l : e)
        {
            if (0 == l.state)
            {
                if (t.battleMain.unitMgr.findTarget(t, TargetFilter.Enemy, l.config.autoDis + t.skillAutoDis, t.Position).isEmpty())
                    continue;
                t.wantSkill = TargetSkill.toSkill(l,TargetSkill.TP_AutoFind);
            }
        }
        return false;
    }
}

