package org.gof.demo.battlesrv.battle.module.aI;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;

import java.util.List;

public class PlayerAI extends IAIHandler {
    @Override
    protected boolean startSkill() {
        UnitHuman t = this.cast;
        if (t.battleMain().playerAuto) {
            List<Skill> e = t.data.skillList;
            if (null == e || 0 == e.size())
                return false;
            for (Skill l : e) {
                if (t.aiTime >= l.useDelay && 0 == l.state) {
                    if (0 == t.battleMain().unitMgr.findTarget(t, TargetFilter.Enemy,
                            l.config.autoDis + t.skillAutoDis, t.Position, true).size())
                        continue;
                    t.addUnlimitSkill(l);
                }
            }
        }
        return false;
    }
}

