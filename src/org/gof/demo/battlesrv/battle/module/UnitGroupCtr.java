package org.gof.demo.battlesrv.battle.module;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.PlayerData;
import org.gof.demo.battlesrv.battle.bentity.ctroller.MovementCtr;
import org.gof.demo.battlesrv.battle.enumVo.StateType;
import org.gof.demo.battlesrv.battle.enumVo.UnitType;
import org.gof.demo.battlesrv.support.Vector2D;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class UnitGroupCtr {
    public static HashMap<Integer, HashMap<Integer, Vector2D>> points ;

    private void InitMap() {
        points = new HashMap<>();
        HashMap<Integer, Vector2D> map1 = new HashMap<>();
        map1.put( 1, new Vector2D(220, 0));
        map1.put(3, new Vector2D(240, 60));
        map1.put(2, new Vector2D(210, -60));
        map1.put( 6, new Vector2D(160, 60));
        map1.put( 4, new Vector2D(140, 0));
        map1.put( 5, new Vector2D(130, -60));
        map1.put( 7, new Vector2D(290, -60));
        map1.put(8, new Vector2D(300, 160));
        points.put(1, map1);
        HashMap<Integer, Vector2D> map2 = new HashMap<>();
        map2.put(1, new Vector2D(180, 0));
        map2.put(3, new Vector2D(240, 60));
        map2.put(2, new Vector2D(210, -60));
        map2.put(6, new Vector2D(180, 60));
        map2.put(4, new Vector2D(100, 60));
        map2.put(5, new Vector2D(130, -60));
        map2.put(7, new Vector2D(290, -60));
        map2.put(8, new Vector2D(300, 160));
        points.put(2, map2);
    }

    public PlayerData playerData;
    public List<UnitHuman> units = new ArrayList<>();
    public UnitHuman player;
    public int offsetY;
    public int offsetX;
    public int type;
    public int idleType = 1;

    public UnitGroupCtr() {
        InitMap();
    }

    public UnitGroupCtr(int t)//t = 1
    {
        units = new ArrayList<>();
        this.idleType = t;
        InitMap();
    }

    public void onUpdate(float t) {
    }

    public void positionSelected(float t, boolean e) {
        positionSelected(t, e, 0);
    }

    public void positionSelected(float t, boolean e, int n) {
        if (null == this.player)
            return;

        List<UnitHuman> s = this.units;
        Vector2D u = new Vector2D(0, 0);
        HashMap<Integer, Vector2D> a = points.get(idleType);
        for (int l = 0; l < s.size(); ++l) {
            u = new Vector2D(t, 0);
            Vector2D f = a.get(s.get(l).data.idleIndex);
            if (0 == this.type)
                u = u.sum(new Vector2D(f.x + this.offsetX, f.y + this.offsetY));
            else
                u = u.sum(new Vector2D(-(f.x + this.offsetX), f.y + this.offsetY));

            if ((1 & n)>0 && s.get(l) == this.player)
            {
            }
            else if ((2 & n)>0 && s.get(l).config().type == UnitType.Partner.getValue() || (1 & n)>0  && s.get(l).config().type == UnitType.FlyPet.getValue())
            {
            }
            else
            {
                if (s.get(l) != this.player) s.get(l).parent = this.player;
                if (e) {
                    s.get(l).setNextPoint(null);
                    s.get(l).setCurTarget(null);
                    s.get(l).setWantTarget(null);
                    s.get(l).Position = u;
                } else {
                    s.get(l).setWantTarget(TargetUnit.toPos(u, TargetUnit.TP_MANUAL_POINT));
                }

                if (s.get(l).battleMain.hitThrowDis)
                    s.get(l) .movectr.limitRangle = new MovementCtr.Rangle((float) this.offsetX, (float)u.x);
            }
        }
    }

    public boolean IsIdle() {
        List<UnitHuman> n = this.units;
        int i = 0;
        for (UnitHuman r : n) {
            if (r.isDead() || r.statectr.currentStateType() == StateType.Idle)
                i++;
        }
        return i >= n.size();
    }

    public void clear() {
        this.player = null;
        this.units.clear();
    }
}