package org.gof.demo.battlesrv.battle.module;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.support.Vector2D;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-05-28 12:17
 **/
public class TargetUnit {
    public static final int T_POSITION = 1;
    public static final int T_UNIT = 2;
    public static final int TP_AUTO_FIND = 0;
    public static final int TP_MANUAL_POINT = 1;
    public static final int TP_FORCED = 2;

    private int type;
    private int priority;
    private UnitHuman unit;
    private Vector2D pos;

    public TargetUnit() {
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public UnitHuman getUnit() {
        return unit;
    }

    public void setUnit(UnitHuman unit) {
        this.unit = unit;
    }

    public Vector2D getPos() {
        return pos;
    }

    public void setPos(Vector2D pos) {
        this.pos = pos;
    }

    public static TargetUnit toUnit(UnitHuman unit, int priority) {
        if (unit == null) {
            return null;
        }
        TargetUnit targetUnit = new TargetUnit();
        targetUnit.setType(T_UNIT);
        targetUnit.setPriority(priority);
        targetUnit.setUnit(unit);
        targetUnit.setPos(unit.Position);
        return targetUnit;
    }

    // 提供一个默认优先级的重载方法
    public static TargetUnit toUnit(UnitHuman unit) {
        return toUnit(unit, TP_AUTO_FIND);
    }

    public static TargetUnit toPos(Vector2D pos, int priority) {
        TargetUnit targetUnit = new TargetUnit();
        targetUnit.setType(T_POSITION);
        targetUnit.setPriority(priority);
        targetUnit.setPos(pos);
        return targetUnit;
    }

    public static TargetUnit toPos(Vector2D pos) {
        return toPos(pos, TP_AUTO_FIND);
    }

    public static void free(TargetUnit value) {
    }
}


