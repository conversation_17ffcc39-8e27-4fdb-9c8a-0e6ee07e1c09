package org.gof.demo.battlesrv.battle.module.skill.SkillHandler;

import org.gof.core.Port;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.Time;
import org.gof.demo.battlesrv.battle.TimerGroup;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.worldsrv.config.ConfSkill;
import org.gof.demo.worldsrv.support.D;

public class SkillHandleRandomSkill extends SkillHandleBase {
    public SkillHandleRandomSkill(SkillRunner runner) {
        super(runner);
    }

    @Override
    public void beginRun() {
        SkillHandleRandomSkill t = this;
        ConfSkill i = this.runner.useSkill().config;
        if (i.releaseTime > 0) {
            this._timerId = this.addTimer(2, -1, obj -> {
                toRandomSkill();
                timers.get(t._timerId).duration = i.releaseInterval;
            });
        }
    }

    private void toRandomSkill() {
        SkillRunner t = this.runner;
        if (t.cast().data.skillList.size() > 1) {
            int i = t.battleMain.random.Next(1,t.cast().data.skillList.size());
            Skill n = t.cast().data.skillList.get(i);
            if (n == null)
                return;
            Skill l = new Skill();
            l.config = n.config;
            l.level = n.level;
            l.skillDam = n.skillDam;
            l.triggerEffect = false;
            l.checkState();
            t.cast().setSkillToSkill(l);
        }
    }
}