package org.gof.demo.battlesrv.battle.module.skill.SkillHandler;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfSkilleffcet;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-05-28 12:17
 **/
public class SkillEffectTask {
    public int id;
    public ConfSkilleffcet config;
    public SkillRunner runner;
    public SkillHandleBase handle;
    public UnitHuman target;
    public float totalTime = 0;
    public float curTotalTime = 0;
    public float delayTime = 0;
    public float duration = 0;
    protected float curTime = 0;
    public int index;
    public Vector2D pos;
    public boolean isStop;

    public void onUpdate(float t) {
        if (!this.isStop) {
            if (this.delayTime > 0) {
                this.delayTime -= t;
                if (this.delayTime <= 0)
                    this.handle.onExecuteEffectAction(this, false);
                return;
            }
            if (0 != this.totalTime) {
                if (this.curTime >= this.duration) {
                    this.handle.onExecuteEffectAction(this, false);
                    this.curTime -= this.duration;
                    if (this.totalTime > 0 && this.curTotalTime >= this.totalTime) {
                        this.handle.onExecuteEffectAction(this, true);
                        this.isStop = true;
                        return;
                    }
                }
                this.curTotalTime += t;
                this.curTime += t;
            } else
                this.isStop = true;
        }
    }

    public void cacheReset() {
        this.isStop = false;
        this.curTotalTime = 0;
        this.curTime = 0;
        this.totalTime = 0;
        this.duration = 0;
        this.index = 0;
        this.handle = null;
        this.runner = null;
        this.target = null;
        this.delayTime = 0;
    }
}