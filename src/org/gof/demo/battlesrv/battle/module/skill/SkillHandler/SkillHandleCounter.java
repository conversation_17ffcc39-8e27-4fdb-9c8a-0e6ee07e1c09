package org.gof.demo.battlesrv.battle.module.skill.SkillHandler;

import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.ctroller.SkillCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.HurtUtil;
import org.gof.demo.battlesrv.battle.module.buff.Buff;
import org.gof.demo.battlesrv.battle.module.Bullet;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.enumVo.*;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfUnitType;

import java.util.List;

public class SkillHandleCounter extends SkillHandleBase {
    public SkillHandleCounter(SkillRunner runner) {
        super(runner);
    }

    @Override
    public void beginRun() {
        SkillRunner t = this.runner;
        t.healthTarget(t.cast(), 0, HealthType.Counter_Act, false);
        t.useSkill().state = 0;
        this.triggerAction(t);
        t.interrupt();
    }

    private void att(SkillRunner t, UnitHuman e) {
        if (!t.cast().isDestroy() && null != e && !e.isDestroy()) {
            AttackType n = HurtUtil.checkHit(t.cast(), e, false);
            if (n.getValue() > AttackType.Miss.getValue()) {
                long f = HurtUtil.normalCounterHurt(t.cast(), e, n);
                HealthType g = n == AttackType.Normal ? HealthType.Hurt : HealthType.Hurt_Crit;
                float d = this.runner.cast().data.getAttrib(AttribDefine.boss_dam);
                if (e.config().type == UnitType.Boss.getValue() && d > 0) f = Math.round(f * (1D + d));
                List<Buff> y = e.buffCtr.getBuffByType(BuffGroupType.FRAGILE_EFFECT.getValue());
                for (Buff k : y) {
                    f += k.calDamage(f, t.cast(),0);
                }
                y.clear();
                t.healthTarget(e, f, g, false);
                ConfUnitType B = ConfUnitType.get(t.cast().config().type);
                if (HurtUtil.checkCounterThrowHit(t.cast(), e))
                    t.throwHit(e, B.suspend_time[0], B.suspend_time[1], 0);
                List<SkillCtr.SkillEffect> C = t.cast().skillctr.skillEffects;
                for (SkillCtr.SkillEffect xhx : C) {
                    if (xhx.triggerType == (int) EffectTriggerType.COUNTER.getValue()||xhx.triggerType == (int) EffectTriggerType.ALL_ATTACK.getValue()) {
                        xhx.num++;
                        if (xhx.limit <= 0 || xhx.num % xhx.limit == 0) {
                            if (0 == xhx.useType)
                                this.addTask(xhx.id, e.Position, xhx.runner, e);
                            else if (1 == xhx.useType)
                                t.cast().skillctr.addSkill(xhx.id);
                        }
                    }
                }
                if (n == AttackType.Cirt)
                {
                    for (int i = 0; i < C.size(); i++)
                    {
                        SkillCtr.SkillEffect M = C.get(i);
                        if (M.triggerType == EffectTriggerType.CRIT_ATTACK.getValue())
                        {
                            M.num++;
                            if (M.limit > 0 && M.num % M.limit != 0)
                                continue;
                            if (0 == M.useType)
                                this.addTask(M.id, e.Position, M.runner, e);
                            else if (1 == M.useType)
                                t.cast().skillctr.addSkill(M.id);
                        }
                    }
                }
                List<SkillCtr.SkillEffect> U = e.skillctr.skillEffects;
                for (int zz = 0; zz < U.size(); zz++)
                {
                    SkillCtr.SkillEffect N = U.get(zz);
                    if (N.triggerType != EffectTriggerType.HP_Hurt.getValue())
                        continue;
                    N.num++;
                    if (N.limit > 0 && N.num % N.limit != 0)
                        continue;
                    if (0 == N.useType)
                        this.addTask(N.id, e.Position, N.runner, e);
                    else if (1 == N.useType)
                        e.skillctr.addSkill(N.id);
                }
                List<Buff> O = e.buffCtr.getBuffByType(BuffGroupType.VAMPIRE.getValue());
                for (Buff q : O)
                {
                    if (q.runner.cast() != t.cast() && q.runner.cast() != t.cast().parent)
                        continue;
                    q.calDamage(f, e, 0);
                }
            } else
                t.healthTarget(e, 0, HealthType.Miss, false);
        }
    }

    private void triggerAction(SkillRunner t) {
        int e = t.cast().config().bullet;
        if (t.cast().data.skinConfig() != null && t.cast().data.skinConfig().bullet > 0)
            e = t.cast().data.skinConfig().bullet;
        if (null != t.configWeapon() && t.configWeapon().bullet > 0)
            e = t.configWeapon().bullet;
        t.cast().hurtNumCount++;
        if (0 == e) {
            this.att(t, t.getLockTarget());
            SkillCommonEffectUtil.checkTriggerBullet(this, t, t.getLockTarget(), new Vector2D(0, 0), e);
        } else {
            t.addBullet(t.getLockTarget(), e, true).param.add(BattleParamKey.SkillEffectMark_Normal);
            Vector2D i = t.cast().getBindPos(BindType.bp_lead);
            SkillCommonEffectUtil.checkTriggerBullet(this, t, t.getLockTarget(), i, e);
        }
    }

    @Override
    public void onBulletAction(Bullet t, UnitHuman e, Vector2D i, List<Object> n) {
        if (null != e && !e.isDestroy()) {
            SkillRunner r = this.runner;
            if (r.cast().shamUnit || (r.battleMain.battleFlag & (int) BattleFlag.NOT_HURT.getValue()) > 0)
                return;
            if (null != n && 0 != n.size()) {
                if (!BattleParamKey.SkillEffectMark_Bullet.equals(String.valueOf(n.get(0))))
                    this.att(this.runner, e);
                else
                    SkillCommonEffectUtil.bulletHurt(r, e, (float) n.get(1));
            }
        }
    }
}