package org.gof.demo.battlesrv.battle.module.skill.SkillHandler;

import org.gof.core.support.Utils;
import org.gof.core.support.function.GofFunction1;
import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.module.Bullet;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.TimerGroup;
import org.gof.demo.battlesrv.battle.enumVo.BindType;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;
import org.gof.demo.battlesrv.battle.enumVo.TargetSelectFilter;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfSkill;
import org.gof.demo.worldsrv.config.ConfSkilleffcet;
import org.gof.demo.worldsrv.support.Log;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-05-28 12:17
 **/
public class SkillHandleBase {
    protected SkillRunner runner;
    protected List<SkillEffectTask> taskList;
    protected int _skillEffectId = 0;
    protected int _timerId = 0;
    protected TimerGroup timers;

    private Vector2D d;

    public SkillHandleBase(SkillRunner runner) {
        this.runner = runner;
    }

    public void beginRun() {
    }

    public void onModelEnd() {

    }

    public void onActTargetAction(Object t, Object e) {
    }

    protected void onDeadTargetAction(Object t) {
    }

    protected void onBeHitAction(Object t, Object e, Object i) {
    }

    protected void checkBuff(SkillEffectTask t, UnitHuman i) {
        ConfSkilleffcet r = t.config;
        if (r.buffGroup != null && !r.buffGroup.isEmpty() && r.buffRate != null && !r.buffRate.isEmpty()) {
            int[] c = t.runner.useSkill().skillDam;
            if (t.index > 0) c = t.runner.useSkill().skillDamList.get(t.index);
            String[] l = r.buffRate.split("\\|");
            for (String s : l) {
                String[] o = s.split(",");
                int f = t.runner.battleMain.random.Next(0, 10000);
                if (f <= Math.round(10000 * Utils.floatValue(o[1]))) {
                    String[] b = r.buffGroup.split("\\|");
                    for (int u = 0; u < b.length; u++) {
                        String[] h = b[u].split(",");
                        if (h[0].equals(o[0])) {
                            int d = r.skillPar != null && r.skillPar.length > u ? r.skillPar[u] : 1;
                            float g = null != c && c.length >= d ? (c[d - 1] / 10000f) : 1f;
                            float n = this.runner.cast().data.getSkillFactAttrValue(Utils.floatValue(h[3]), this.runner.useSkill().config.sn, AttribDefine.active_skillbuff_time.getValue());
                            t.runner.addBuff(i, Utils.intValue(h[1]), n, g);
                        }
                    }
                }
            }
        }
    }

    protected void execSkillEffect(UnitHuman t, SkillEffectTask e) {
        this.execHitAction(t, e);
    }

    public SkillEffectTask addTask(int t, Vector2D e, SkillRunner i)// n = null
    {
        return addTask(t, e, i, null);
    }

    public SkillEffectTask addTask(int t, Vector2D e, SkillRunner i, UnitHuman n)// n = null
    {
        return addTask(t, e, i, n,0);
    }

    public SkillEffectTask addTask(int t, Vector2D e, SkillRunner i, UnitHuman n,int r)// n = null
    {
        ConfSkilleffcet s = ConfSkilleffcet.get(t);
        if (null == s) {
            Log.temp.error("===ConfSkilleffcet not find sn={}", t);
//            Console.WriteLine("技能：" + i.useSkill().config.sn + " 效果ID：" + t + " 没找到!");
            return null;
        }
        return this._addTask(s, e, i, n,r);
    }

    private SkillEffectTask _addTask(ConfSkilleffcet t, Vector2D e, SkillRunner i, UnitHuman n,int r) {
        if (this.taskList == null)
            this.taskList = new ArrayList<>();
        this._skillEffectId++;
        SkillEffectTask s = new SkillEffectTask();
        s.config = t;
        s.id = this._skillEffectId;
        s.handle = this;
        s.target = n;
        s.index = r;
        s.pos = e;

        float o = 0;
        if (t.execute != null && t.execute.length > 0) {
            float f = i.cast().data.getSkillFactAttrValue(t.execute[1], this.runner.useSkill().config.sn, AttribDefine.active_skillbuff_time.getValue());
            s.totalTime = f;
            s.duration = t.execute[0];
            o = f;
        }
        if (t.effectDelay > 0)
            s.delayTime = (t.effectDelay / 1000f);
        s.runner = i;
        if (0 == s.totalTime && 0 == t.effectDelay) {
            s.isStop = true;
            this.execHitAction(n, s);
            return null;
        }
        this.runner.pushRun();
        this.taskList.add(s);
        return s;
    }

    protected void execEffectTarget(UnitHuman t, SkillEffectTask i) {
        int[] n = i.config.skillEffect;
        if (n != null) {
            for (int a : n) {
                if (a == i.config.sn) {
                    Log.temp.error("===技能效果死循环={}", a);
//                    Console.WriteLine("技能效果死循环!!:" + a);
                    break;
                }
                this.addTask(a, i.pos, i.runner, t);
            }
        }
    }

    protected void _skillEffect1() {
        SkillRunner t = this.runner;
        if (t.cast().dead) {
            if (this._timerId > 0) {
                this.timers.stop(this._timerId);
                this._timerId = 0;
            }
        } else {
            ConfSkill i = t.useSkill().config;
            UnitHuman n = t.getLockTarget();
            if (null == n || n.dead) {
                if (i.autoDis > 0) {
                    List<UnitHuman> r = t.getTargets(TargetFilter.Enemy, i.autoDis, t.cast().Position, 1, TargetSelectFilter.NearTarget);
                    if (r.isEmpty()) {
                        int s = t.battleMain.random.Next(100, 300);
                        double l = t.cast().Position.x + (t.cast().direction * s);
                        d = new Vector2D(l, t.cast().Position.y);
                    } else
                        d = r.get(0).Position;
                } else {
                    int u = t.battleMain.random.Next(100, 300);
                    double c = t.cast().Position.x + (t.cast().direction * u);
                    d = new Vector2D(c, t.cast().Position.y);
                }
            } else
                d = n.Position;

            if (i.targetType != null && 0 != i.targetType.length) {
                int[] h = i.skillEffect1;
                if (h != null && 0 != h.length) {
                    List<UnitHuman> v = t.getTargets(TargetFilter.fromValue(i.targetType[0]), i.targetRange, d,
                            i.targetType[1], TargetSelectFilter.fromValue(i.targetType[2]));
                    if (!v.isEmpty() && i.targetType[0] < 4)
                        d = v.get(0).Position;
                    if (i.bullet > 0) {
                        for (UnitHuman k : v) {
                            t.addBullet(k, i.bullet, true).param.add(BattleParamKey.SkillEffectMark_Effect1);
                        }
                    } else {
                        for (int y : h) {
                            this.addTask(y, d, t, n);
                        }
                    }
                }
            }
        }
    }

    protected void _skillEffect2() {
        SkillRunner t = this.runner;
        ConfSkill i = t.useSkill().config;
        if (null != i.rangeType && 0 != i.rangeType.length) {
            int[] n = i.skillEffect2;
            if ((n != null && 0 != n.length)|| t.useSkill().skillEffectList != null) {
                int r = t.cast().direction;
                for (int s = 0; s < i.rangeType.length; s += 2) {
                    double l = t.cast().Position.x + ((r * i.rangeType[s + 0]) * i.rangeType[s + 1]);
                    d = new Vector2D(l, t.cast().Position.y);
                    if (i.bullet > 0)
                        t.addBulletToPos(d, t.cast().getBindPos(BindType.bp_lead), i.bullet).param.add(BattleParamKey.SkillEffectMark_Effect2);
                    else {
                        List<int[]> o = t.useSkill().skillEffectList;
                        if (o != null&& !o.isEmpty())
                        {
                            for (int f = 0; f < o.size(); f++)
                            {
                                int[] c = o.get(f);
                                for(int v : c)
                                {
                                    this.addTask(v, d, t, null, f);
                                }
                            }
                        }
                        else
                        {
                            for (int c : n) {
                                this.addTask(c, d, t,null,0);
                            }
                        }
                    }
                }
            }
        }
    }

    protected void execBeginAction() {
        SkillHandleBase t = this;
        SkillRunner e = this.runner;
        ConfSkill i = e.useSkill().config;

        if (i.releaseTime > 0) {
            int n = Math.round(i.releaseTime / i.releaseInterval);
            e.useSkill().currenRelease = 0;
            this._timerId = this.addTimer(i.releaseInterval, n, obj -> {
                if (i.releaseInterval <= i.releaseTime)
                {
                    t._skillEffect1();
                    t._skillEffect2();
                }
            });
        } else
            e.resetPower();

        this._skillEffect1();
        this._skillEffect2();
    }

    protected void execHitAction(UnitHuman t, SkillEffectTask i) {
        ConfSkilleffcet s = i.config;
        int l = s.skillPar!=null && s.skillPar.length > 0 ? s.skillPar[0] : 1;
        int[] n = i.runner.useSkill().skillDam;
        if (i.index > 0) n = i.runner.useSkill().skillDamList.get(i.index);
        float o =  n.length >= l ? n[l - 1] / 10000f : 1f;
        if (s.trapId != null) {
            for (int c : s.trapId) {
                this.runner.addTrap(c, i.pos, o);
            }
        }

        if (s.buffGroup2 != null && !s.buffGroup2.isEmpty()) {
            String[] group = Utils.strToStrArray(s.buffGroup2, "\\|");
            for (String g : group) {
                String[] val = Utils.strToStrArray(g, "\\,");
                this.runner.addBuffByPos(i.pos, Utils.intValue(val[0]), Utils.floatValue(val[1]), o);
            }
        }

        if (null != t) {
            this.checkBuff(i, t);
            this.execEffectTarget(t, i);
        }
    }

    public void onExecuteEffectAction(SkillEffectTask t, boolean i)// i = false
    {
        SkillRunner n = this.runner;
        if (!i) {
            ConfSkilleffcet r = t.config;
            Vector2D s = t.pos;
            if (r.targetType != null && r.targetType.length > 0) {
                List<UnitHuman> a = n.getTargets((TargetFilter.fromValue(r.targetType[0])), r.targetRange, s, r.targetType[1]
                        , (TargetSelectFilter.fromValue(r.targetType[2])));
                int[][] l = r.skillEffect_rate;
                List<Integer> o = new ArrayList<>();
                if (l != null) {
                    for (int[] c : l) {
                        if (t.runner.battleMain.random.Next(0, 10000) <= c[1])
                            o.add(c[0]);
                    }
                }

                for (int j = 0; j < a.size(); j++)
                {
                    UnitHuman v = a.get(j);
                    this.execSkillEffect(v, t);
                    for (int k : o) {
                        if (k == t.config.sn) {
                            Log.temp.error("技能效果死循环={}", k);
                            continue;
                        }
                        this.addTask(k, t.pos, t.runner, v);
                    }
                }
            } else {
                if (null != t.target)
                    this.execSkillEffect(t.target, t);
            }
        }
    }

    public void onBulletAction(Bullet t, UnitHuman i, Vector2D n, List<Object> r) {
        if (null != r && !r.isEmpty()) {
            String type = r.get(0) instanceof String ? String.valueOf(r.get(0)) : "";
            if (!BattleParamKey.SkillEffectMark_Effect1.equals(type)) {
                if (!BattleParamKey.SkillEffectMark_Effect2.equals(type))
                    this.execHitAction(i, (SkillEffectTask) r.get(0));
                else {
                    List<int[]> s = this.runner.useSkill().skillEffectList;
                    if (s != null&& !s.isEmpty())
                    {
                        for (int l = 0; l < s.size(); l++)
                        {
                            int[] f = s.get(l);
                            for (int c : f)
                            {
                                this.addTask(c, n, this.runner, i, l);
                            }
                        }
                    }
                    else {
                        int[] a = this.runner.useSkill().config.skillEffect2;
                        for (int o : a) {
                            this.addTask(o, n, this.runner, i);
                        }
                    }
                }
            } else {
                int[] u = this.runner.useSkill().config.skillEffect1;
                for (int h : u) {
                    this.addTask(h, i.Position, this.runner, i);
                }
            }
        }
    }

    public void onUpdate(float t) {
        SkillHandleBase e = this;
        SkillRunner i = this.runner;
        if (this.timers != null) {
            if (this._timerId > 0) {
                i.useSkill().currenRelease = (i.useSkill().currenRelease + t);
            }
            this.timers.update(t,obj->{
                this.runner.popRun();
                if (obj.id == this._timerId) {
                    this._timerId = 0;
                    this.runner.useSkill().currenRelease = 0;
                    this.runner.resetPower();
                }
            });
        }
        List<SkillEffectTask> n = this.taskList;
        if (null != n) {
            List<SkillEffectTask> removeList = new ArrayList<>();
            for (int r = 0; r < n.size(); r++) {
                SkillEffectTask s = n.get(r);
                if (s.isStop) {
                    removeList.add(s);
                    i.popRun();
                }else{
                    s.onUpdate(t);
                }
            }
            n.removeAll(removeList);
        }
    }
    public int addTimer(float t, int e) {
        return addTimer(t, e, null, null);
    }

    public int addTimer(float t, int e, GofFunction1<Object> i) {
        return addTimer(t, e, i, null);
    }

    public int addTimer(float t, int e, GofFunction1<Object> i, Object n) {// e = 1， i = null，n = null
        if (this.timers == null) {
            this.timers = new TimerGroup("");
        }

        int s = this.timers.start(t, e, i, null);
        this.runner.pushRun();
        return s;
    }

    public void destroy() {
        List<SkillEffectTask> t = this.taskList;
        if (null != t) {
            for (int e = 0; e < t.size(); e++) {
//            SkillEffectTask i = t.get(e);
//            t[e] = null;
                this.runner.popRun();
            }
            t.clear();
        }
//    this.timers?.clear();
        if (this.timers != null) {
            timers.clear();
        }
        if (this._timerId > 0) {
            this._timerId = 0;
            this.runner.resetPower();
        }
    }
}

