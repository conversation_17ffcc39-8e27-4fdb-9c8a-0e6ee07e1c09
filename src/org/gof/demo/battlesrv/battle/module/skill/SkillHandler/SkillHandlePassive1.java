package org.gof.demo.battlesrv.battle.module.skill.SkillHandler;

import org.gof.core.Port;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.support.Time;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.worldsrv.config.ConfSkill;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;

public class SkillHandlePassive1 extends SkillHandleBase {
    public SkillHandlePassive1(SkillRunner runner) {
        super(runner);
    }

    @Override
    public void beginRun() {
        SkillRunner e = this.runner;
        ConfSkill i = e.useSkill().config;
        if(i == null){
            Log.temp.error("====技能错误， e={}， i={}", e, e.useSkill().config);
            return;
        }
        if(i.buffGroup != null){
            for (int o = 0; o < i.buffGroup.length; o++) {
                int s = i.buffGroup.length == e.useSkill().skillDam.length ? 0 < e.useSkill().skillDam.length
                        ? e.useSkill().skillDam[o] : 0 : e.useSkill().skillDam.length > 0 ? e.useSkill().skillDam[0] : 0;
                float _s = s / 10000f;
                e.addBuff(e.cast(), i.buffGroup[o], -1, _s);
            }
        }

        if (i.immediate_time > 0) {
            this._timerId = this.addTimer(i.immediate_time / 1000f, 1, obj->{
                _skillEffect1();
                _skillEffect2();
            });
        }
        if (i.releaseTime > 0) {
            this._timerId = this.addTimer(i.releaseInterval, -1, obj->{
                _skillEffect1();
                _skillEffect2();
            });
        }
    }

}