package org.gof.demo.battlesrv.battle.module.skill.SkillHandler;

import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;

public class SkillHandleThiefNormal extends SkillHandleBase {
    public SkillHandleThiefNormal(SkillRunner runner) {
        super(runner);
    }

    @Override
    public void beginRun() {
        this.runner.changeModeAction(true, BattleParamKey.AniNames_Skill_1, 1);
    }
}