package org.gof.demo.battlesrv.battle.module.skill;

import org.gof.core.support.Utils;
import org.gof.core.support.function.GofFunction0;
import org.gof.core.support.function.GofFunction1;
import org.gof.demo.battlesrv.battle.bentity.ctroller.IntArryAction;
import org.gof.demo.battlesrv.battle.module.*;
import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.bentity.ctroller.IAction;
import org.gof.demo.battlesrv.battle.bentity.ctroller.SkillCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitCall;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.buff.Buff;
import org.gof.demo.battlesrv.battle.module.skill.SkillHandler.SkillHandleBase;
import org.gof.demo.battlesrv.battle.UnitMgr;
import org.gof.demo.battlesrv.battle.enumVo.*;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfAppearance;
import org.gof.demo.worldsrv.config.ConfAttribute;
import org.gof.demo.worldsrv.config.ConfBuff;
import org.gof.demo.worldsrv.config.ConfTrap;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.support.Log;

import java.util.*;
import java.util.function.Function;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-05-28 12:17
 **/

public class SkillRunner {
    public BattleMain battleMain;
    protected UnitHuman _owner;
    protected Skill _useSkill;
    protected GofFunction1<int[]> _skillAction;
    protected boolean _interrupt = false;
    protected int _run = 0;
    protected boolean triggerEndEvent = true;
    protected SkillHandleBase _skillHandle = null;
    protected UnitHuman _target = null;
    protected boolean _passive;
    protected List<Bullet> _bulletList;
    protected boolean _limitModelAction;
    protected List<Trap> _trapList;
    protected ConfAppearance _configWeapon;

    protected GofFunction1<int[]> _onModeActionEvent;
    protected GofFunction0 _onModeActionEnd;

    private List<String> T = new ArrayList<>(Arrays.asList("dizz", "ban_skil", "throw_hit", "bound", "ban_act"));

    public UnitHuman cast() {
        return _owner;
    }

    public UnitHuman getLockTarget() {
        return _target;
    }

    public void setLockTarget(UnitHuman target) {
        _target = target;
    }

    public Skill useSkill() {
        return _useSkill;
    }

    public boolean isNormalAct() {
        return _useSkill == _owner.data.attack;
    }

    public boolean isPassive() {
        return _passive;
    }

    public boolean isStop() {
        return _run <= 0;
    }

    public boolean isInterrupt() {
        return _interrupt;
    }

    public void set_interrupt(boolean value) {
        _interrupt = value;
    }

    public ConfAppearance configWeapon() {  return this._configWeapon;  }

    public SkillRunner() {
        this.triggerEndEvent = true;
        this._bulletList = new ArrayList<>();
        this._trapList = new ArrayList<>();
    }

    public void init(UnitHuman t, Skill i, boolean e) {// e = false
        _owner = t;
        _useSkill = i;
        this.battleMain = t.battleMain();
        this._limitModelAction = e;
    }

    public void start() {
        start(false);
    }

    public void start(boolean isPassive) {
        this._passive = isPassive;
        this.pushRun();
        Skill i = this._useSkill;

        if (this._owner.weapon() > 0)
            this._configWeapon = ConfAppearance.get(this._owner.weapon());
        else if (this._owner.config().apperanceID > 0)
            this._configWeapon = ConfAppearance.get(this._owner.config().apperanceID);

        this._skillHandle = ClassFactory.skillMap(i.config.action, this);

        if (this.isNormalAct()) {
            this.resetPower();
        } else {
            this.useSkill().state = 1;
            this.useSkill().currenPower = 0;
        }
        this._skillHandle.beginRun();
//        Log.battle.info("SkillRunner {} {}", _owner.data.unitId, i.config.sn);

        if (!this._passive) {
            UnitHuman n = this._owner;
            if (n.curTarget() != null) {
                this._target = n.curTarget().getUnit();
            }
            if (!this._limitModelAction) {
                _onModeActionEvent =this::onModeActionEvent;
                n.animatorctr.addActionListener(_onModeActionEvent);
                _onModeActionEnd = this::onModeActionEnd;
                n.animatorctr.addActionListenerEnd(_onModeActionEnd);
            }
        }
    }

    public void resetPower() {
        if (this.useSkill().resetCd) {
            this.useSkill().currenPower = this.useSkill().config.maxPower;
            this.useSkill().currenRelease = 0;
            this.useSkill().currenCd = 0;
            this.useSkill().checkState();
            this.useSkill().resetCd = false;
            return;
        }
        this.useSkill().state = 2;
        this.useSkill().currenPower = 0;
        this.useSkill().currenCd = 0;
    }

    public void onUpdate(float t) {
        if (this._run <= 0)
            return;
        List<Bullet> i = this._bulletList;
        List<Bullet> removeList = new ArrayList<>();
        for (int e = 0; e < i.size(); e++) {
            Bullet n = i.get(e);
            if (n.isStop()) {
                removeList.add(n);
                n.destroy();
//                i.RemoveAt(e);
//                e--;
                this.popRun();
            } else
                n.onUpdate(t);
        }
        i.removeAll(removeList);

        List<Trap> r = this._trapList;
        List<Trap> removeT = new ArrayList<>();
        for (int a = 0; a < r.size(); a++) {
            Trap s = r.get(a);
            if (s.isStop) {
                removeT.add(s);
//                r.RemoveAt(a);
//                a--;
                this.popRun();
            } else
                s.onUpdate(t);
        }
        r.removeAll(removeT);

        this._skillHandle.onUpdate(t);
        if (null != this._target && this._target.isDestroy())
            this._target = null;
    }

    public void stop() {
        if (this._run <= 0)
            return;
        this._run = 0;
    }

    public void destroy() {
        List<Bullet> e = this._bulletList;
        for (Bullet t : e) {
            t.destroy();
        }
        e.clear();
        List<Trap> a = this._trapList;
        a.clear();
        this._skillHandle.destroy();
    }

    public void interrupt() {
        if (this._interrupt)
            return;
        this.popRun();
        this._interrupt = true;

        if (null != this._owner && !this._passive) {
            UnitHuman t = this._owner;
            if (t.statectr.currentStateType() == StateType.Skill)
                t.statectr.lockCurrenState = false;

            if (!this._limitModelAction) {
                t.animatorctr.removeActionListener(_onModeActionEvent);
                t.animatorctr.removeActionEndListener(_onModeActionEnd);
            }
        }
    }


    protected void onModeActionEvent(int[] t) {
        if (null != this._skillAction)
            this._skillAction.apply(t);
//        Log.temp.info("execute _skillAction action: {} , skillActionList={}", Utils.arrayIntToStr(t), skillActionList.size());
    }

    protected void onModeActionEnd() {
        this._skillHandle.onModelEnd();

        if (this.triggerEndEvent)
            this.interrupt();
    }

    public void nextTriggerAction(GofFunction1<int[]> t) {
        this._skillAction= t;
//        Log.temp.info("set _skillAction action: {} , {}", t, skillActionList.size());
    }

    public void onBulletAction(Bullet t, UnitHuman i, Vector2D e, List<Object> n) {
        this._skillHandle.onBulletAction(t, i, e, n);
    }

    public void changeModeAction(boolean t, String i, float e)//e = 1
    {
        UnitHuman n = this._owner;
        this.triggerEndEvent = t;
        n.animatorctr.changeState(i, true, e);
    }

    public float getActSpeed(String t, int i)//i = 1
    {
        UnitHuman e = this._owner;
        int[][] n = e.animatorctr.config.getFieldValue(t);
        if (null == n)
            return 1;
        float a = (30f / n[0][0]);
        float s = (e.data.getAttrib(AttribDefine.att_speed) / a);
        s = (s * i);
        s = Math.max(s, 1);
        return s;
    }

    public List<UnitHuman> getTargets(TargetFilter t, float i, Vector2D e, int n, TargetSelectFilter r)//n = 1, r = TargetSelectFilter.NearTarget
    {
        UnitMgr a = this.battleMain.unitMgr;
        List<UnitHuman> s = a.findTarget(this._owner, t, i, e, true);
        return a.getTargetList(this._owner, s, n, r);
    }

    public void healthTarget(UnitHuman t, long e, HealthType n)
    {
        healthTarget(t, e, n, false);
    }
    public void healthTarget(UnitHuman t, long e, HealthType n, boolean s)// s = false
    {
        if (t.dead)
            return;
        UnitHuman l = this._owner;
        if (s) {
            t.hatredCtr.addHurtHatred(l, e);
            l.hatredCtr.addHurtHatred(t, e);
        }
        switch (n) {
            case Hurt:
            case Hurt_Crit:
            case Hurt_Share_Damage:
            case Hurt_Share_Damage_Crit:
            case Hurt_Double:
            case Hurt_Double_Crit:
            case Hurt_Counter:
            case Hurt_Counter_Crit:
                if (t.statectr.notGetDamage > 0)
                    return;
                if (Float.isNaN(e))
                    return;
                if (0 == e)
                    return;
                if (t.statectr.invincible > 0)
                    return;
                if (t.data.getBuffState(SpBuffState.DelayDamage) > 0) {
                    List<Buff> p = t.buffCtr.getBuffByType(BuffGroupType.DELAY_DEMAGE.getValue());
                    for (Buff c : p) {
                        c.onDamage(e);
                    }
                    return;
                }
                if (n != HealthType.Hurt_Share_Damage && n != HealthType.Hurt_Share_Damage_Crit) {
                    List<Buff> b = l.buffCtr.getBuffByType(BuffGroupType.SHARE_DAMAGE.getValue());
                    for (Buff y : b) {
                        y.onShareDamageAction(e, t, n);
                    }
                    if (b.size() > 0)
                        return;
                }
                if (l.config().type == UnitType.Boss.getValue()) {
                    double A = t.data.getAttrib(AttribDefine.boss_def);
                    e = Math.round(e * (1 - A));
                }
                if (this._useSkill == l.data.attack) {
                    List<SkillCtr.SkillEffect> m = t.skillctr.skillEffects;
                    for (SkillCtr.SkillEffect D : m) {
                        if (D.triggerType == EffectTriggerType.HIT.getValue()) {
                            D.num++;
                            if (D.limit <= 0 || D.num % D.limit == 0){
                                if (0 == D.useType)
                                    D.runner._skillHandle.addTask(D.id, this.cast().Position, D.runner, this.cast());
                                else if (1 == D.useType)
                                    t.skillctr.addSkill(D.id);
                            }
                        }
                    }
                }
                break;
            case Real_Damage:
            case Hurt_Bleed:
            case Hurt_Bleed_Crit:
                if (Float.isNaN(e))
                    return;
                if (0 == e)
                    return;
                if (t.statectr.invincible > 0)
                    return;
                break;
            case Miss:
            case Double_Act:
            case Counter_Act:
                if (n == HealthType.Miss) {
                    List<SkillCtr.SkillEffect> E = t.skillctr.skillEffects;
                    for (SkillCtr.SkillEffect H : E) {
                        if (H.triggerType == EffectTriggerType.Miss.getValue()) {
                            H.num++;
                            if (H.limit <= 0 || H.num % H.limit == 0) {
                                if (0 == H.useType)
                                    H.runner._skillHandle.addTask(H.id, t.Position, H.runner, t);
                                else if (1 == H.useType)
                                    t.skillctr.addSkill(H.id);
                            }
                        }
                    }
                }
                if (t.data.getBuffState(SpBuffState.StateTriger) > 0) {
                    List<Buff> E = t.buffCtr.getBuffByType(BuffGroupType.STATE_TRIGER.getValue());
                    for (Buff H : E) {
                        if (n == HealthType.Miss)
                            H.onStateTrigger(StateTrigerType.Miss, null);
                        else if (n == HealthType.Counter_Act)
                            H.onStateTrigger(StateTrigerType.Counter_Act, null);
                        else if (n == HealthType.Double_Act)
                            H.onStateTrigger(StateTrigerType.Double_Act, null);
                    }
                }
                break;
        }
        if (n != HealthType.Treat && n != HealthType.Treat_Crit && n != HealthType.Skill_Hpsteal && n != HealthType.Act_Hpsteal) {
            List<Buff> F = t.buffCtr.getBuffByType(BuffGroupType.DEFER_DAMAGE.getValue());
            if (!F.isEmpty()) {
                for (Buff P : F) {
                    e = P.calDamage(e, t, 0);
                }
            }
        }
        t.hit(l, e, n, this.useSkill().config.sn);
    }

    public boolean throwHit(UnitHuman t, float i, float e, float n) {
        int a = this.cast().Position.x > t.Position.x ? -1 : 1;
        return t.throwHit(i, e, Math.round(a * n));
    }

    public Buff addBuff(UnitHuman t, int e, float n, float a)//a = 0
    {
        if (t.dead)
            return null;
        ConfBuff u = ConfBuff.get(e);
        if (null == u) {
//            Console.WriteLine("技能：" + this.useSkill().config.sn + " BuffID：" + e + " 没找到!!");
            Log.temp.error("==技能：{}, buffId={} 没找到", this.useSkill().config.sn, e);
            return null;
        }
        List<Buff> l = t.buffCtr.getBuffByType(BuffGroupType.IGNORE_BUFFIDS.getValue());
        if (l.size() > 0) {
            if (l.get(0).config().param5.contains(String.valueOf(u.sn)))
                return null;
        }
        if (2 == u.mutex) {
            if (t.buffCtr.getBuff(e) != null)
                return null;
        }
        if (4 == u.mutex)
        {
            List<Buff> d = t.buffCtr.getBuffListById(e);
            for (Buff _ : d)
            {
                if (_ != null && _.runner.cast() == this.cast())
                    return null;
            }
        }
        if ((t.statectr.notControlled > 0 || t.statectr.invincible > 0) && T.contains(u.action))
            return null;
        Buff c = ClassFactory.buffMap(u.action, u);
        if(c.config() == null){
            Log.temp.error("==buffId={} 没找到, u.action={}, u={}, buffSn={}", e, u.action, u.sn);
            return null;
        }
        c.skillPar = a;
        if (("dizz".equals(u.action) && 0 == u.param1) || "ban_act".equals(u.action)) {
            float g = t.data.getAttrib(AttribDefine.CONTROL_RES);
            if (g > 0) {
                n = (n - (n * g));
            }
        }
        if ("shield".equals(u.action)) {
            float xhx = t.data.getAttrib(AttribDefine.shield_time_extra);
            if (xhx > 0) n = (n + xhx);
        }

        c.currenTime = n;
        c.runner = this;
        if (0 == c.config().type) {
            c.owner = t;
            c.start();
            c.destroy();
            return null;
        }

        List<Buff> v = t.buffCtr.getBuffListById(e);
        if (3 == c.config().mutex) {
            for (int y = 0; y < v.size(); y++)
                v.get(y).currenTime = n;
            int b = c.config().add_max;
            if (b > 0 && v.size() >= b) {
                for (Buff buff : v) {
                    if (buff.getRunState()) {
                        buff.stop();
                        break;
                    }
                }
            }
        }

        if (1 == c.config().mutex)
            t.buffCtr.stopBuffById(e);
        t.buffCtr.addBuff(c);
        if (this._owner.data.getBuffState(SpBuffState.AddBuffTrigger) > 0) {
            List<Buff> A = this._owner.buffCtr.getBuffByType(BuffGroupType.AddBuffTrigger.getValue());
            for (Buff S : A) {
                S.onAddBuffTrigger(t, c.config().sn, c.currenTime);
            }
        }
        return c;
    }

    public void addBuffByPos(Vector2D t, int i, float e, float n)//n = 0
    {
        ConfBuff r = ConfBuff.get(i);
        if (null == r)
            Log.temp.info("技能：" + this.useSkill().config.sn + " BuffID：" + i + " 没找到!!");
        if (0 != r.type)
            Log.temp.info("技能：" + this.useSkill().config.sn + " BuffID：" + i + " 必须为立即生效删除类型!!");
        Buff a = ClassFactory.buffMap(r.action, r);
        a.skillPar = n;
        a.currenTime = e;
        a.runner = this;
        a.position = t;
        a.start();
        a.destroy();
    }

    public void addTrap(int t, Vector2D i, float e)//e = 0
    {
        ConfTrap n = ConfTrap.get(t);
        if (null == n)
            Log.temp.info("技能：" + this.useSkill().config.sn + " 陷阱ID：" + t + " 没找到!!");
        Trap r = Trap.alloc(n, i, e);
        r.runner = this;
        this.pushRun();
        this._trapList.add(r);
    }

    public Bullet addBullet(UnitHuman t, int i, boolean e)//  e = true
    {
        if (null != t) {
            this.pushRun();
            Bullet n = Bullet.alloc(this, i, this.cast().getBindPos(BindType.bp_lead), t, e);
            this._bulletList.add(n);
            return n;
        }
        return null;
    }

    public Bullet addBullet1(UnitHuman t, Vector2D i, int e) {
        return addBullet1(t, i, e, true);
    }

    public Bullet addBullet1(UnitHuman t, Vector2D i, int e, boolean n)// n=true
    {
        if (null != t) {
            this.pushRun();
            Bullet r = Bullet.alloc(this, e, i, t, n);
            this._bulletList.add(r);
            return r;
        }
        return null;
    }

    public Bullet addBulletToPos(Vector2D to, Vector2D from, int e) {
        this.pushRun();
        Bullet n = Bullet.allocToPos(this, e, from, to);
        this._bulletList.add(n);
        return n;
    }

    public UnitCall addCallUnit(int t, Vector2D e, float n, float[][] a, float s, boolean o)// s = 0, boolean o = true
    {
        BattleMain u = this.battleMain;
        UnitCall l = null;
        if (0 == t) {
            UnitData c = new UnitData();
            c.setConfig(this.cast().config());
            c.roleId = this.cast().data.roleId;
            for (Integer sn : GlobalConfVal.propNameSnModule1Map.values()) {
                ConfAttribute g = ConfAttribute.get(sn);
//                this.cast().data.attribs.TryGetValue((AttribDefine)g.sn, out _t);
                MetaAttrib _t = this.cast().data.attribs.get(AttribDefine.fromValue(g.sn));


                MetaAttrib ma = new MetaAttrib(g, _t);
                c.attribs.put(AttribDefine.fromValue(g.sn), ma);
            }
            c.skillList = new ArrayList<>();
            l = u.unitMgr.addUnitImageCall(c, this.cast().teamId);
            l.tauntValue = 999999999999999L;
        } else
            l = u.unitMgr.addUnitCall(t, this.cast().teamId);
        int p = o ? u.random.Next(-50,50) : 0;
        e.y = (e.y + p);
        l.Position = e;
        l.direction = this.cast().direction;
        l.lifeTime = this.cast().data.getSkillFactAttrValue(n, this.useSkill().config.sn, AttribDefine.active_skillbuff_time.getValue());
        l.parent = this.cast();
        l.isCallType = true;
        if (null != a && a.length > 0) {
            for (float[] T : a) {
                AttribDefine k = (AttribDefine.fromValue(Utils.intValue(T[0])));
                l.data.attribs.get(k).setBaseValue((this.cast().data.attribs.get(k).getBaseValue() * (T[1] * (1 == T[2] ? s : 1))));
            }
        }
        l.data.currenHp = l.data.getAttribByInt(AttribDefine.hp);
        return l;
    }

    public void addTask(int t, Vector2D i, SkillRunner e, UnitHuman n) {
        this._skillHandle.addTask(t, i, e, n);
    }

    public void onBeHitAction(Object t, Object i, Object e) {
        //this._passive
    }

    public void onActTargetAction(Object t, Object i) {
        if (this._passive)
            this._skillHandle.onActTargetAction(t, i);
    }

    public void pushRun() {
        this._run++;
    }

    public void popRun() {
        this._run--;
    }


}