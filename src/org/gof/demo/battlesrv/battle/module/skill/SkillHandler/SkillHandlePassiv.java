package org.gof.demo.battlesrv.battle.module.skill.SkillHandler;

import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.worldsrv.config.ConfSkill;

import java.util.ArrayList;
import java.util.List;

public class SkillHandlePassiv extends SkillHandleBase {
    private List<SkillEffectTask> ptaskList = new ArrayList<>();;

    public SkillHandlePassiv(SkillRunner runner) {
        super(runner);
    }

    @Override
    public void beginRun() {
        SkillRunner t = this.runner;
        ConfSkill e = t.useSkill().config;
        int[] skillDam = t.useSkill().skillDam;
        int leng = 0;
        if(skillDam != null){
            leng = skillDam.length;
        }
        if (null != e.buffGroup ) {
            for (int o = 0; o < e.buffGroup.length; o++) {
                int i = e.buffGroup.length == leng ? 0 < leng ? skillDam[o] : 0 : leng > 0 ? skillDam[0] : 0;
                t.addBuff(t.cast(), e.buffGroup[o], -1, i / 10000f);
            }
        }
        int[] u = e.skillEffect1;
        if (null != u && 0 != u.length) {
            for (int p : u) {
                SkillEffectTask h = this.addTask(p, t.cast().Position, t, null);
                this.execSkillEffect(t.cast(), h);
                if (0 != h.totalTime) {
                    h.totalTime = -1;
                    this.ptaskList.add(h);
                }
            }
        }
    }


    @Override
    public void onUpdate(float t) {
        for (SkillEffectTask p : this.ptaskList) {
            p.pos = this.runner.cast().Position;
        }
        super.onUpdate(t);
    }

    @Override
    public void destroy() {
        super.destroy();
        this.ptaskList.clear();
    }

}