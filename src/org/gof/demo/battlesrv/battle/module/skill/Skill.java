package org.gof.demo.battlesrv.battle.module.skill;

import org.gof.demo.worldsrv.config.ConfSkill;

import java.util.List;

/**
 * @program: game2
 * @description:
 * @author: Mr.wugz
 * @create: 2024-05-28 12:17
 **/
public class Skill {
    public ConfSkill config;
    public int parentSkillId;
    public int level;
    public float currenCd;
    public int state;
    public int[] skillDam;
    public float currenPower;
    public float currenRelease;
    public float counterDamage = 1;
    public boolean resetCd;
    public float useDelay;
    public boolean triggerEffect = true;
    public List<int[]> skillEffectList;
    public List<int[]> skillDamList;

    public void checkState() {
        this.state = this.currenPower >= this.config.maxPower ? 0 : 2;
    }
}
