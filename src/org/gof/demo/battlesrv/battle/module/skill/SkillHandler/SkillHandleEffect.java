package org.gof.demo.battlesrv.battle.module.skill.SkillHandler;

import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.bentity.ctroller.SkillCtr;
import org.gof.demo.battlesrv.battle.module.buff.Buff;
import org.gof.demo.battlesrv.battle.module.buff.BuffUseSkillAdd;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.enumVo.BuffGroupType;
import org.gof.demo.battlesrv.battle.enumVo.EffectTriggerType;
import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.battlesrv.battle.enumVo.StateTrigerType;

import java.util.List;

public class SkillHandleEffect extends SkillHandleBase {
    public SkillHandleEffect(SkillRunner runner) {
        super(runner);
    }

    @Override
    public void beginRun() {
        SkillRunner e = this.runner;
        int t = e.useSkill().config.sn;
        e.cast().hurtNumCount++;
        e.cast().buffCtr.removeBuff(BuffGroupType.DESTROY_WHEN_SKILL_AFTER.getValue());
        this.execBeginAction();
        if (e.useSkill().triggerEffect) {
            List<SkillCtr.SkillEffect> s = e.cast().skillctr.skillEffects;
            for (SkillCtr.SkillEffect g : s) {
                if (g.triggerType == (int) EffectTriggerType.USE_SKILL.getValue() || g.triggerType == (int) EffectTriggerType.ALL_ATTACK.getValue()){
                    g.num++;
                    if (g.limit <= 0 || g.num % g.limit == 0){
                        String[] d = g.param5;
                        if (d != null && d.length > 0) {
                            boolean p = false;
                            for (int S = 0; S < d.length; S++) {
                                if (d[S].equals(Integer.toString(t))) {
                                    p = true;
                                    break;
                                }
                            }
                            if (!p)
                                continue;
                        }
                        if (0 == g.useType)
                            this.addTask(g.id, e.cast().Position, g.runner, e.cast());
                        else if (1 == g.useType)
                            e.cast().skillctr.addSkill(g.id);
                    }
                }
            }
        }
        List<Buff> T = e.cast().buffCtr.getBuffByType(BuffGroupType.USE_SKILL_ADD.getValue());
        for (Buff v : T) {
            if (!(v instanceof BuffUseSkillAdd)) {
                continue;
            }
            BuffUseSkillAdd E = (BuffUseSkillAdd) v;
            if (!(e.cast().skillctr.getRecordDamage(t) > E._limit) && E.skillList.contains(t)){
                long h = Math.min(FixRandom.roundInt(e.cast().skillctr.getRecordDamage(t) + E._value), E._limit);
                e.cast().skillctr.setRecordDamage(t, h);
            }
        }
        T.clear();
        if (e.useSkill().triggerEffect && e.cast().data.getBuffState(SpBuffState.StateTriger) > 0) {
            List<Buff> E = e.cast().buffCtr.getBuffByType(BuffGroupType.STATE_TRIGER.getValue());
            for (Buff B : E) {
                B.onStateTrigger(StateTrigerType.Skill_Effect, null);
            }
            E.clear();
        }
        this.runner.interrupt();
    }
}