package org.gof.demo.battlesrv.battle.module;

import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.msg.Define;

import java.util.*;

public class UnitData {
    public int unitId;
    public long roleId;
    public String name;
    public Define.p_head head;
    private ConfUnit _config;
    private ConfUnitModel _modelConfig;
    private ConfUnitType _unitType;
    private ConfSkin _skinConfig;
    public boolean skinChange;
    public int idleIndex = 1;
    private int intScaleSize;
    public int petId = 0;
    public int level = 0;
    public long currenHp = 0;
    public long initHp = 0;
    public long maxHp = 0;
    public long shieldHp = 0;
    public List<Integer> buffs = new ArrayList<>();
    public long totalDamage = 0;// 收到的伤害
    public long totalCauseDamage = 0;// 造成的伤害
    public boolean immuneDeath;
    public boolean remakeLock;
    private Map<SpBuffState, Integer> _buffStateCounter;
    public Map<AttribDefine, MetaAttrib> attribs;
    public Skill attack;
    public Skill counterAttack;
    public List<Define.p_active_skill> ativeSkills;
    public List<Skill> skillList;
    public List<Skill> passiveSkillList;
    public List<Skill> petPassiveSkillList;
    public List<Skill> flyPetPassiveSkillList;
    public Map<Integer, Map<Long, Long>> skillAttrInfo;  /// role_total_sp_info_s2c.p_attr_obj.attrList
    public int mount = 0;

    public float warningRange() {
        return this.attribs.get(AttribDefine.detection_range).getValue();
    }

    public ConfUnit config() {
        return this._config;
    }

    public void setConfig(ConfUnit value) {
        if (this._config == value)
            return;
        this._config = value;
        this._unitType = ConfUnitType.get(value.type);
        this._modelConfig = ConfUnitModel.get(value.model);
    }

    public ConfUnitType unitType() {
        return this._unitType;
    }

    public ConfUnitModel modelConfig() {
        return this._modelConfig;
    }

    public ConfSkin skinConfig() {
        return this._skinConfig;
    }
    public int weapon;

    public UnitData() {
        _buffStateCounter = new HashMap<>();
        attribs = new HashMap<>();
    }

    public void AddBuffState(SpBuffState state, int num) {
        if (!_buffStateCounter.containsKey(state))
            _buffStateCounter.put(state, 0);
        _buffStateCounter.put(state, _buffStateCounter.get(state) + num);
    }

    public int getBuffState(SpBuffState state) {
        if (_buffStateCounter.containsKey(state))
            return _buffStateCounter.get(state);
        return 0;
    }

    public Skill getSkill(int id) {
        for (Skill skill : skillList) {
            if (skill.config.sn == id)
                return skill;
        }
        return null;
    }

    public float getAttrib(AttribDefine attrib) {
        if (attribs.containsKey(attrib))
            return this.attribs.get(attrib).getValue();
        return 0;
    }

    public MetaAttrib getAttribMeta(AttribDefine attrib) {
        if (!attribs.containsKey(attrib)){
            this.attribs.put(attrib, new MetaAttrib(ConfAttribute.get(attrib.ordinal()),null));
        }
        return this.attribs.get(attrib);
    }

    public long getAttribByInt(AttribDefine attrib) {
        return Math.round((double) this.attribs.get(attrib).getValue());
    }

    public Map<Long, Long> getSkillAttrInfoByCfgId(int id) {
        if (skillAttrInfo != null && skillAttrInfo.containsKey(id))
            return this.skillAttrInfo.get(id);
        return null;
    }

    public long getSkillAttrByAttrId(int id, long attrib) {
        Map<Long, Long> attr = getSkillAttrInfoByCfgId(id);
        if (attr != null && attr.containsKey(attrib)) {
            return attr.get(attrib);
        }
        return 0;
    }

    public float getSkillFactAttrValue(float value, int skillId, int attrId) {
        if (-1 == value && AttribDefine.active_skillbuff_time.getValue() == attrId)
            return value;
        float n = value + this.getSkillAttrByAttrId(skillId, attrId) + this.getSkillAttrByAttrId(0, attrId);
        List<ConfAttribute> r = ConfAttribute.findBy(ConfAttribute.K.group, attrId);
        if (r.isEmpty()) {
            return n;
        }

        for (ConfAttribute u : r) {
            float s = this.getSkillAttrByAttrId(skillId, u.sn) + this.getSkillAttrByAttrId(0, u.sn);
            n =  FixRandom.round(n * (1 +  FixRandom.round(s / 10000f)));
        }
        if (AttribDefine.active_skillbuff_time.getValue() == attrId)
            n += FixRandom.round(value * this.getAttrib(AttribDefine.skillbuff_time_all));
        return n;
    }

    public void setDress(int job, int skin, int gender)
    {
        ConfJobs o = ConfJobs.get(job);
        if (skin > 0)
        {
            for (int[] r : o.fashion)
            {
                if (r[0] == skin)
                {
                    int f = r[gender + 1];
                    this._modelConfig = ConfUnitModel.get(f);
                    _skinConfig = ConfSkin.get(skin);
                    return;
                }
            }
        }
        this._modelConfig = ConfUnitModel.get(this._config.model);
    }
}

