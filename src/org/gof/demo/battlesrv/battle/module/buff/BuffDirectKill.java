package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.HealthType;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffDirectKill extends Buff {
    public int _calType;
    public float _value;

    public static BuffDirectKill alloc(ConfBuff o) {
        BuffDirectKill t = new BuffDirectKill();
        t.setConfig(o);
        t._calType = o.param1;
        t._value = o.param2;
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        boolean t = false;
        long n = this.owner.data.getAttribByInt(AttribDefine.hp);
        if (0 == this._calType) {
            float e = this.owner.data.currenHp;
            if (e / n <= this._value * this.skillPar / 10000)
                t = true;
        }

        if (t)
            this.runner.healthTarget(this.owner, n, HealthType.Hurt_Crit, false);
    }
}
