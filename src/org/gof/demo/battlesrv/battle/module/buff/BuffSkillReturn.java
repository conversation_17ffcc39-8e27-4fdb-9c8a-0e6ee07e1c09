package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.ArrayList;
import java.util.List;

public class BuffSkillReturn extends Buff {

    protected List<Integer> _checkSkillList;
    protected buff[] _buffListStart;
    protected buff[] _buffListEnd;
    protected List<Buff> _buffs;

    public static class buff {
        public int id;
        public float time;
        public int par;
    }

    public static BuffSkillReturn alloc(ConfBuff t) {
        BuffSkillReturn a = new BuffSkillReturn();
        a.setConfig(t);
        String[] param = Utils.strToStrArray(t.param5, "\\|");
        a._checkSkillList = Utils.strToIntList(param[0]);
        a._buffListStart = GetBuffs( Utils.strToStrArray(param[1], "\\,"), 2);
        a._buffListEnd = GetBuffs( Utils.strToStrArray(param[2], "\\,"), 3);
        return a;
    }

    protected static buff[] GetBuffs(String[] str, int len) {
        buff[] buffs = new buff[str.length / len];
        for (int i = 0; i < buffs.length; i++) {
            buff buff = new buff();
            if (len == 2) {
                buff.id = Integer.parseInt(str[i * 2]);
                buff.par = Integer.parseInt(str[i * 2 + 1]);
            } else {
                buff.id = Integer.parseInt(str[i * len]);
                buff.time = Float.parseFloat(str[i * len + 1]);
                buff.par = Integer.parseInt(str[i * len + 2]);
            }
            buffs[i] = buff;
        }
        return buffs;
    }

    @Override
    protected void onBegin() {
        if (this._buffs == null) this._buffs = new ArrayList<>();
        for (buff r : this._buffListStart) {
            this._buffs.add(this.runner.addBuff(this.owner, r.id, -1, r.par / 10000f));
        }
    }

    @Override
    public boolean checkSkillReturn(UnitHuman t, SkillRunner n) {
        if (!this._run)
            return false;
        boolean e = false;
        if (t != this.owner && this.checkSkillCanReturn(n.useSkill()))
        {
            this.owner.addReturnInfo(t, n.useSkill().config.sn, n.useSkill().level);
            n.interrupt();
            e = true;
        }
        return e;
    }

    private boolean checkSkillCanReturn(Skill t) {
        if (this._checkSkillList.contains(t.config.sn)) {
            for (buff r : this._buffListEnd){
                this.runner.addBuff(this.owner, r.id, r.time, r.par / 10000f);
            }
            for (Buff o : this._buffs){
                o.stop();
            }
            this._buffs.clear();
            this.stop();
            return true;
        }
        return false;
    }

    @Override
    protected void onDestroy() {
        for (Buff t : this._buffs) {
            t.stop();
        }
        this._buffs.clear();
    }
}
