package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.HealthType;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;
import org.gof.demo.battlesrv.battle.enumVo.TargetSelectFilter;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffShareDamage extends Buff {
    public int _calType;
    public float _calPercent;

    public static BuffShareDamage alloc(ConfBuff o) {
        BuffShareDamage t = new BuffShareDamage();
        t.setConfig(o);
        t._calType = o.param1;
        t._calPercent = o.param2 / 10000;
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    public float onShareDamageAction(long t, UnitHuman e, HealthType r) {
        if (this.runner == null)
            return 0;
        int[] n = Utils.strToIntArray(this.config().param5);//this.config.param5.ToInts();
        List<UnitHuman> o = this.runner.getTargets(TargetFilter.Enemy, n[1], e.Position, n[0], TargetSelectFilter.NearTarget);
        long u = 0 == this._calType ? t : Math.round((double) t * this._calPercent * this.skillPar);
        HealthType l = HealthType.Hurt_Share_Damage;
        if (r == HealthType.Hurt_Crit || r == HealthType.Hurt_Double_Crit)
            l = HealthType.Hurt_Share_Damage_Crit;
        for (int s = 0; s < o.size(); s++) {
            UnitHuman f = o.get(s);
            if (1 != this._calType || e != f)
                this.runner.healthTarget(f, u, l, false);
            else
                this.runner.healthTarget(f, t + u, l, false);
        }
        return 0;
    }
}