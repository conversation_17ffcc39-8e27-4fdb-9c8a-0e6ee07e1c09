package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.enumVo.BuffGroupType;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffClear extends Buff {
    public int[] buffs;

    public static BuffClear alloc(ConfBuff o) {
        BuffClear t = new BuffClear();
        t.setConfig(o);
        t.buffs = Utils.strToIntArray(o.param5);//o.param5.ToInts();
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        for (int item : buffs) {
            this.owner.buffCtr.removeBuff(item);
        }
    }
}
