package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.module.MetaAttrib;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffAttribConvert extends Buff {
    private int _calType;
    private AttribDefine _attribId;
    private AttribDefine _tagAttribId;
    private long _lastValue;

    public static BuffAttribConvert alloc(ConfBuff o) {
        BuffAttribConvert t = new BuffAttribConvert();
        t.setConfig(o);
        t._calType = o.param1;
        t._attribId = AttribDefine.fromValue((int) o.param2);
        t._tagAttribId = AttribDefine.fromValue((int) o.param3);
        return t;
    }

    public BuffAttribConvert(ConfBuff o) {
        setConfig(o);
        _calType = o.param1;
        _attribId = AttribDefine.fromValue((int) o.param2);
        _tagAttribId = AttribDefine.fromValue((int) o.param3);
    }

    public BuffAttribConvert() {
    }

    @Override
    protected void onBegin() {
        this._lastValue = this._calValue();
        MetaAttrib a = this.owner.data.getAttribMeta(this._tagAttribId);
        a.addValue(this._lastValue);
    }

    private long _calValue() {
        long a = 0;
        switch (this._calType) {
            case 0:
                MetaAttrib e = this.owner.data.getAttribMeta(this._attribId);
                if (2 == e.config.num_type)
                    a = Math.round(10000D * e.getValue());
                break;
            case 1:
                a = this.owner.data.currenHp;
                break;
        }
        return Math.round(a * (double)this.skillPar);
    }

    @Override
    protected void onDestroy() {
        MetaAttrib t = this.owner.data.getAttribMeta(this._tagAttribId);
        t.addValue(-this._lastValue);
    }

}
