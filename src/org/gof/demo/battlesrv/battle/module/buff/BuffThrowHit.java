package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffThrowHit extends Buff {
    public static BuffThrowHit alloc(ConfBuff o) {
        BuffThrowHit t = new BuffThrowHit();
        t.setConfig(o);
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        int t = this.runner.cast().Position.x > this.owner.Position.x ? -1 : 1;
        float o = this.config().param2;
        this.owner.throwHit(this.config().param1, this.currenTime, Math.round(t * o));
    }
}
