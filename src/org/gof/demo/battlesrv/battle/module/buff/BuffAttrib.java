package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.module.MetaAttrib;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffAttrib extends Buff {
    private AttribDefine _id;
    private float _value;
    private boolean _isMultiples;
    private float _lastValue;
    private ConfBuff config;

    public static BuffAttrib alloc(ConfBuff t) {
        BuffAttrib i = new BuffAttrib();
        i.config = t;
        i.setConfig(t);
        i._id = AttribDefine.fromValue(t.param1);
        i._isMultiples = t.param2 == 2;
        i._value = t.param3;
        return i;
    }

    @Override
    protected void onBegin() {
        MetaAttrib t = this.owner.data.getAttribMeta(this._id);
        this._lastValue = this._value * this.runner.cast().data.getSkillFactAttrValue(this.skillPar, this.runner.useSkill().config.sn, 1044);
        if (this._isMultiples) {
            t.addMultiples(this._lastValue);
        } else {
            t.addValue(this._lastValue);
        }
    }

    @Override
    protected void onDestroy() {
        // 同样的假设
        MetaAttrib t = this.owner.data.getAttribMeta(this._id);
        if (this._isMultiples) {
            t.addMultiples(-this._lastValue);
        } else {
            t.addValue(-this._lastValue);
        }
    }
}
