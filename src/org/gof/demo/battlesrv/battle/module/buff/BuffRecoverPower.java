package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffRecoverPower extends Buff {
    public int _calType;
    public float _value;

    public static BuffRecoverPower alloc(ConfBuff o) {
        BuffRecoverPower t = new BuffRecoverPower();
        t.setConfig(o);
        t._calType = o.param1;
        t._value = o.param2;
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        float o = 0 == this._calType ? this._value * this.skillPar : 0;
        List<Skill> e = this.runner.cast().data.skillList;
        for (int r = 0; r < e.size(); r++) {
            Skill t = e.get(r);
            t.currenPower = Math.min(Math.round(t.currenPower + o), t.config.maxPower);
        }
    }
}
