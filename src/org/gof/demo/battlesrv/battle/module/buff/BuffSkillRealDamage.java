package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.bentity.ctroller.SkillCtr;
import org.gof.demo.battlesrv.battle.enumVo.BattleFlag;
import org.gof.demo.battlesrv.battle.enumVo.BuffGroupType;
import org.gof.demo.battlesrv.battle.enumVo.EffectTriggerType;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffSkillRealDamage extends Buff {
    private int _calType;

    public static BuffSkillRealDamage alloc(ConfBuff t) {
        BuffSkillRealDamage i = new BuffSkillRealDamage();
        i.setConfig(t);
        i._calType =t.param1;
        return i;
    }

    @Override
    public long calDamage(long e, UnitHuman t, int r) {
        long c = 0L;
        UnitHuman p = this.owner;
        Skill g = this.runner.useSkill();
        switch (this._calType)
        {
            case 1:
                if ((t.battleMain.battleFlag&BattleFlag.NOT_HURT.getValue())>0)
                    return 0;
                float v = g.skillDam.length > 0 ? g.skillDam[0] / 1000f : 1;
                c = FixRandom.roundInt(e * v);
                break;
        }
        List<Buff> y = t.buffCtr.getBuffByType(BuffGroupType.VAMPIRE.getValue());
        for (Buff h : y)
            h.calDamage(c, t, 0);
        List<SkillCtr.SkillEffect> T = p.skillctr.skillEffects;
        for (int _i = 0; _i < T.size(); _i++)
        {
            SkillCtr.SkillEffect k = T.get(_i);
            if (k.triggerType == EffectTriggerType.HP_Hurt.getValue())
            {
                k.num++;
                if (k.limit <= 0 || k.num % k.limit == 0)
                {
                    if (0 == k.useType)
                        this.runner.addTask(k.id, p.Position, k.runner, p);
                    else if (1 == k.useType)
                        p.skillctr.addSkill(k.id);
                }
            }
        }
        return c;
    }
}