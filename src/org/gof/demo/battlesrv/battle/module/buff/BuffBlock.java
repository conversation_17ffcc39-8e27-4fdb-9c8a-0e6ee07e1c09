package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffBlock extends Buff {
    public int _blockEffect;
    public int[][] n;

    public static BuffBlock alloc(ConfBuff o) {
        BuffBlock t = new BuffBlock();
        t.setConfig(o);
        t._blockEffect = (int) o.param2;
        t.n = Utils.parseIntArray2(t.config().param5);
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    public long onShieldAction(long t) {
        if (!this._run)
            return 0;
        long o = Math.round(t / this.owner.data.getAttrib(AttribDefine.hp) * 10000D);
        for (int r = n.length - 1; r >= 0; r--) {
            int[] c = n[r];
            if (o >= c[0])
                return Math.round(c[1] / 10000D * t);
        }
        return 0L;
    }
}
