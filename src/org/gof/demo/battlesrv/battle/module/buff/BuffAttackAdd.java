package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffAttackAdd extends Buff {
    public static final int Type_AttNum = 1;
    public static final int Type_Attack = 4;
    public static final int Type_Target = 8;

    private int _addType;
    private int _calType;
    private int _attribId;
    private int _limit;

    public static BuffAttackAdd alloc(ConfBuff t) {
        BuffAttackAdd a = new BuffAttackAdd();
        a.setConfig(t);
        a._addType = t.param1;
        a._calType = (int) t.param2;
        a._attribId = (int) t.param3;
        a._limit = (int) t.param4;
        return a;
    }

    public long calValue(int t) {
        if ((this._addType & Type_AttNum) > 0 && t % this._limit != 0)
            return 0;
        UnitHuman a = this.owner;
        UnitHuman r = this.runner.cast();
        long i = 0L;
        switch (this._calType) {
            case 0:
                i = (this._addType & Type_Target) > 0 ? a.data.getAttribByInt((AttribDefine.fromValue(this._attribId)))
                        : r.data.getAttribByInt((AttribDefine.fromValue(this._attribId)));
                break;
            case 1:
                float n = r.data.getAttrib(AttribDefine.att);
                float u = a.data.getAttrib(AttribDefine.def);
                float s = a.data.getAttrib(AttribDefine.def_coe);
                i = Math.max(FixRandom.roundInt(n - u * (1 + s)), 1);
                break;
            case 2:
                long d = r.data.getAttribByInt(AttribDefine.hp);
                i = d - a.data.currenHp;
                break;
            case 3:
                i = a.data.currenHp;
                break;
        }
        return FixRandom.roundInt(i * this.skillPar);
    }
}
