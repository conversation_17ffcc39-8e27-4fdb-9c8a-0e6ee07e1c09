package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.HurtUtil;
import org.gof.demo.battlesrv.battle.bentity.ctroller.SkillCtr;
import org.gof.demo.battlesrv.battle.enumVo.*;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffBleed extends Buff {
    protected int _type;
    public static BuffBleed alloc(ConfBuff o) {
        BuffBleed t = new BuffBleed();
        t.setConfig(o);
        t._type = o.param1;
        return t;
    }

    @Override
    protected void onBegin() {
        UnitHuman t = this.owner;
        UnitHuman r = this.runner.cast();
        if ((t.battleMain().battleFlag & BattleFlag.NOT_HURT.getValue()) == 0) {
            float e = r.data.getAttrib(AttribDefine.att);
            float i = t.data.getAttrib(AttribDefine.def);
            float o = t.data.getAttrib(AttribDefine.def_coe);
            float m = r.data.getAttrib(AttribDefine.att_dam);
            float k = r.data.getAttrib(AttribDefine.boss_dam);
            long v = 0L;
            long A = 0L;
            HealthType y = HealthType.Hurt_Bleed;
            switch (this._type)
            {
                case 0:
                    v = Math.max(FixRandom.roundInt(e - i * (1 + o)), 1);
                    v = FixRandom.roundInt(v * m);
                    v = FixRandom.roundInt(v * r.data.getSkillFactAttrValue(this.skillPar, this.runner.useSkill().config.sn, AttribDefine.active_skilldamage_par.getValue()));
                    if (this.owner.config().type == UnitType.Boss.getValue() && k > 0)
                        v = FixRandom.roundInt(v * (1 + k));
                    A = HurtUtil.calHurt(v, t, r);
                    break;
                case 1:
                    float g = this.owner.data.currenHp;
                    v = FixRandom.roundInt(g * this.skillPar);
                    A = v = FixRandom.roundInt(v * t.battleMain.injuryReduce);
                    break;
                case 2:
                    v = Math.max(FixRandom.roundInt(e - i * (1 + o)), 1);
                    float I = r.data.getAttrib(AttribDefine.skill_dam_extra);
                    v = FixRandom.roundInt(v * I * this.skillPar);
                    float M = t.data.getAttrib(AttribDefine.skill_resist);
                    v = HurtUtil.calHurt(FixRandom.roundInt(v * Math.max(0, FixRandom.round(1 - M))), t, r);
                    if (HurtUtil.checkSkillCirt(r))
                    {
                        float x = r.data.getAttrib(AttribDefine.skill_crit_dam);
                        v = FixRandom.roundInt(v * FixRandom.round(1 + x));
                        v = FixRandom.roundInt((float)Math.pow(v, .98));
                        y = HealthType.Hurt_Bleed_Crit;
                    }
                    A = v;
                    break;
                case 3:
                    v = Math.max(FixRandom.roundInt(e - i * (1 + o)), 1);
                    v = FixRandom.roundInt(v * m);
                    v = FixRandom.roundInt(v * r.data.getSkillFactAttrValue(this.skillPar, this.runner.useSkill().config.sn, AttribDefine.active_skilldamage_par.getValue()));
                    if (this.owner.config().type == UnitType.Boss.getValue() && k > 0)
                        v = FixRandom.roundInt(v * (1 + k));
                    float H = t.data.getAttrib(AttribDefine.att_resist);
                    A = HurtUtil.calHurt(FixRandom.roundInt(v * Math.max(0, 1 - H)), t, r);
                    if (HurtUtil.checkHit(r, t) == AttackType.Cirt)
                    {
                        float T = r.data.getAttrib(AttribDefine.crit_dam);
                        float C = (float)Math.max(.5f, t.data.getAttrib(AttribDefine.crit_def));
                        A = FixRandom.roundInt(A * (float)Math.max(1.5f, FixRandom.round(T / C)));
                        y = HealthType.Hurt_Bleed_Crit;
                    }
                    break;
                case 4:
                    float S = r.data.getAttrib(AttribDefine.double_hit_dam);
                    v = Math.max(FixRandom.roundInt(FixRandom.roundInt(e - i * (1 + o)) * S), 1);
                    v = FixRandom.roundInt(v * r.data.getSkillFactAttrValue(this.skillPar, this.runner.useSkill().config.sn,AttribDefine.active_skilldamage_par.getValue()));
                    if (this.owner.config().type == UnitType.Boss.getValue() && k > 0)
                        v = FixRandom.roundInt(v * FixRandom.round(1 + k));
                    float F = t.data.getAttrib(AttribDefine.double_hit_def);
                    A = HurtUtil.calHurt(FixRandom.roundInt(v * Math.max(0, 1 - F)), t, r);
                    if (HurtUtil.checkHit(r, t) == AttackType.Cirt)
                    {
                        float P = r.data.getAttrib(AttribDefine.crit_dam);
                        float w = (float)Math.max(.5, t.data.getAttrib(AttribDefine.crit_def));
                        A = FixRandom.roundInt(A * (float)Math.max(1.5, FixRandom.round(P / w)));
                        y = HealthType.Hurt_Bleed_Crit;
                    }
                    break;
                case 5:
                    float E = r.data.getAttrib(AttribDefine.counter_dam);
                    v = Math.max(FixRandom.roundInt(FixRandom.roundInt(e - i * (1 + o)) * E), 1);
                    v = FixRandom.roundInt(v * r.data.getSkillFactAttrValue(this.skillPar, this.runner.useSkill().config.sn, AttribDefine.active_skilldamage_par.getValue()));
                    if (this.owner.config().type == UnitType.Boss.getValue() && k > 0)
                        v = FixRandom.roundInt(v * FixRandom.round(1 + k));
                    float R = t.data.getAttrib(AttribDefine.counter_def);
                    A = HurtUtil.calHurt(FixRandom.roundInt(v * Math.max(0, 1 - R)), t, r);
                    if (HurtUtil.checkHit(r, t) == AttackType.Cirt)
                    {
                        float V = r.data.getAttrib(AttribDefine.crit_dam);
                        float D = (float)Math.max(.5f, t.data.getAttrib(AttribDefine.crit_def));
                        A = FixRandom.roundInt(A * (float)Math.max(1.5, FixRandom.round(V / D)));
                        y = HealthType.Hurt_Bleed_Crit;
                    }
                    break;
            }
            if (0 == this._type)
            {
                List<Buff> L = r.buffCtr.getBuffByType(BuffGroupType.GIANT_SLAYER.getValue());
                for(Buff j : L)
                    A = j.onCalHpDamage(t, A);
            }
            this.runner.healthTarget(t, A, y, true);
            List<Buff> U = t.buffCtr.getBuffByType(BuffGroupType.VAMPIRE.getValue());
            for(Buff G : U)
                G.calDamage(A, t, 0);
            List<SkillCtr.SkillEffect> z = t.skillctr.skillEffects;
            for (int _i = 0; _i < z.size(); _i++)
            {
                SkillCtr.SkillEffect W = z.get(_i);
                if (W.triggerType == EffectTriggerType.HP_Hurt.getValue())
                {
                    W.num++;
                    if (W.limit <= 0 || W.num % W.limit == 0)
                    {
                        if (0 == W.useType)
                            this.runner.addTask(W.id, t.Position, W.runner, t);
                        else if (1 == W.useType)
                            t.skillctr.addSkill(W.id);
                    }
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
    }
}
