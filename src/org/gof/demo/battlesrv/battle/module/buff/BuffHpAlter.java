package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffHpAlter extends Buff {

    public float _tarPercent;

    public static BuffHpAlter alloc(ConfBuff o) {
        BuffHpAlter t = new BuffHpAlter();
        t.setConfig(o);
        t._tarPercent = o.param1;
        return t;
    }

    @Override
    protected void onBegin() {
        long t = FixRandom.roundInt(this.owner.data.getAttrib(AttribDefine.hp) * this._tarPercent / 10000);
        this.owner.data.currenHp = t;
    }
}
