package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffBanSkill extends Buff {
    public static BuffBanSkill alloc(ConfBuff o) {
        BuffBanSkill t = new BuffBanSkill();
        t.setConfig(o);
        return t;
    }

    public BuffBanSkill() {

    }

    public BuffBanSkill(ConfBuff o) {
        setConfig(o);
    }

    @Override
    protected void onBegin() {
        this.owner.statectr.banSkill++;
        super.onBegin();
    }

    @Override
    protected void onDestroy() {
        this.owner.statectr.banSkill--;
    }
}
