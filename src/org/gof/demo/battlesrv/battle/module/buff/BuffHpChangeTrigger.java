package org.gof.demo.battlesrv.battle.module.buff;

import io.netty.util.internal.StringUtil;
import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffHpChangeTrigger extends Buff {
    public int _operator;
    public float triggervalue;
    public int _addBuffid;
    public float _addlasttime;
    public float _cd;
    public float _checktime;

    public static BuffHpChangeTrigger alloc(ConfBuff o) {
        BuffHpChangeTrigger t = new BuffHpChangeTrigger();
        t.setConfig(o);
        t._operator = o.param1;
        t.triggervalue = o.param2;
        t._addBuffid = (int) o.param3;
        t._addlasttime = o.param4;
        t._cd = StringUtil.isNullOrEmpty(o.param5) ? t._addlasttime : Utils.floatValue(o.param5);//string.IsNullOrEmpty(o.param5) ? t._addlasttime : o.param5.ToFloat();
        t._checktime = 0;
        return t;
    }

    @Override
    protected void onDestroy() {
        this.owner.data.AddBuffState(SpBuffState.HpChangeTriger, -1);
    }

    @Override
    protected void onBegin() {
        this.owner.data.AddBuffState(SpBuffState.HpChangeTriger, 1);
    }

    @Override
    public void onDamage(float t) {
        float e = t / this.owner.data.getAttrib(AttribDefine.hp);
        float i = this.triggervalue / 10000f;
        boolean a = false;
        switch (this._operator) {
            case 0:
                a = e > i;
                break;
            case 1:
                a = e < i;
                break;
            case 2:
                a = e == i;
                break;
        }
        if (a && this._checktime <= 0) {
            this._checktime = this._cd;
            this.runner.addBuff(this.owner, this._addBuffid, this._addlasttime, this.skillPar);
        }
    }

    @Override
    protected void onUpdate(float t) {
        if (this._checktime > 0)
            this._checktime = this._checktime - t;
    }
}
