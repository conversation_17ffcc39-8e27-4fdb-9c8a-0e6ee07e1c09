package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.ArrayList;
import java.util.List;

public class BuffPauseCd extends Buff {
    public int _num;
    public List<Skill> _skills = new ArrayList<>();
    public int _sortType;

    public static BuffPauseCd alloc(ConfBuff o) {
        BuffPauseCd t = new BuffPauseCd();
        t.setConfig(o);
        t._num = o.param1;
        t._sortType = (int) o.param2;
        return t;
    }

    @Override
    protected void onDestroy() {
        this._num = 0;
        for (Skill r : _skills) {
            if (3 == r.state)
                r.checkState();
        }
        this._skills.clear();
    }

    @Override
    protected void onBegin() {
        UnitHuman t = this.owner;
        if (t.data.skillList != null && 0 != t.data.skillList.size()) {
            List<Skill> e = t.data.skillList;
            int n = 0;
            for (int o = 0; o < e.size() && n < this._num; o++) {
                this.runner.battleMain.random.Next(0, e.size());
                Skill r = e.get(o);
                if (0 != r.state && 1 != r.state) {
                    r.state = 3;
                    this._skills.add(r);
                    n++;
                }
            }
        }
    }
}
