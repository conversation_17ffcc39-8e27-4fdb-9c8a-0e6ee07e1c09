package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffSpeedRandom extends Buff {

    private int _calType;
    private int _max;
    private int _buffId;
    private float _buffTime;
    private int[] _limit;

    public static BuffSpeedRandom alloc(ConfBuff o) {
        BuffSpeedRandom t = new BuffSpeedRandom();
        t.setConfig(o);
        t._calType = o.param1;
        t._max = (int)o.param2;
        t._buffId = (int)o.param3;
        t._buffTime = o.param4;
        t._limit = Utils.strToIntArray(o.param5);
        return t;
    }

    @Override
    protected void onBegin() {
        this.onCheck();
    }

    private void onCheck()
    {
        float t = 0f;
        if (1 == this._calType)
        {
            float i = this.owner.data.getAttrib(AttribDefine.speed);
            float n = this.owner.data.getAttribMeta(AttribDefine.speed).getBaseValue();
            t = FixRandom.round(Math.max(0, n - i) / n);
        }
        float e = Math.min(FixRandom.round(this._max * t + this._limit[0]), this._limit[1]);
        if (this.owner.battleMain.random.randomInt(0, 10000) <= e)
            this.runner.addBuff(this.owner, this._buffId, this._buffTime, this.skillPar);
    }
}
