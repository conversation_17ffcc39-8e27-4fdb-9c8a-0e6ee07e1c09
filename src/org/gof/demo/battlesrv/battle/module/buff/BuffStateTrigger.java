package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.battlesrv.battle.enumVo.StateTrigerType;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.ArrayList;
import java.util.List;

public class BuffStateTrigger extends Buff {
    public int _statebuff;
    public int _triggercount;
    public float _addlasttime;
    public int[] _addbufflist;
    public float _currentTriggerValue;

    public static BuffStateTrigger alloc(ConfBuff o) {
        BuffStateTrigger t = new BuffStateTrigger();
        t.setConfig(o);
        t._statebuff = o.param1;
        t._triggercount = (int) o.param2;
        t._addlasttime = (int) o.param3;
        t._addbufflist = Utils.strToIntArray(o.param5); //o.param5.ToInts();
        t._currentTriggerValue = 0;
        return t;
    }

    @Override
    protected void onDestroy() {
        this.owner.data.AddBuffState(SpBuffState.StateTriger, -1);
    }

    @Override
    protected void onBegin() {
        this.owner.data.AddBuffState(SpBuffState.StateTriger, 1);
        super.onBegin();
    }

    @Override
    public List<Buff> onStateTrigger(StateTrigerType t, UnitHuman i)// i = null
    {
        List<Buff> r = new ArrayList<>();
        if (t == (StateTrigerType.fromValue(this._statebuff)) || this._statebuff == (int) StateTrigerType.All.getValue())
            this._currentTriggerValue++;

        if (this._currentTriggerValue >= this._triggercount) {
            this._currentTriggerValue = 0;
            for (int e = 0; e < this._addbufflist.length; e++)
                r.add(this.runner.addBuff(this.owner, this._addbufflist[e], this._addlasttime, this.skillPar));
        }
        return r;
    }
}
