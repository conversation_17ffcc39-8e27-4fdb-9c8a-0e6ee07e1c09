package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffCallUnit extends Buff {
    public int _unitId;
    public float[][] _attribs;

    public static BuffCallUnit alloc(ConfBuff o) {
        BuffCallUnit t = new BuffCallUnit();
        t.setConfig(o);
        t._unitId = o.param1;
        t._attribs = Utils.parseFloatArray2(o.param5);//o.param5.ToJaggedInts();
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        Vector2D i = this.owner != null ? this.owner.Position : this.position;
        this.runner.addCallUnit(this._unitId, i, this.currenTime, this._attribs, this.skillPar, 0 == this.config().param2);
    }
}
