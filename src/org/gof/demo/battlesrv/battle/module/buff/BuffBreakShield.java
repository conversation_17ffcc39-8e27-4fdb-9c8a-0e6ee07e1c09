package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.enumVo.BuffGroupType;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffBreakShield extends Buff {
    public static BuffBreakShield alloc(ConfBuff o) {
        BuffBreakShield t = new BuffBreakShield();
        t.setConfig(o);
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        this.owner.buffCtr.removeBuff(BuffGroupType.SHIELD.getValue());
        super.onBegin();
    }
}
