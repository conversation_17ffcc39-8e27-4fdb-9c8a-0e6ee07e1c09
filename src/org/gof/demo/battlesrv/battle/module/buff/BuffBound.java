package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffBound extends Buff {
    public static BuffBound alloc(ConfBuff o) {
        BuffBound t = new BuffBound();
        t.setConfig(o);
        return t;
    }

    @Override
    protected void onDestroy() {
        this.owner.statectr.bound--;
    }

    @Override
    protected void onBegin() {
        this.owner.statectr.bound++;
    }
}
