package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffSkillDamageAdd extends Buff {
    public int _calType;
    public float _calPercent;
    public List<Integer> _skillIds;

    public static BuffSkillDamageAdd alloc(ConfBuff o) {
        BuffSkillDamageAdd t = new BuffSkillDamageAdd();
        t.setConfig(o);
        t._calType = o.param1;
        t._calPercent = o.param2 / 10000;
        t._skillIds = Utils.strToIntList(o.param5);
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    public long calDamage(long t, UnitHuman n,int i) {
        long e = 0L;
        if (i > 0 && !this._skillIds.contains(i))
            return 0;
        if (0 == this._calType)
            e =  Math.round(t * (double)this.skillPar);
        else if (1 == this._calType) {
            float a = n.data.getAttrib(AttribDefine.hp);
            long r = n.data.currenHp;
            if (r / a <= this._calPercent)
                e =  Math.round(t * (double)this.skillPar);
        }
        return e;
    }
}
