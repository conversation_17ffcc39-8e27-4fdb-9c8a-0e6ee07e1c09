package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffDelayDamage extends Buff {
    public int _buffid;
    public float _addlasttime;

    public static BuffDelayDamage alloc(ConfBuff o) {
        BuffDelayDamage t = new BuffDelayDamage();
        t.setConfig(o);
        t._buffid = o.param1;
        t._addlasttime = o.param2;
        return t;
    }

    @Override
    protected void onDestroy() {
        this.owner.data.AddBuffState(SpBuffState.DelayDamage, -1);
    }

    @Override
    protected void onBegin() {
        this.owner.data.AddBuffState(SpBuffState.DelayDamage, 1);
    }

    @Override
    public void onDamage(float t) {
        this.runner.addBuff(this.owner, this._buffid, this._addlasttime, t);
    }
}
