package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.UnitMgr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;
import org.gof.demo.battlesrv.battle.enumVo.TargetSelectFilter;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffCheck extends Buff {

    private int checkbuffid;
    private int _perTriggerLayers;
    private float _maxTime;
    private String[][] _buffList;
    private int _checkCD;
    private float checkCount;
    private int triggeredTimes;

    public static BuffCheck alloc(ConfBuff o) {
        BuffCheck t = new BuffCheck();
        t.setConfig(o);
        t.checkbuffid = o.param1;
        t._perTriggerLayers = (int)o.param2;
        t._maxTime = o.param3;
        t._checkCD = o.param4;
        t._buffList = Utils.parseStrArray2(o.param5);
        return t;
    }

    private void onAddBuffCheck()
    {
        if (this.triggeredTimes >= this._maxTime){
            this.stop();
        }
        else {
            List<Buff> e = this.owner.buffCtr.getBuffListById(this.checkbuffid);
            int t = this._perTriggerLayers;

            if (t > 0 && e.size() >= t && this.triggeredTimes < this._maxTime) {
                ++this.triggeredTimes;
                String[] n = this._buffList[0];
                for (String r : n)
                {
                    String[] s =r.split("_");
                    this.runner.addBuff(this.owner, Utils.intValue(s[0]), Utils.floatValue(s[2]), Utils.floatValue(s[1]));
                }
            }
            if (this._buffList.length >= 2) {
                UnitMgr c = this.runner.battleMain.unitMgr;
                List<UnitHuman> h = c.findTarget(this.owner, TargetFilter.CastPartner, 100, this.owner.Position);
                List<UnitHuman> g = c.getTargetList(this.owner, h, 10, TargetSelectFilter.NearTarget);
                String[] l = this._buffList[1];
                for(UnitHuman _ : g){
                    for (String d : l)
                    {
                        String[] v = d.split("_");
                        if(v.length !=3)
                            continue;
                        this.runner.addBuff(_, Utils.intValue(v[0]), Utils.floatValue(v[1]), Utils.floatValue(v[2]) / 10000);
                    }
                }
            }
        }
    }

    @Override
    protected void onUpdate(float t) {
        if (this.triggeredTimes < this._maxTime) {
            if (this.triggeredTimes > 0 && this.checkCount < this._checkCD) {
                this.checkCount += t;
                return;
            }
            this.onAddBuffCheck();
            this.checkCount = 0;
        }
    }
}
