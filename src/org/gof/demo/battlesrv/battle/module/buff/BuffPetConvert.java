package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.battlesrv.battle.module.MetaAttrib;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffPetConvert extends Buff {

    private int _calType;
    private AttribDefine _attribId;
    private AttribDefine _tagAttribId;
    private float[] _limit;
    private float _lastValue;

    public static BuffPetConvert alloc(ConfBuff o) {
        BuffPetConvert t = new BuffPetConvert();
        t.setConfig(o);
        t._calType = o.param1;
        t._attribId = AttribDefine.fromValue((int)o.param2);
        t._tagAttribId = AttribDefine.fromValue((int)o.param3);
        t._limit = Utils.strToFloatArray(o.param5);
        return t;
    }

    @Override
    protected void onBegin() {
        float t = this._calValue();
        MetaAttrib a = this.owner.data.getAttribMeta(this._tagAttribId);
        this._lastValue = t;
        a.addExtraValue(this._lastValue);
    }

    @Override
    protected void onDestroy() {
        MetaAttrib t = this.owner.data.getAttribMeta(this._tagAttribId);
        t.addExtraValue(-this._lastValue);

    }

    private float _calValue()
    {
        UnitHuman t = this.owner.parent;
        if (t == null)
            return 0;
        float a = 0f;
        switch (this._calType)
        {
            case 0:
                MetaAttrib i = t.data.getAttribMeta(this._attribId);
                a = i.getValue();
                if (2 == i.config.num_type)
                    a = FixRandom.roundInt(10000 * i.getValue());
                break;
        }
        MetaAttrib e = this.owner.data.getAttribMeta(this._tagAttribId);
        a = FixRandom.roundInt(a * this.skillPar);
        if (this._limit != null && 2 == this._limit.length)
        {
            a = Math.min(Math.max(a, FixRandom.round(this._limit[0] * e.getValue())), FixRandom.round(this._limit[1] * e.getValue()));
        }
        return a;
    }
}
