package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.UnitMgr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.TargetFilter;
import org.gof.demo.battlesrv.battle.enumVo.TargetSelectFilter;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.ArrayList;
import java.util.List;

public class BuffDizz extends Buff {

    private int _dizzType;
    private float _stayTime;
    private int _triggerType;
    private int _triggerNum;
    private String[][] _buffList;
    private List<Buff> _buffs;

    private float checkTime;
    private long hpLog;
    private long hpFloatTop;
    private long hurtLog;

    public static BuffDizz alloc(ConfBuff o) {
        BuffDizz t = new BuffDizz();
        t.setConfig(o);
        t._dizzType = o.param1;
        t._stayTime = o.param2;
        t._triggerType = (int)o.param3;
        t._triggerNum = o.param4;
        t._buffList = Utils.parseStrArray2(o.param5);
        t._buffs = new ArrayList<>();
        return t;
    }

    @Override
    protected void onDestroy() {
        this.owner.statectr.dizz--;
        for(Buff t : this._buffs){
            t.stop();
        }
        this._buffs.clear();
        if (0 == this._triggerType) {
            if (this._buffList !=null && this._buffList.length >= 2) {
                String[] f = this._buffList[1];
                for (String _f : f) {
                    int[] h = Utils.strToIntArray(_f,"_");
                    this.runner.addBuff(this.owner, h[0], h[1], h[2] / 10000f);
                }
            }
            if (this._buffList !=null && this._buffList.length >= 3)
            {
                UnitMgr d = this.runner.battleMain.unitMgr;
                List<UnitHuman> g = d.findTarget(this.owner, TargetFilter.CastPartner, 100, this.owner.Position,true);
                List<UnitHuman> p = d.getTargetList(this.owner, g, 10, TargetSelectFilter.NearTarget);
                String[] v = this._buffList[2];
                for (String _v : v) {
                    int[] b =  Utils.strToIntArray(_v,"_");
                    for (UnitHuman L : p) {
                        this.runner.addBuff(L, b[0], b[1], b[2] / 10000f);
                    }
                }
            }
        }
    }

    @Override
    protected void onBegin() {
        this.checkTime = 0;
        this.owner.statectr.dizz++;
        this.hpLog = this.owner.data.currenHp;
        this.hpFloatTop = this.hpLog;
        this.hurtLog = 0;
        this._buffs.clear();
        switch (this._triggerType)
        {
            case 0:
                if (this._buffList !=null &&this._buffList.length > 0)
                {
                    String[] e = this._buffList[0];
                    for (String _e : e)
                    {
                        int[] s = Utils.strToIntArray(_e,"_");
                        this._buffs.add(this.runner.addBuff(this.owner, s[0], this._stayTime, s[1] / 10000f));
                    }
                }
                break;
            case 1:
                if (this._buffList !=null && this._buffList.length > 0)
                {
                    String[] n = this._buffList[0];
                    for (String _n : n)
                    {
                        int[] f = Utils.strToIntArray(_n,"_");
                        this._buffs.add(this.runner.addBuff(this.owner, f[0], this._stayTime, f[1] / 10000f));
                    }
                }
                break;
            case 2:
                if (this._buffList !=null && this._buffList.length > 0)
                {
                    String[] h = this._buffList[0];
                    for (String _h : h)
                    {
                        int[] g = Utils.strToIntArray(_h,"_");
                        this._buffs.add(this.runner.addBuff(this.owner, g[0], this._stayTime, g[1] / 10000f));
                    }
                }
                break;
        }
    }

    private void onCheckDizzBreak()
    {
        if (this.checkTime > this._stayTime)
        {
            for (Buff t : this._buffs){
                t.stop();
            }
            this._buffs.clear();

            if (this._buffList !=null && this._buffList.length >= 2)
            {
                String[] f = this._buffList[1];
                if (f != null)
                {
                    for (String _f : f)
                    {
                        int[] h = Utils.strToIntArray(_f,"_");
                        this.runner.addBuff(this.owner, h[0], h[1], h[2] / 10000f);
                    }
                }
            }
            if (this._buffList !=null && this._buffList.length >= 3)
            {
                UnitMgr d = this.runner.battleMain.unitMgr;
                List<UnitHuman> g = d.findTarget(this.owner, TargetFilter.CastPartner, 100, this.owner.Position,true);
                List<UnitHuman> p = d.getTargetList(this.owner, g, 10, TargetSelectFilter.NearTarget);
                String[] v = this._buffList[2];
                if (v != null)
                {
                    for (String _v : v)
                    {
                        int[] b = Utils.strToIntArray(_v,"_");
                        for(UnitHuman L : p)
                        {
                            this.runner.addBuff(L, b[0], b[1], b[2] / 10000f);
                        }
                    }
                }
            }
            this.stop();
        }
        else
        {
            boolean w = false;
            float y = this.owner.data.getAttrib(AttribDefine.hp);
            long m = this.owner.data.currenHp;
            switch (this._triggerType)
            {
                case 0:
                    break;
                case 1:
                    if ((m / y * 10000) <= this._triggerNum)
                    {
                        w = true;
                        for (Buff z : this._buffs){
                            z.stop();
                        }
                        this._buffs.clear();
                        String[] k = this._buffList[1];
                        if (k != null)
                        {
                            for (String n : k)
                            {
                                int[] N = Utils.strToIntArray(n,"_");
                                this.runner.addBuff(this.owner, N[0], N[1], N[2] / 10000f);
                            }
                        }
                    }
                    break;
                case 2:
                    if (m > this.hpFloatTop) {
                        this.hpFloatTop = m;
                    }
                    this.hurtLog += Math.max(this.hpFloatTop - m, 0);
                    this.hpFloatTop = m;
                    if ((float) this.hurtLog / this.hpLog * 10000f >= this._triggerNum)
                    {
                        w = true;
                        for (Buff D : this._buffs){
                            D.stop();
                        }
                        this._buffs.clear();
                        UnitMgr x = this.runner.battleMain.unitMgr;
                        List<UnitHuman> H = x.findTarget(this.owner, TargetFilter.CastPartner, 100, this.owner.Position,true);
                        List<UnitHuman> I = x.getTargetList(this.owner, H, 10, TargetSelectFilter.NearTarget);
                        String[] A = this._buffList[1];
                        if (A != null)
                        {
                            for(String r : A)
                            {
                                int[] R = Utils.strToIntArray(r,"_");
                                for(UnitHuman E : I)
                                {
                                    this.runner.addBuff(E, R[0], R[1], R[2] / 10000f);
                                }
                            }
                        }
                    }
                    break;
            }
            if (w)
            {
                if (this._buffList !=null && this._buffList.length >= 3)
                {
                    UnitMgr V = this.runner.battleMain.unitMgr;
                    List<UnitHuman> Y = V.findTarget(this.owner, TargetFilter.CastPartner, 100, this.owner.Position,true);
                    List<UnitHuman> q = V.getTargetList(this.owner, Y, 10, TargetSelectFilter.NearTarget);
                    String[] J = this._buffList[2];
                    if (J != null)
                    {
                        for(String j : J)
                        {
                            int[] Q = Utils.strToIntArray(j,"_");
                            for(UnitHuman X : q){
                                this.runner.addBuff(X, Q[0], Q[1], Q[2] / 10000f);
                            }
                        }
                    }
                }
                this.stop();
            }
        }
    }

    @Override
    protected void onUpdate(float t) {
        this.checkTime += t;
        this.onCheckDizzBreak();
    }
}
