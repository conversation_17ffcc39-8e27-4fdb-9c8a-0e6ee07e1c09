package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.MathUtil;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.BuffGroupType;
import org.gof.demo.battlesrv.battle.enumVo.HpType;
import org.gof.demo.battlesrv.battle.enumVo.StateTrigerType;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.ArrayList;
import java.util.List;

public class BuffShield extends Buff {
    public int _hpType;
    public int _calType;
    public int _attribId;
    public long _value;
    public List<Buff> _buffList = new ArrayList<>();
    public List<Buff> _buffs = new ArrayList<>();

    public static BuffShield alloc(ConfBuff o) {
        BuffShield t = new BuffShield();
        t.setConfig(o);
        t._hpType = o.param1;
        t._calType = (int) o.param2;
        t._attribId = (int) o.param3;
        return t;
    }

    @Override
    protected void onDestroy() {
        for (Buff buff : this._buffList) {
            buff.destroy();
        }
        for (Buff n : this._buffs) {
            n.stop();
        }
        this._buffList.clear();
        if (this._value > 0) {
            this.owner.data.shieldHp = this.owner.data.shieldHp - this._value;
            this.owner.data.shieldHp = Math.max(0, this.owner.data.shieldHp);
        }
        String[] h = this.config().param5.split("\\|");
        if (h.length > 1 && !h[1].isEmpty()) {
            String[] l = h[1].split(",");
            for (String f : l) {
                String[] p = f.split("_");
                this.runner.addBuff(this.owner, Utils.intValue(p[0]) , Utils.floatValue(p[1]), Utils.intValue(p[2]) / 10000f);
            }
        }
    }

    @Override
    protected void onBegin() {
        UnitHuman t = this.owner;
        UnitHuman e = this.runner.cast();
        long a = 0;
        switch (this._calType) {
            case 0:
                a = MathUtil.AND(this._hpType, HpType.Target.getValue()) ? (int) t.data.getAttrib(AttribDefine.fromValue(this._attribId)) : (int) e.data.getAttrib(AttribDefine.fromValue(this._attribId));
                break;
            case 1:
                float n = e.data.getAttrib(AttribDefine.att);
                float s = t.data.getAttrib(AttribDefine.def);
                float d = t.data.getAttrib(AttribDefine.def_coe);
                a = Math.max(FixRandom.roundInt(n - s * (1 + d)), 1);
                break;
            case 2:
                long f = e.data.getAttribByInt(AttribDefine.hp);
                a = f - t.data.currenHp;
                break;
            case 3:
                a = t.data.currenHp;
                break;
        }
        a = FixRandom.roundInt(a * this.skillPar);
        a = FixRandom.roundInt(a * (1 + t.data.getAttrib(AttribDefine.shield_hp_extra)));
        a = FixRandom.roundInt(a * t.battleMain().shieldDecay);
        this._value = a;
        String[] p = this.config().param5.split("\\|");
        if (p.length > 0 && !p[0].isEmpty()) {
            String[] b = p[0].split(",");
            for(String _g : b)
            {
                String[] g = _g.split("_");
                this._buffs.add(this.runner.addBuff(t,  Utils.intValue(g[0]), -1,  Utils.intValue(g[1]) / 10000f));
            }
        }
        this._buffList.clear();
        List<Buff> y = t.buffCtr.getBuffByType(BuffGroupType.STATE_TRIGER.getValue());
        for (Buff c : y) {
            List<Buff> b = c.onStateTrigger(StateTrigerType.Shield, null);
            if (b != null && !b.isEmpty())
                this._buffList.addAll(b);
        }
        this.owner.data.shieldHp = this.owner.data.shieldHp + this._value;
    }

    @Override
    public long onShieldAction(long t) {
        if (!this._run)
            return 0;
        long e = Math.min(this._value, t);
        this.owner.data.shieldHp = this.owner.data.shieldHp - e;
        this.owner.data.shieldHp = Math.max(0, this.owner.data.shieldHp);
        this._value = this._value - e;
        if (this._value <= 0)
            this.stop();
        return e;
    }
}
