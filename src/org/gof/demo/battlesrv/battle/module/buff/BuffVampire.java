package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.FixRandom;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.bentity.ctroller.SkillCtr;
import org.gof.demo.battlesrv.battle.enumVo.*;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffVampire extends Buff {

    private int _calType;
    private int _topType;
    private float _max;

    public static BuffVampire alloc(ConfBuff t) {
        BuffVampire i = new BuffVampire();
        i.setConfig(t);
        i._calType =t.param1;
        i._topType = (int)t.param2;
        i._max = t.param3;
        return i;
    }

    @Override
    public long calDamage(long t, UnitHuman e, int n) {
        UnitHuman u = this.runner.cast();
        long s = 0L;
        switch (this._calType)
        {
            case 0:
                s = t;
                break;
        }
        float l = 0f;
        switch (this._topType)
        {
            case 0:
                float f = u.data.getAttrib(AttribDefine.hp);
                l = FixRandom.round(f * this._max / 10000f);
                break;
        }
        BattleMain p = this.runner.battleMain;
        s = Math.max(FixRandom.roundInt(s / this.runner.battleMain.injuryReduce), 1);
        float h = this.runner.useSkill().skillDam.length > 0 ? this.runner.useSkill().skillDam[0] : 1;
        float y = FixRandom.round(s * h / 10000);
        float v = l / p.treatDecay;
        if (y < l)
            v = y / p.treatDecay;
        s = FixRandom.roundInt(v);
        this.runner.healthTarget(u, s, HealthType.Treat);
        return s;
    }
}