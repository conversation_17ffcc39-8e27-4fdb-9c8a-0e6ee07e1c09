package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.enumVo.BuffGroupType;
import org.gof.demo.battlesrv.battle.enumVo.HealthType;
import org.gof.demo.battlesrv.battle.enumVo.StateTrigerType;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfBuff;
import org.gof.demo.worldsrv.entity.Unit;

import java.util.List;

public class Buff {

    public UnitHuman owner;
    public Vector2D position = new Vector2D();
    public SkillRunner runner;
    protected ConfBuff _config;
    public int buffType;
    public float currenTime = 0;
    protected boolean _run = false;
    protected boolean _isDestroy = false;
    public float skillPar = 0;

    public ConfBuff config() {
        return _config;
    }

    public void setConfig(ConfBuff value) {
        _config = value;
        this.buffType = value.group;
    }

    protected void loadEffect() {

    }

    public boolean execBuff(float t) {
        if (!this._run || 0 == this.currenTime)
            return false;

        if (-1 != this.currenTime) {
            this.currenTime = this.currenTime - t;
            this.currenTime = Math.max(this.currenTime, 0);
        }
        this.loadEffect();
        this.onUpdate(t);
        return true;
    }

    public void start() {
        this._run = true;
        this._isDestroy = false;
        this.loadEffect();
        this.onBegin();
    }

    protected void _destroy() {
        this.onDestroy();
        this.owner = null;
        this.runner = null;
        this.skillPar = 0;
    }

    public void stop() {
        this._run = false;
    }

    public void destroy() {
        if (!this._isDestroy) {
            this._isDestroy = true;
            this._destroy();
        }
    }

    public void updateAttrib() {
    }

    public long calDamage(long t, UnitHuman i,int e) {
        return 0;
    }

    public long onShieldAction(long t) {
        return 0;
    }

    public float onShareDamageAction(long t, UnitHuman i, HealthType n) {
        return 0;
    }

    public void onDamage(float t) {
    }

    public void onTotalDamage(int t) {
    }

    public List<Buff> onStateTrigger(StateTrigerType t, UnitHuman i) {
        return null;
    }// i = null

    public void onAddBuffTrigger(UnitHuman t, int i, float n) {
    }

    public boolean checkTarget(float t) {
        return false;
    }

    public long onCalHpDamage(UnitHuman t, long i) { return i; }
    public boolean checkSkillReturn(UnitHuman t, SkillRunner i) { return false; }
    public float getFixHp() { return 0; }
    public float getFixTime(float t, int i) { return t; }
    public boolean getRunState() { return this._run; }

    protected void onBegin() {
    }

    protected void onUpdate(float t) {
    }

    protected void onDestroy() {
    }

    public void OnAlloc() {

    }
}