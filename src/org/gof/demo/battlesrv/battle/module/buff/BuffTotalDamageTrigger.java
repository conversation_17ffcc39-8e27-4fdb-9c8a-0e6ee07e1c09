package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffTotalDamageTrigger extends Buff {
    public boolean hasTrigger;
    public int _calType;
    public float _value;
    public int _targetType;
    public float _addlasttime;
    public int[] _addBuffList;

    public static BuffTotalDamageTrigger alloc(ConfBuff o) {
        BuffTotalDamageTrigger t = new BuffTotalDamageTrigger();
        t.setConfig(o);
        t._calType = o.param1;
        t._value = o.param2;
        t._targetType = (int) o.param3;
        t._addlasttime = o.param4;
        t._addBuffList = Utils.arrayStrToInt(o.param5);
        t.hasTrigger = false;
        return t;
    }

    @Override
    protected void onDestroy() {
        this.owner.data.AddBuffState(SpBuffState.TotalDamageTrigger, -1);
    }

    @Override
    protected void onBegin() {
        this.owner.data.AddBuffState(SpBuffState.TotalDamageTrigger, 1);
    }

    @Override
    public void onTotalDamage(int t) {
        if (!this.hasTrigger) {
            float a = this.owner.data.totalDamage;
            if (0 == this._calType) {
                if (a < this._value)
                    return;
            } else if (1 == this._calType && (a / this.owner.data.maxHp) < (this._value / 10000))
                return;
            UnitHuman e = 0 == this._targetType ? this.owner : this.runner.battleMain.unitMgr.getUnit(t);
            for (int i = 0; i < this._addBuffList.length; i++)
                this.runner.addBuff(e, this._addBuffList[i], this._addlasttime, this.skillPar);
            this.hasTrigger = true;
        }
    }
}
