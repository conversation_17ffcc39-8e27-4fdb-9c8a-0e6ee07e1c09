package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffNotGetDamage extends Buff {
    public static BuffNotGetDamage alloc(ConfBuff o) {
        BuffNotGetDamage t = new BuffNotGetDamage();
        t.setConfig(o);
        return t;
    }

    @Override
    protected void onDestroy() {
        this.owner.statectr.notGetDamage--;
    }

    @Override
    protected void onBegin() {
        this.owner.statectr.notGetDamage++;
        super.onBegin();
    }
}
