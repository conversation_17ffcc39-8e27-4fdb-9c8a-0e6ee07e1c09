package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.UnitType;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffTrigerByUType extends Buff {
    public int _bossbuff;
    public float _bossbufftime;
    public int _monsterbuff;
    public float _monsterbufftime;

    public static BuffTrigerByUType alloc(ConfBuff o) {
        BuffTrigerByUType t = new BuffTrigerByUType();
        t.setConfig(o);
        t._bossbuff = o.param1;
        t._bossbufftime = o.param2;
        t._monsterbuff = (int) o.param3;
        t._monsterbufftime = o.param4;
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        UnitHuman t = this.owner;
        if (this.owner.config().type != (int) UnitType.Monster.getValue())
            this.runner.addBuff(t, this._bossbuff, this._bossbufftime, this.skillPar);
        else
            this.runner.addBuff(t, this._monsterbuff, this._monsterbufftime, this.skillPar);
    }
}
