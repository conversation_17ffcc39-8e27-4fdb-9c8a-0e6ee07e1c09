package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.battlesrv.battle.enumVo.StateTrigerType;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffDoubleTrigger extends Buff {
    public int _triggercount;
    public float _addlasttime;
    public int[] _addbufflist;
    public float _currentTriggerValue;

    public static BuffDoubleTrigger alloc(ConfBuff o) {
        BuffDoubleTrigger t = new BuffDoubleTrigger();
        t.setConfig(o);
        t._triggercount = o.param1;
        t._addlasttime = o.param2;
        t._addbufflist = Utils.strToIntArray(o.param5);//o.param5.ToInts();
        t._currentTriggerValue = 0;
        return t;
    }

    @Override
    protected void onDestroy() {
        this.owner.data.AddBuffState(SpBuffState.StateTriger, -1);
    }

    @Override
    protected void onBegin() {
        this.owner.data.AddBuffState(SpBuffState.StateTriger, 1);
    }

    @Override
    public List<Buff> onStateTrigger(StateTrigerType t, UnitHuman r)//r = null
    {
        this._currentTriggerValue++;
        if (this._currentTriggerValue >= this._triggercount) {
            this._currentTriggerValue = 0;
            for (int e = 0; e < this._addbufflist.length; e++)
                this.runner.addBuff(r, this._addbufflist[e], this._addlasttime, this.skillPar);
        }
        return null;
    }
}
