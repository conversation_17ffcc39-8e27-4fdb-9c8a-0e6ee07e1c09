package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffInvincible extends Buff {
    public static BuffInvincible alloc(ConfBuff o) {
        BuffInvincible t = new BuffInvincible();
        t.setConfig(o);
        return t;
    }

    @Override
    protected void onDestroy() {
        this.owner.statectr.invincible--;
    }

    @Override
    protected void onBegin() {
        this.owner.statectr.invincible++;
    }
}
