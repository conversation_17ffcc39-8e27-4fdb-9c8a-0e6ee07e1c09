package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.BuffGroupType;
import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.battlesrv.battle.enumVo.StateTrigerType;
import org.gof.demo.worldsrv.config.ConfBuff;

import java.util.List;

public class BuffBanAct extends Buff {
    public static BuffBanAct alloc(ConfBuff o) {
        BuffBanAct t = new BuffBanAct();
        t.setConfig(o);
        return t;
    }

    public BuffBanAct() {
    }

    public BuffBanAct(ConfBuff o) {
        setConfig(o);
    }

    @Override
    protected void onBegin() {
        this.owner.statectr.banAct++;
        UnitHuman t = this.owner;
        if (t.data.getBuffState(SpBuffState.StateTriger) > 0) {
            List<Buff> o = t.buffCtr.getBuffByType(BuffGroupType.STATE_TRIGER.getValue());
            for (Buff e : o) {
                e.onStateTrigger(StateTrigerType.BanAct, null);
            }
            o.clear();
        }
    }

    @Override
    protected void onDestroy() {
        this.owner.statectr.banAct--;
    }
}
