package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.SpBuffState;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffAddBuffTrigger extends Buff {
    private int _trigerbuffid;
    private int _triggercount;
    private int _addlasttime;
    private int[] _addbufflist;
    private int _currentTriggerValue;

    public BuffAddBuffTrigger() {

    }

    public static BuffAddBuffTrigger alloc(ConfBuff t) {
        BuffAddBuffTrigger r = new BuffAddBuffTrigger();
        r.setConfig(t);
        r._trigerbuffid = t.param1;
        r._triggercount = (int) t.param2;
        r._addlasttime = (int) t.param3;
        r._addbufflist = Utils.strToIntArray(t.param5, "\\,");//t.param5.ToInts();
        r._currentTriggerValue = 0;
        return r;
    }

    public BuffAddBuffTrigger(ConfBuff t) {
        super.setConfig(t);
        _trigerbuffid = t.param1;
        _triggercount = (int) t.param2;
        _addlasttime = (int) t.param3;
        _addbufflist = Utils.strToIntArray(t.param5, "\\,");
        _currentTriggerValue = 0;
    }


    @Override
    protected void onBegin() {
        this.owner.data.AddBuffState(SpBuffState.AddBuffTrigger, 1);
    }

    @Override
    protected void onDestroy() {
        this.owner.data.AddBuffState(SpBuffState.AddBuffTrigger, -1);
    }

    @Override
    public void onAddBuffTrigger(UnitHuman t, int r, float i) {
        if (r == this._trigerbuffid && ++this._currentTriggerValue >= this._triggercount) {
            this._currentTriggerValue = 0;
            for (int e = 0; e < this._addbufflist.length; e++) {
                if (0 == this._addlasttime)
                    this.runner.addBuff(t, this._addbufflist[e], i, this.skillPar);
                else
                    this.runner.addBuff(t, this._addbufflist[e], this._addlasttime, this.skillPar);
            }
        }
    }
}
