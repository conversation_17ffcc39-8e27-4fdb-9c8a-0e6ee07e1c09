package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.module.Trap;
import org.gof.demo.worldsrv.config.ConfBuff;
import org.gof.demo.worldsrv.config.ConfTrap;

import java.util.ArrayList;
import java.util.List;

public class BuffTrap extends Buff {
    public ConfTrap trapConfig;
    public List<Integer> triggerList = new ArrayList<>();
    public float trapCurTime;
    public boolean triggerfirst;

    public static BuffTrap alloc(ConfBuff o) {
        BuffTrap t = new BuffTrap();
        t.setConfig(o);
        t.trapConfig = ConfTrap.get(o.param1);
        t.triggerfirst = 1 != o.param3;
        return t;
    }

    @Override
    protected void onDestroy() {
        this.triggerList.clear();
    }

    @Override
    protected void onBegin() {
        if (this.triggerfirst)
            Trap.trigger(this, this.owner.Position);
        this.trapCurTime = 0;
    }

    @Override
    protected void onUpdate(float t) {
        if(this.trapConfig.execute == null ||this.trapConfig.execute.length == 0){
            return;
        }
        if ( this.trapCurTime >= this.trapConfig.execute[0]) {
            Trap.trigger(this, this.owner.Position);
            this.trapCurTime = this.trapCurTime - this.trapConfig.execute[0];
        }
        this.trapCurTime = this.trapCurTime + t;
    }
}
