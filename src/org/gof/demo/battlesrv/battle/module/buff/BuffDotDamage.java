package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.enumVo.HealthType;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffDotDamage extends Buff {
    public float _freqtime;
    public float _damage;
    public float _curtime;
    public float _totalddamagevalue;

    public static BuffDotDamage alloc(ConfBuff o) {
        BuffDotDamage t = new BuffDotDamage();
        t.setConfig(o);
        t._freqtime = o.param1;
        t._curtime = 0;
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    protected void onBegin() {
        this._totalddamagevalue = this.skillPar;
        this._damage = this._totalddamagevalue / (this.currenTime / this._freqtime);
    }

    @Override
    protected void onUpdate(float t) {
        if (this._curtime >= this._freqtime && this._totalddamagevalue > 0) {
            this._totalddamagevalue = this._totalddamagevalue - this._damage;
            long e = Math.round((double) this._damage);
            this.owner.hit(null, e, HealthType.Hurt_Share_Damage, 0);
            this._curtime = 0;
        }
        this._curtime = this._curtime + t;
    }
}
