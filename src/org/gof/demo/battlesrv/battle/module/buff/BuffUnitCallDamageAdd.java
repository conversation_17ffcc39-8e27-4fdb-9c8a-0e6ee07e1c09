package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffUnitCallDamageAdd extends Buff {
    public int _calType;
    public float _calPercent;

    public static BuffUnitCallDamageAdd alloc(ConfBuff o) {
        BuffUnitCallDamageAdd t = new BuffUnitCallDamageAdd();
        t.setConfig(o);
        t._calType = o.param1;
        t._calPercent = (int) o.param2;
        return t;
    }

    @Override
    protected void onDestroy() {
    }

    @Override
    public long calDamage(long t, UnitHuman n,int i) {
        long e = 0L;
        if (0 == this._calType)
            e = Math.round(t * (double)this.skillPar);
        else if (1 == this._calType) {
            float a = n.data.getAttrib(AttribDefine.hp);
            long o = n.data.currenHp;
            if (o / a <= this._calPercent / 10000f)
                e = Math.round(t * (double)this.skillPar);
        }
        return e;
    }
}
