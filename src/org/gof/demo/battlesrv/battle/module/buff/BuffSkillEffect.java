package org.gof.demo.battlesrv.battle.module.buff;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.config.ConfBuff;

public class BuffSkillEffect extends Buff {
    public int _id;

    public static BuffSkillEffect alloc(ConfBuff o) {
        BuffSkillEffect t = new BuffSkillEffect();
        t.setConfig(o);
        t._id = o.param1;
        return t;
    }

    @Override
    protected void onDestroy() {
        this.runner.popRun();
        this.owner.skillctr.removeSkillEffect(this._id);
    }

    @Override
    protected void onBegin() {
        this.runner.pushRun();
        this.owner.skillctr.addSkillEffect(this._id, this.runner, (int) this.config().param2, (int) this.config().param3,
                (int) this.config().param4, Utils.strToStrArray(this.config().param5, "\\|"));
    }
}
