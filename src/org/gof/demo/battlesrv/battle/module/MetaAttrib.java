package org.gof.demo.battlesrv.battle.module;

import org.gof.demo.worldsrv.config.ConfAttribute;

public class MetaAttrib {
    public ConfAttribute config;

    public ConfAttribute getConfig() {
        return config;
    }

    public void setConfig(ConfAttribute conf) {
        this.config = conf;
    }

    private long _baseValue = 0;
    private float _addValue = 0;
    private float _addExtraValue = 0;
    private float _time = 1;
    private float _value = 0;
    private long _checkValue = 0;
    private boolean _change = false;

//    public float Value;

    public float getValue() {
        if (this._change) {
            this._change = false;
            this._value = this._calculateValue(this._baseValue);
        }
        return _value;
    }

//    public float BaseValue;

    public float getBaseValue() {
        return this._baseValue;
    }

    public void setBaseValue(double value) {
        this._baseValue = Math.round(value);
        if(value == -1 && 2 == this.config.num_type) this._baseValue = 10000 ;
        //this._baseValue = Mathf.RoundToInt(this._baseValue);
        this._checkValue = 32 ^ this._baseValue;
        this._change = true;
    }

    public MetaAttrib(ConfAttribute conf, MetaAttrib t)//t = null
    {
        this.config = conf;
        if (t != null) {
            this._baseValue = t._baseValue;
            this._addValue = t._addValue;
            this._time = t._time;
            this._value = t._value;
            this._checkValue = t._checkValue;
            this._change = true;
        }
    }

    private float _calculateValue(float value) {
        float i = Math.round(((double) value + this._addValue) * this._time+ this._addExtraValue);
        if (0 != this.config.up_limit)
            i = Math.min(i, this.config.up_limit);
        if (2 == this.config.num_type)
            i = (i / 10000);
        //return i = null != (t = i) ? t : 0;
        return i;
    }

    public void addExtraValue(float value)
    {
        this._addExtraValue = (this._addExtraValue + value);
        this._change = true;
    }

    public void addValue(float value) {
        this._addValue = (this._addValue + value);
        this._change = true;
    }

    public void addMultiples(float value) {
        this._time = (this._time + value);
        this._change = true;
    }

    public void setAttribValue(MetaAttrib e)
    {
        if (e != null)
        {
            this._baseValue = e._baseValue;
            this._addValue = e._addValue;
            this._addExtraValue = e._addExtraValue;
            this._time = e._time;
            this._value = e._value;
            this._checkValue = e._checkValue;
            this._change = true;
        }
    }

    public boolean checkCheat() {
        float e = this._calculateValue(32 ^ this._checkValue);
        if (this.getValue() != e) {
            return false;
        }
        return true;
    }

}