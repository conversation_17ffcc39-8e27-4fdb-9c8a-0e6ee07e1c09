package org.gof.demo.battlesrv.battle.module.state;

import org.gof.demo.battlesrv.battle.bentity.ctroller.StateCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.module.TargetSkill;
import org.gof.demo.battlesrv.battle.enumVo.StateType;

public class UnitSkillState extends State {
    private SkillRunner _skillRunner;

    public UnitSkillState(StateCtr ctr, int priority) {
        super(ctr, priority);
        type = StateType.Skill;
    }

    @Override
    public void onEnter() {
        UnitHuman t = this._owner;
        if (null != t.curSkill() || 0 == t.data.attack.state) {
            if (null == t.curSkill()) {
                t.setWantSkill(TargetSkill.toSkill(t.data.attack, TargetSkill.TP_AutoFind));
                t.applySkill(false);
            }
            t.idle();
            if (t.currenSkillRunner != null)
                t.currenSkillRunner.interrupt();
            t.statectr.lockCurrenState = true;
            SkillRunner n = new SkillRunner();
            n.init(t, t.curSkill().getSkill(), false);
            n.start();
            if (null != t.curSkill().getTarget())
                n.setLockTarget(t.curSkill().getTarget());
            t.skillctr.addRunner(n);
            t.currenSkillRunner = n;
            this._skillRunner = n;
        } else
            this.changeState();
    }

    @Override
    public void onUpdate(float t) {
        SkillRunner n = this._skillRunner;
        UnitHuman l = this._owner;
        if (null != n && !n.isInterrupt() || null != l.wantSkill()) {
            if (null == l.wantSkill() || this._ctr.lockCurrenState)
                return;
            else if (null != n) {
                n.interrupt();
                l.currenSkillRunner = null;
                l.applySkill(false);
                this.onEnter();
            }
            return;
        }
        this.changeState();
    }

    @Override
    public void onExit() {
        UnitHuman t = this._owner;
        t.applySkill(true);
        if (this._skillRunner != null) this._skillRunner.interrupt();
        this._skillRunner = null;
        t.currenSkillRunner = null;
        t.movectr.startMove();
    }
}
