package org.gof.demo.battlesrv.battle.module.state;

import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.ctroller.StateCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.StateType;

public class UnitOverState extends State {
    public UnitOverState(StateCtr ctr, int priority) {
        super(ctr, priority);
        type = StateType.Over;
    }

    @Override
    public void onEnter() {
        UnitHuman t = this._owner;
        t.stopAI++;
        t.animatorctr.changeState(BattleParamKey.UnitConfig_ANIMATOR_IDLE);
        t.statectr.lockCurrenState = true;
    }

    @Override
    public void onExit() {
        this._owner.stopAI--;
    }
}
