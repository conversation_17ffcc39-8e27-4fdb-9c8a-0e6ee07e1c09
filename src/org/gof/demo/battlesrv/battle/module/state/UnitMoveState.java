package org.gof.demo.battlesrv.battle.module.state;

import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.ctroller.StateCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.MathUtil;
import org.gof.demo.battlesrv.battle.module.TargetUnit;
import org.gof.demo.battlesrv.battle.enumVo.StateType;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.support.Log;

public class UnitMoveState extends State {
    private Vector2D _targetPos;

    public UnitMoveState(StateCtr ctr, int priority) {
        super(ctr, priority);
        type = StateType.Move;
    }

    private boolean moveToTargetPos() {
        UnitHuman t = this._owner;
        TargetUnit e = t.curTarget();

        if (e != null) {
            if (null != e.getUnit()) {
                e.setPos(e.getUnit().Position);
                if (!t.movectr.checkFormationPostion(e.getUnit(), _targetPos))
                    return false;
            }

            if (MathUtil.DistanceX(e.getPos(), t.Position) > 1) {
                t.movectr.startMove();
                return true;
            }
        }
        return false;
    }

    @Override
    public void onEnter() {
        UnitHuman t = this._owner;
        if (this.moveToTargetPos())
            t.animatorctr.changeState(BattleParamKey.UnitConfig_ANIMATOR_MOVE, false, 1);
        else
            this.exitState();
    }

    @Override
    public void onUpdate(float t) {
        UnitHuman e = this._owner;
        if (null != e.curTarget()) {
            if (null == e.checkTarget()) {
                switch (e.movectr.state()) {
                    case Seeking:
                        if (!e.animatorctr.hasState(BattleParamKey.UnitConfig_ANIMATOR_MOVE))
                            e.animatorctr.changeState(BattleParamKey.UnitConfig_ANIMATOR_MOVE);
                        break;
                    case Stop:
                        this.exitState();
                        break;
                    default:
                        Log.temp.error("===e.movectr.state={}", e.movectr.state());
                        break;
                }
            } else
                this.exitState();
        } else
            this.exitState();
    }

    private void exitState() {
        UnitHuman t = this._owner;
        if (t.animatorctr.hasState(BattleParamKey.UnitConfig_ANIMATOR_MOVE)) {
            TargetUnit e = t.curTarget();
            if (null != e && e.getType() == TargetUnit.T_UNIT) {
                double i = MathUtil.DistanceX(t.Position, e.getUnit().Position);
                i -= /*Mathf.Round*/(t.radiusSize + e.getUnit().radiusSize);
                if (i > t.attackDistance() && this.moveToTargetPos())
                    return;
            }
        }
        this.changeState();
    }

    @Override
    public void onExit() {
        UnitHuman t = this._owner;
        if (null != t.curTarget()) {
            t.applyTarget(true);
            t.movectr.stopMove();
        }
    }
}
