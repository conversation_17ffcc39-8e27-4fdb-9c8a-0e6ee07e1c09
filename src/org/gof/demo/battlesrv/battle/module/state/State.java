package org.gof.demo.battlesrv.battle.module.state;

import org.gof.demo.battlesrv.battle.bentity.ctroller.StateCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.StateType;

public class State {
    protected int _priority;
    protected StateCtr _ctr;
    public UnitHuman _owner;
    public boolean isRepeat;

    public StateType type;
    public int priority(){
        return _priority;
    }

    public State(StateCtr ctr, int priority) {
        _ctr = ctr;
        _priority = priority;
    }

    protected void changeState() {
        this._ctr.lockCurrenState = false;
        if (this._owner.dead)
            this._owner.changeDeadState();
        else
            this.changeState(StateType.Idle);
    }

    protected void changeState(StateType t) {
        this._ctr.changeStateType(t);
    }

    public void onEnter() {
    }

    public void onUpdate(float t) {
    }

    public void onEnd() {
    }

    public void onExit() {
    }
}
