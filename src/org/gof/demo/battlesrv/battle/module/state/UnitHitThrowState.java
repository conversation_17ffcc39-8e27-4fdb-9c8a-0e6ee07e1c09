package org.gof.demo.battlesrv.battle.module.state;

import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.ctroller.MovementCtr;
import org.gof.demo.battlesrv.battle.bentity.ctroller.StateCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.TargetPoint;
import org.gof.demo.battlesrv.battle.enumVo.StateType;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.support.Log;

public class UnitHitThrowState extends State {
    private boolean _changePos;
    private Vector2D _oldPos;
    private float _currenTime = 0;
    private float _duration;
    private float _height;
    private float _dis;

    public UnitHitThrowState(StateCtr ctr, int priority) {
        super(ctr, priority);
        type = StateType.HitThrow;
    }

    private float a(float t) {
        return t * (2 - t);
    }

    @Override
    public void onEnter() {
        UnitHuman t = this._owner;
        t.idle();
        this._ctr.lockCurrenState = true;
        this._ctr.beControlled++;
    }

    public void setThrowHit(float t, float i, int n)//n = 0
    {
        this._duration = i;
        this._changePos = true;
        this._height = t;
        this._oldPos = this._owner.Position;
        this._currenTime = 0;
        this._dis = this._owner.battleMain().hitThrowDis ? n : 0;
        this._owner.animatorctr.changeState(BattleParamKey.UnitConfig_ANIMATOR_JIFEI, false, 1);
    }

    @Override
    public void onUpdate(float t) {
        UnitHuman n = this._owner;
        if (this._changePos) {
            float r = (this._currenTime / this._duration);
            float s = 0;
            float h = 0;
            if (this._height > 0) {
                if (r >= .5f)
                    h = Math.max((1 - r * r), 0);
                else
                    h = (a(r)) * this._height;
            }

            if (0 != this._dis) {
                s = (a(r));
                s = this._dis * s;
            }

            Vector2D u = new Vector2D(s, h).Sum(this._oldPos);
            MovementCtr.Rangle c = n.movectr.limitRangle;
            if (c != null) {
                u.x = u.x > c.y ? c.y : u.x;
                u.x = u.x < c.x ? c.x : u.x;
            }
            n.setNextPoint(TargetPoint.toPos(u, TargetPoint.TP_Control));
            if (this._currenTime >= this._duration) {
                this._currenTime = 0;
                this._changePos = false;
                return;
            }

            this._currenTime = (this._currenTime + t);
        }
        if (!this._changePos) {
            n.setNextPoint(null);
            this.changeState();
        }
    }

    @Override
    public void onExit() {
        this._ctr.beControlled--;
        this._currenTime = 0;
        this._changePos = false;
    }
}
