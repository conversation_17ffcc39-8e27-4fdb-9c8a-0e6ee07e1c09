package org.gof.demo.battlesrv.battle.module.state;

import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.bentity.ctroller.StateCtr;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.enumVo.StateType;

public class UnitBornState extends State {
    private float _curTime;

    public UnitBornState(StateCtr ctr, int priority) {
        super(ctr, priority);
        type = StateType.Born;
    }

    @Override
    public void onEnter() {
        UnitHuman t = this._owner;
        t.animatorctr.changeState(BattleParamKey.UnitConfig_ANIMATOR_CHUXIAN);
        this._curTime = t.data.modelConfig().chuxian_time;
    }

    @Override
    public void onUpdate(float t) {
        if (this._curTime <= 0)
            this.changeState(StateType.Idle);
        else
            this._curTime = /*Mathf.Round*/(this._curTime - t);
    }
}
