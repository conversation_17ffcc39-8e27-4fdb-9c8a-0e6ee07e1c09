package org.gof.demo.battlesrv.battle.module.state;

import org.gof.demo.battlesrv.battle.BattleParamKey;
import org.gof.demo.battlesrv.battle.UnitMgr;
import org.gof.demo.battlesrv.battle.bentity.ctroller.StateCtr;
import org.gof.demo.battlesrv.battle.enumVo.StateType;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;

public class UnitDeadState extends State {
    private float _curTime = 0;
    private float _maxTime = 0;
    private boolean _triggerEvent = true;
    private int _setp = 1;

    public UnitDeadState(StateCtr ctr, int priority) {
        super(ctr, priority);
        type = StateType.Dead;
    }

    @Override
    public void onEnter() {
        StateCtr t = this._ctr;
        t.needChangeState = false;
        t.lockCurrenState = true;
        t.beControlled++;
        this._curTime = 0;

        this._owner.animatorctr.changeState(BattleParamKey.UnitConfig_ANIMATOR_DEAD);

        int[] n = this._owner.config().dead_skills;
        if (n != null && n.length> 0) {
            for (int e : n) {
                Skill i = UnitMgr.newSkill(e, 1, this._owner.data);
                i.triggerEffect = false;
                SkillRunner _n = new SkillRunner();
                _n.init(t.getOwner(), i, true);
                _n.setLockTarget(t.getOwner());
                _n.start();
                t.getOwner().skillctr.addRunner(_n);
            }
        }
    }

    public void setParam(boolean t) {
        this._triggerEvent = t;
        this._maxTime = this._owner.data.modelConfig().deadTime;
        this._setp = 1;
    }

    @Override
    public void onUpdate(float t) {
        if (this._curTime >= this._maxTime) {
            switch (this._setp) {
                case 1:
                    this._owner.visible = false;
                    break;
                case 2:
                    this._owner.destroy();
                    break;
            }
            this._setp++;
        } else
            this._curTime = /*Mathf.Round*/(this._curTime + t);
    }

    @Override
    public void onExit() {
        this._ctr.beControlled--;
    }
}
