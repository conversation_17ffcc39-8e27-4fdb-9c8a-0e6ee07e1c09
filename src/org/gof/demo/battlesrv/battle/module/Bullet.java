package org.gof.demo.battlesrv.battle.module;

import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.skill.SkillRunner;
import org.gof.demo.battlesrv.battle.enumVo.BindType;
import org.gof.demo.battlesrv.battle.enumVo.BulletType;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfBullet;
import org.gof.demo.worldsrv.support.Log;

import java.util.ArrayList;
import java.util.List;

public class Bullet {
    protected SkillRunner _runner;
    protected ConfBullet _config;
    protected UnitHuman _target;
    protected boolean _stop = false;
    protected boolean _destroy = false;
    protected Vector2D _curPoint;
    protected Vector2D _startPoint;
    protected Vector2D _targtPoint;
    protected Vector2D _handlePos;
    protected float _time;

    protected boolean _track = true;
    public List<Object> param = new ArrayList<>();

    private Vector2D a;
    private Vector2D xhx;

    public boolean isStop() {
        return _stop;
    }

    public void c(Vector2D t, float i, float e, float s, float n, float r, float h, float a) {
        float xhx = (1 - a);
        float c = xhx * xhx * i + 2 * a * xhx * s + a * a * r;
        float f = xhx * xhx * e + 2 * a * xhx * n + a * a * h;
        t.x = c;
        t.y = f;
    }

    public static Bullet alloc(SkillRunner i, int e, Vector2D s, UnitHuman o, boolean n)//n = true
    {
        Bullet r = new Bullet();
        r._runner = i;
        r._config = ConfBullet.get(e);
        r._target = o;
        r._curPoint = s;
        if (null == r._config)
            Log.temp.info("弹道配置不存在！{}", e);
        r._targtPoint = o.getBindPos(BindType.fromValue(r._config.end_bind));
        r.reset();
        r._track = n;
        return r;
    }

    public static Bullet allocToPos(SkillRunner i, int e, Vector2D from, Vector2D to) {
        Bullet r = new Bullet();
        r._runner = i;
        r._config = ConfBullet.get(e);
        r._curPoint = from;
        r._targtPoint = to;
        r.reset();
        return r;
    }

    public void reset() {
        ConfBullet t = this._config;
        if (t.type == (int) BulletType.Bezier.getValue()) {
            this._startPoint = this._curPoint;
            this._time = 0;
            this._handlePos = this._startPoint.sum(this._targtPoint);
            this._handlePos.mul(0.5f);
            float i = (float) (this._startPoint.distance(this._targtPoint) / 500f);
            this._handlePos.y = (this._handlePos.y + Math.min(t.hegiht, (t.hegiht * i)));
        }
        this._track = false;
        this._stop = false;
        this._destroy = false;
    }
    //int count =0;
    public void onUpdate(float t) {
        if (this._stop)
            return;
        if (null != this._target && this._target.isDestroy())
            this._stop = true;
        else {
            float i = 20;
            if (this._track) {
                this._targtPoint = this._target.getBindPos(BindType.fromValue(this._config.end_bind));
                i = (i + this._target.data.modelConfig().radius);
            }
            Vector2D e = this._targtPoint;
            //count++;
            if (this._config.type == BulletType.Liner.getValue()) {
                if (this.moveToPoint(e, i, t)) {
                    //Log.battle.info("bulletOnUpdate {} {} {} {} {}", this._config.sn,count,this._target.Position,this._targtPoint,this._curPoint );
                    this._runner.onBulletAction(this, this._target, e, this.param);
                    this._stop = true;
                }
            } else {
                if (this.moveTo(this._startPoint, e, i, t)) {
                    this._runner.onBulletAction(this, this._target, e, this.param);
                    this._stop = true;
                }
            }
        }
    }

    public void destroy() {
        if (this._destroy)
            return;
        this._destroy = true;
    }

    private boolean moveToPoint(Vector2D t, float i, float e) {
        float s = t.Distance(this._curPoint) - i;
        a = t.sub(this._curPoint);
        a.Normalize();
        a.Multiply(Math.min(this._config.speed * e, s));
        this._curPoint = this._curPoint.Sum(a);
        s = t.Distance(this._curPoint) - i;
        return s <= .1f;
    }

    private boolean moveTo(Vector2D t, Vector2D i, float e, float s) {
        Vector2D n = this._handlePos;
        float h = this._time;
        float f = 1f;
        float u = (this._config.speed * s);
        for (float l = (u * u); (f - h) > .01f; ) {
            float d = ((h + f) / 2);
            c(a, (float) t.x, (float) t.y, (float) n.x, (float) n.y, (float) i.x, (float) i.y, d);
            a = a.sub(this._curPoint);
            if (a.lengthSquared()/*a.sqrMagnitude*/ > l)
                f = d;
            else
                h = d;
        }

        this._time = ((h + f) / 2f);
        boolean P = ((float) (i.distance(this._curPoint)) - e) <= .1f;
        if (this._time >= .9999 || P)
            return true;

        c(a, (float) t.x, (float) t.y, (float) n.x, (float) n.y, (float) i.x, (float) i.y, this._time);
        xhx = a.sub(this._curPoint);
        this._curPoint = a;
        return false;
    }
}