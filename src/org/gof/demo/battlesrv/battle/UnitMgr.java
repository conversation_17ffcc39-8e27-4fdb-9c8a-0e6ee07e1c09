package org.gof.demo.battlesrv.battle;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.bentity.*;
import org.gof.demo.battlesrv.battle.module.MetaAttrib;
import org.gof.demo.battlesrv.battle.module.skill.Skill;
import org.gof.demo.battlesrv.battle.module.UnitData;
import org.gof.demo.battlesrv.battle.enumVo.*;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfAttribute;
import org.gof.demo.worldsrv.config.ConfSkill;
import org.gof.demo.worldsrv.config.ConfSkillLevel_0;
import org.gof.demo.worldsrv.config.ConfUnit;
import org.gof.demo.worldsrv.entity.Unit;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.support.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UnitMgr {
    protected Map<Integer, UnitHuman> _entitys = new HashMap<>();
    protected List<UnitHuman> _entityList = new ArrayList<>();
    protected List<UnitHuman> _filterUnitList = new ArrayList<>();
    protected BattleMain battleMain;
    protected int _tempId = 0;
    protected List<UnitHuman> objectsToDestroy = new ArrayList<>();
    protected boolean interruptUpdate = false;

    public List<UnitHuman> entityList() {
        return _entityList;
    }

    public UnitMgr(BattleMain battleMain) {
        this._tempId = 0;
        this.interruptUpdate = false;
        this.battleMain = battleMain;
    }

    private int _getValidId() {
        return ++this._tempId;
    }

    public <T extends UnitHuman> T createUnit(UnitData i, int e, boolean n) {
        int a = _getValidId();

        try {
//            T o = (T) createUnitHuman((Class<T>) i.getClass().getInterfaces()[0], this.battleMain);
            T o = (T)new UnitHuman(this.battleMain);

            o.unitId = a;
            o.teamId = e;
            i.unitId = a;

            i.attack = new Skill();
            i.attack.config = ConfSkill.get(i.unitType().att_skill);
            i.attack.currenCd = 1f / i.attribs.get(AttribDefine.att_speed).getValue();
            if (i.unitType().counter > 0) {
                i.counterAttack = new Skill();
                i.counterAttack.config = ConfSkill.get(i.unitType().counter);
            }

            if (0 == i.currenHp)
                i.currenHp = i.getAttribByInt(AttribDefine.hp);
            i.maxHp = i.getAttribByInt(AttribDefine.hp);
            o.init(i);

            _entitys.put(a, o);
            _entityList.add(o);

            return o;
        } catch (Exception err) {
            Log.temp.error("[battle] createUnit {}", err.getStackTrace());
        }
        return null;
    }

    public <T extends UnitHuman> T _createUnit(UnitData i, int e, boolean n, Class<T> clazz) {
        int a = _getValidId();

        try {
            T o = null;
            if(clazz.equals(UnitHuman.class)){
                UnitHuman uhN = new UnitHuman(this.battleMain);
                o = (clazz.cast(uhN));
            } else if(clazz.equals(UnitCall.class)){
                o = (clazz.cast(new UnitCall(this.battleMain)));
            } else if(clazz.equals(UnitT20Player.class)){
                o = (clazz.cast(new UnitT20Player(this.battleMain)));
            } else if(clazz.equals(UnitT20Boss.class)){
                o = (clazz.cast(new UnitT20Boss(this.battleMain)));
            } else if(clazz.equals(UnitDLPlayer.class)){
                o = (clazz.cast(new UnitDLPlayer(this.battleMain)));
            } else if(clazz.equals(UnitDPVPPlayer.class)){
                o = (clazz.cast(new UnitDPVPPlayer(this.battleMain)));
            }
            if(o == null){
                return null;
            }

            o.unitId = a;
            o.teamId = e;
            i.unitId = a;
            if (i.unitType().att_skill > 0) {
                i.attack = new Skill();
                i.attack.config = ConfSkill.get(i.unitType().att_skill);
                i.attack.currenCd = 1f / i.attribs.get(AttribDefine.att_speed).getValue();
            }
            if (i.unitType().counter > 0) {
                i.counterAttack = new Skill();
                i.counterAttack.config = ConfSkill.get(i.unitType().counter);
            }

            if (0 == i.currenHp)
                i.currenHp = i.getAttribByInt(AttribDefine.hp);
            i.maxHp = i.getAttribByInt(AttribDefine.hp);
            o.init(i);


            _entitys.put(a, o);
            _entityList.add(o);

            return o;
        } catch (Exception e1) {
            Log.temp.error("[battle] _createUnit {}", e1);
            return null;
        }
    }

    public <T extends UnitHuman> T _createUnit(UnitData i, int e, Class<T> clazz) {
        return _createUnit(i, e, false, clazz);
    }


//        private T _createUnit<T>(UnitData i, int e = 0, boolean n = false) where T : Unit
//        {
//            var a = this._getValidId();
//            var o = (T)Activator.CreateInstance(typeof(T), this.battleMain);
//            o.unitId = a;
//            o.teamId = e;
//            i.unitId = a;
//
//            i.attack = new Skill();
//            i.attack.config = ConfSkill.get(i.unitType().att_skill);
//            i.attack.currenCd = 1f / i.attribs[AttribDefine.att_speed].Value;
//            if (i.unitType().counter > 0)
//            {
//                i.counterAttack = new Skill();
//                i.counterAttack.config = ConfSkill.get(i.unitType().counter );
//            }
//
//            if (0 == i.currenHp)
//                i.currenHp = i.getAttribByInt(AttribDefine.hp);
//            i.maxHp = i.getAttribByInt(AttribDefine.hp);
//            o.Init(i);
//
//            this._entitys.add(a, o);
//            this._entityList.add(o);
//
//            return o;
//        }

//        private T _addUnit1<T>(ConfUnit e, int r = 0, Object a = null) where T : Unit
//        {
//            var s = new UnitData();
//            s.config = e;
//
//            var d = ConfAttribute.Array.Where(i => i.module == 1);
//            foreach (var f in d)
//            {
//                var h = new MetaAttrib(f);
//                h.BaseValue = e[f.key];
//                s.attribs[(AttribDefine)f.sn] = h;
//            }
//            for (var p = 0; p < e.skills.Length; p++)
//            {
//                var g = e.skills[p];
//                addSkill(s, g, 1);
//            }
//            for (var y = 0; y < e.passiveSkills.Length; y++)
//            {
//                var v = e.passiveSkills[y];
//                addSkill(s, v, 1);
//            }
//            return this._createUnit<T>(s, r);
//        }

    public <T extends UnitHuman> T _addUnit1(ConfUnit e, int r, Object a, Class<T> clazz) {//r = 0, object a = null
        UnitData s = new UnitData();
        s.setConfig(e);

        for (Integer sn : GlobalConfVal.propNameSnModule1Map.values()) {
            ConfAttribute f = ConfAttribute.get(sn);
            MetaAttrib h = new MetaAttrib(f, null);
            Object value = e.getFieldValue(f.key);
            h.setBaseValue(value ==null? -1: Utils.floatValue(value));
            s.attribs.put(AttribDefine.fromValue(f.sn), h);
        }
        if (e.skills != null) {
            for (int p = 0; p < e.skills.length; p++) {
                int g = e.skills[p];
                addSkill(s, g, 1);
            }
        }
        if (e.passiveSkills != null) {
            for (int y = 0; y < e.passiveSkills.length; y++) {
                int v = e.passiveSkills[y];
                addSkill(s, v, 1);
            }
        }
        return this._createUnit(s, r, clazz);
    }

//        private T _addUnit<T>(long confId, int e = 0) where T : Unit
//        {
//            return this._addUnit1<T>(ConfUnit.get(confId), e);
//        }

    public <T extends UnitHuman> T _addUnit(long confId, int e, Class<T> clazz) {//e = 0
        return _addUnit1(ConfUnit.get(confId), e, null, clazz);
    }

//        public UnitCall addUnitCall(long confId, int e)//e = 0
//        {
//            return this._addUnit<UnitCall>(confId, e);
//        }

    public UnitT20Boss addT20Boss(long confId, int e)//e = 0
    {
        return this._addUnit1(ConfUnit.get(confId), e, null, UnitT20Boss.class);
    }

    public UnitCall addUnitCall(long confId, int e)//e = 0
    {
        return this._addUnit(confId, e, UnitCall.class);
    }

    public UnitCall addUnitImageCall(UnitData data, int i)//i = 0
    {
//            return this._createUnit<UnitCall>(data, i);
        return this._createUnit(data, i, UnitCall.class);
    }

    public static Skill newSkill(int t, int i, UnitData o) {
        ConfSkill e = ConfSkill.get(t);
        if (null == e)
            Log.temp.error("[battle]技能表ConfSkill:{}缺少配置", t);
        else if (e.type != SkillType.PASSIVE_ADD.getValue()) {
            Skill n = new Skill();
            n.config = e;
            n.level = i;
            n.currenPower = e.initialPower;
            n.currenCd = 0;
            n.checkState();
            if (i > 0) {
                ConfSkillLevel_0 r = ConfSkillLevel_0.get(t, i);
                if (null == r) {
                    Log.temp.error("[battle]技能等级表:ConfSkillLevel_0:{}缺少配置", t);
                    return null;
                }
                if(r.skillCoefficient != null) {
                    int[] skillCoefficient = new int[r.skillCoefficient.length];
                    for (int j = 0; j < r.skillCoefficient.length; j++)
                        skillCoefficient[j] = (int)o.getSkillFactAttrValue(r.skillCoefficient[j], t, 1063);
                    n.skillDam = skillCoefficient;
                }else
                    n.skillDam =new int[0];
            }
            return n;
        }
        return null;
    }

    public static Skill addSkill(UnitData t, int i, int e) {
        Skill r = newSkill(i, e,t);
        if (null == r)
            return null;
        ConfSkill a = r.config;
        if (a.type == (int) SkillType.PASSIVE_EFFECT.getValue()) {
            if (t.passiveSkillList == null)
                t.passiveSkillList = new ArrayList<>();
            t.passiveSkillList.add(r);
        } else if (a.type == (int) SkillType.PARTNER_SKILL.getValue()) {
            if (t.petPassiveSkillList == null)
                t.petPassiveSkillList = new ArrayList<>();
            t.petPassiveSkillList.add(r);
        } else {
            if (t.skillList == null)
                t.skillList = new ArrayList<>();
            t.skillList.add(r);
        }
        return r;
    }

    public UnitHuman addPlayer(UnitData t) {
        return addPlayer(t, 0);
    }

    public UnitHuman addPlayer(UnitData t, int i) {
//            return this._createUnit<UnitHuman>(t, i);
        return this._createUnit(t, i, UnitHuman.class);
    }

    public UnitHuman addT20Player(UnitData t, int i)// i = 0
    {
        return this._createUnit(t, i, UnitT20Player.class);
    }

    public UnitHuman addDLPlayer(UnitData t, int i)
    {
        return this._createUnit(t, i, UnitDLPlayer.class);
    }

    public UnitHuman addDPVPPlayer(UnitData t, int i)
    {
        return this._createUnit(t, i, UnitDPVPPlayer.class);
    }

    private void _onUpdate(float t, String i) {
        for (int e = 0; e < this._entityList.size(); ++e) {
            UnitHuman n = this._entityList.get(e);
            if (n.isDestroy()) {
                this._entityList.remove(e);
                this._entitys.remove(n.unitId);
                e--;
            } else {
                if (i.equals("onThink"))
                    n.onThink(t);
                else if (i.equals("onUpdate"))
                    n.OnUpdate(t);
                else if (i.equals("onLastUpdate"))
                    n.OnLastUpdate(t);
            }
        }
    }

    public void updateDepth() {
        this._entityList.sort((t, i) ->
        {
            int e = (int)(100 * t.Position.y) + (t.statectr.currentStateType() == StateType.Skill ? 10 : 0);
            int n = (int)(100 * i.Position.y) + (i.statectr.currentStateType() == StateType.Skill ? 10 : 0);
            if (i.Position.y == t.Position.y)
            {
                e = (int)(100 * t.Position.x) + (t.statectr.currentStateType() == StateType.Skill ? 10 : 0);
                n = (int)(100 * i.Position.x) + (i.statectr.currentStateType() == StateType.Skill ? 10 : 0);
            }
            return n - e;
        });

//        for (int t = 0; t < this._entityList.size(); ++t)
//            this._entityList.get(t).depth = t;
    }

    public void onUpdate(float t, Object i)//i = null
    {
        if (this._entityList.isEmpty())
            return;
        this._onUpdate(t, "onThink");
        this._onUpdate(t, "onUpdate");
        this._onUpdate(t, "onLastUpdate");
        if (this._entityList.size() >= 2)
            this.updateDepth();
    }

    public void battleOver() {
        for (int t = 0; t < this._entityList.size(); ++t) {
            UnitHuman i = this._entityList.get(t);
            i.statectr.lockCurrenState = false;
            i.statectr.changeStateType(StateType.Over);
        }
    }

    public void playerGroupDead(long t) {
        for (int i = 0; i < this._entityList.size(); ++i) {
            UnitHuman e = this._entityList.get(i);
            if (e.isDead() || e.data.roleId == t)
                e.dead(true);
        }
    }

    public UnitHuman getUnit(int t) {
        return this._entitys.containsKey(t) ? this._entitys.get(t) : null;
    }

    public List<UnitHuman> findTarget(UnitHuman t, TargetFilter i, float e, boolean r) {
        return findTarget(t, i, e, t.Position, r);
    }

    public List<UnitHuman> findTarget(UnitHuman t, TargetFilter i, float e, Vector2D n) {
        return findTarget(t, i, e, n, true);
    }

    public List<UnitHuman> findTarget(UnitHuman t, TargetFilter i, float e, Vector2D n, boolean r)// e=0, n=t.Position, r=true
    {
        List<UnitHuman> s = new ArrayList<>();
        if(t==null){
            Log.game.info("findTarget t=={}, {}, {},{},{}", t, i, e, n, r);
            return s;
        }
        for (int o = 0; o < this._entityList.size(); ++o) {
            UnitHuman l = this._entityList.get(o);
            if (!l.isDestroy() && !(l.isDead() || r && l.statectr.currentStateType() == StateType.Born)) {
                if (l != t || 0 == (i.getValue() & TargetFilter.Cast.getValue())) {
                    if (0 != (i.getValue() & TargetFilter.Enemy.getValue()) && t.teamId != l.teamId) {
                        if (1 == l.data.unitType().target)
                            continue;
                        if (l.shamUnit)
                            continue;
                        this.filterTarget(s, t, l, n, e);
                    }

                    if (t != l && 0 != (i.getValue() & TargetFilter.CastPartner.getValue())
                            && l.config().type == (int) UnitType.Partner.getValue() && t.teamId == l.teamId && t.data.roleId == l.data.roleId)
                        this.filterTarget(s, t, l, n, e);
                    if (!(t == l || 0 == (i.getValue() & TargetFilter.CastPlayer.getValue()) || l.config().type != UnitType.Player.getValue() || t.teamId != l.teamId || t.data.roleId != l.data.roleId || l.isCallType))
                        this.filterTarget(s, t, l, n, e);
                    if (t != l && 0 != (i.getValue() & TargetFilter.FriendPlayer.getValue())
                            && l.config().type == (int) UnitType.Player.getValue() && t.teamId == l.teamId && t.data.roleId != l.data.roleId)
                        this.filterTarget(s, t, l, n, e);
                    if (t != l && 0 != (i.getValue() & TargetFilter.FriendPartner.getValue())
                            && l.config().type == (int) UnitType.Partner.getValue() && t.teamId == l.teamId && t.data.roleId != l.data.roleId)
                        this.filterTarget(s, t, l, n, e);
                    if (t != l && 0 != (i.getValue() & TargetFilter.Friend.getValue()) && t.teamId == l.teamId)
                        this.filterTarget(s, t, l, n, e);
                    if (t != l && 0 != (i.getValue() & TargetFilter.EnemyPartner.getValue())
                            && l.config().type == (int) UnitType.Partner.getValue() && t.teamId != l.teamId && t.data.roleId != l.data.roleId)
                        this.filterTarget(s, t, l, n, e);
                    if (t != l && 0 != (i.getValue() & TargetFilter.EnemyPlayer.getValue()) && l.config().type == UnitType.Player.getValue() && t.teamId != l.teamId && t.data.roleId != l.data.roleId)
                        this.filterTarget(s, t, l, n, e);
                    if (t != l && 0 != (i.getValue() & TargetFilter.EnemyBoss.getValue()) && l.config().type == UnitType.Boss.getValue() && t.teamId != l.teamId && t.data.roleId != l.data.roleId)
                        this.filterTarget(s, t, l, n, e);
                    if (t != l && 0 != (i.getValue() & TargetFilter.FriendCall.getValue()) && t.teamId == l.teamId && l.isCallType && l.parent == t)
                        this.filterTarget(s, t, l, n, e);
                } else
                    s.add(t);
            }
        }

        return s;
    }

    public void filterTarget(List<UnitHuman> t, UnitHuman i, UnitHuman e, Vector2D n, float a)//float a = 0
    {
        boolean s = 0 == a;
        if (a > 0) {
            float o = MathUtil.DistanceX(n, e.Position);
            o = (o - (i.radiusSize + e.radiusSize));
            s = o <= a;
        }
        if (s)
            t.add(e);
    }

    public List<UnitHuman> getTargetList(UnitHuman t, List<UnitHuman> i, int e, TargetSelectFilter n) {
        if (0 == i.size())
            return i;
        if (e < 1 || e > 0 && e >= i.size() && n != TargetSelectFilter.HpSmallTarget && n != TargetSelectFilter.HpBigTarget)
            return i;
        switch (n) {
            case NotFilter:
                break;
            case NearTarget:
            case RandomTarget:
                this.sortTargetListByDistance(t, i, true);
                break;
            case FarTarget:
                this.sortTargetListByDistance(t, i, false);
                break;
            case MinHpTarget:
                this.sortTargetListByHp(t, i, true);
                break;
            case MaxHpTarget:
                this.sortTargetListByHp(t, i, false);
                break;
            case HpSmallTarget:
                this.getTargetListwithHp(t, i, false);
                break;
            case HpBigTarget:
                this.getTargetListwithHp(t, i, true);
                break;
            case LessHpValueTarget:
                this.getTargetListwithCurrenHp(t, i, false);
                break;
            case MoreHpValueTarget:
                this.getTargetListwithCurrenHp(t, i, true);
                break;
            case LessAttTarget:
                this.getTargetListwithCurrenAtt(t, i, false);
                break;
            case MoreAttTarget:
                this.getTargetListwithCurrenAtt(t, i, true);
                break;
        }
        for (int r = e; r < i.size(); )
            i.remove(r);
        return i;
    }

    public void getTargetListwithCurrenAtt(UnitHuman t, List<UnitHuman> i, boolean e)
    {
        float n = t.data.getAttribMeta(AttribDefine.att).getValue();
        List<UnitHuman> removeList = new ArrayList<>();
        for (int r = 0; r < i.size(); r++)
        {
            UnitHuman s = i.get(r);
            float l = s.data.getAttribMeta(AttribDefine.att).getValue();
            if (e && n < l)
                continue;
            if (!e && n > l)
                continue;
            removeList.add(s);
        }
        i.removeAll(removeList);
    }

    public void getTargetListwithCurrenHp(UnitHuman t, List<UnitHuman> i, boolean e)
    {
        long n = t.data.currenHp;
        List<UnitHuman> removeList = new ArrayList<>();
        for (int r = 0; r < i.size(); r++)
        {
            UnitHuman s = i.get(r);
            long o = s.data.currenHp;
            if (e && n < o)
                continue;
            if (!e && n > o)
                continue;
            removeList.add(s);
        }
        i.removeAll(removeList);
    }

    public void getTargetListwithHp(UnitHuman t, List<UnitHuman> i, boolean e) {
        double n = FixRandom.roundInt(100 * (t.data.currenHp / t.data.getAttrib(AttribDefine.hp)));
        List<UnitHuman> removeList = new ArrayList<>();
        for (int a = 0; a < i.size(); a++) {
            UnitHuman o = i.get(a);
            double l = FixRandom.roundInt(100 * (o.data.currenHp / o.data.getAttrib(AttribDefine.hp)));
            if (e && n < l)
                continue;
            if (!e && n > l)
                continue;
            removeList.add(o);
//                i.RemoveAt(a);
//                a--;
        }
        i.removeAll(removeList);
    }

    public void sortTargetListByHp(UnitHuman t, List<UnitHuman> i, boolean e) {
        i.sort((unitHuman, n) ->
        {
            double a = FixRandom.roundInt(100 * (unitHuman.data.currenHp / unitHuman.data.getAttrib(AttribDefine.hp)));
            double o = FixRandom.roundInt(100 * (n.data.currenHp / n.data.getAttrib(AttribDefine.hp)));
            if (a == o) {
                double it = unitHuman.Position.distance(t.Position);
                double nt = n.Position.distance(t.Position);
                if (Math.floor(it) > Math.floor(nt)) {
                    if (e)
                        return a > o ? 1 : -1;
                }
            }
            return o < a ? 1 : -1;
        });
    }

    public void sortTargetListByDistance(UnitHuman t, List<UnitHuman> i, boolean e) {
        i.sort((m, n) ->
        {
            double it = m.Position.distance(t.Position);
            double nt = n.Position.distance(t.Position);
            if (Math.floor(it) > Math.floor(nt))
                return 1;
            return -1;
        });
    }

    public void destroyUnit(UnitHuman t) {
        this.objectsToDestroy.add(t);
    }

    public void deferredDestroy() {
        List<UnitHuman> t = this.objectsToDestroy;
        int i = t.size();
        for (int e = 0; e < i; ++e) {
            t.get(e).destroyImmediate();
        }
        if (i == t.size())
            t.clear();
        else
//                t.RemoveRange(0, i);
            t.subList(0, i).clear(); // 移除索引从0到i-1的元素
    }

    public void clear() {
        this._tempId = 0;
        for (int t = 0; t < this._entityList.size(); ++t) {
            this._entityList.get(t).destroy();
        }
        this._filterUnitList.clear();
        this._entityList.clear();
        this._entitys.clear();
        this.deferredDestroy();
    }
}
