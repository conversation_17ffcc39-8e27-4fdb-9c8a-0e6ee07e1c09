package org.gof.demo.battlesrv.battle;

import org.gof.demo.battlesrv.support.Vector2D;

public class MathUtil {
    public static float DistanceX(Vector2D t, Vector2D n) {
        double r = (n.x - t.x);
        return (float) (Math.abs(r));
    }

    public static boolean AND(int a, int b) {
        return (a & b) > 0;
    }

//    public static boolean AND(int a, Enum b)
//    {
//        return (a & Convert.ToInt32(b)) > 0;
//    }
}
