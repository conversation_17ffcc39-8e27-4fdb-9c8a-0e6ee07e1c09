package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.BattleMainServer;

public class ChapterCrossWarServer extends ChapterCrossWar {
    public ChapterCrossWarServer(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public boolean toResult(int i, boolean b)//b = false
    {
        if (this.battleMain instanceof BattleMainServer){
            long a = this.battleMain.mainCtr != null && this.battleMain.mainCtr.player != null && this.battleMain.mainCtr.player.data != null ? this.battleMain.mainCtr.player.data.currenHp : 0;
            long e = this.arenaPlayerCtr != null && this.arenaPlayerCtr.player != null && this.arenaPlayerCtr.player.data != null ? this.arenaPlayerCtr.player.data.currenHp : 0;
            ((BattleMainServer) (this.battleMain)).toResult(a >= e ? 0 : 1, b);
        }
        return false;
    }
}