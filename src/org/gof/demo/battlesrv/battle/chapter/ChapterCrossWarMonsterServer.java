package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.BattleMainServer;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;

public class ChapterCrossWarMonsterServer extends ChapterCrossWarMonster {
    public ChapterCrossWarMonsterServer(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public boolean toResult(int t, boolean r)//a = false
    {
        BattleMainServer a = this.battleMain instanceof BattleMainServer ? (BattleMainServer) (this.battleMain) : null;
        long e = this.battleMain.mainCtr != null && this.battleMain.mainCtr.player != null && this.battleMain.mainCtr.player.data != null ? this.battleMain.mainCtr.player.data.currenHp : 0;
        long s = this.bossUnit != null && this.bossUnit.data != null ? this.bossUnit.data.currenHp : 0;
        a.toResult(e >= s ? 0 : 1, r);
        return false;
    }
}