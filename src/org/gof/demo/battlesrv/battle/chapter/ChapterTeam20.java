package org.gof.demo.battlesrv.battle.chapter;

import org.gof.core.support.Utils;
import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.PlayerData;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.bentity.UnitT20Boss;
import org.gof.demo.battlesrv.battle.enumVo.AttribDefine;
import org.gof.demo.battlesrv.battle.enumVo.UnitType;
import org.gof.demo.battlesrv.battle.module.UnitData;
import org.gof.demo.battlesrv.battle.module.UnitGroupCtr;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.instance.ConfChapterVO;
import org.gof.demo.worldsrv.instance.InstanceConstants;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ChapterTeam20 extends BaseChapter {
    protected int playerDeadNum;
    protected List<ConfUnit> bossList = new ArrayList<>();
    public UnitT20Boss bossUnit;

    public ChapterTeam20(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public void destroy() {
    }

    @Override
    public void init() {
//        battleMain.on(EventDefine.UnitDead, this::onUnitDead);
//        battleMain.on(EventDefine.T20AddPlayer, this::onAddPlayer);
    }

    @Override
    public void setChapter(int id, boolean e) {
        super.setChapter(id, e);
        for (ConfLeagueGveChapter conf : ConfLeagueGveChapter.findAll()){
            if (conf.index == this.config.index && conf.sn >= id) {
                bossList.add(ConfUnit.get(Utils.longValue(conf.bossID)));
            }
        }
    }

    @Override
    public ConfChapterVO getConfig(int id) {
        return new ConfChapterVO(ConfLeagueGveChapter.get(id));
    }

    @Override
    public void loadData(float t) {
        BattleMain i = this.battleMain;
        for (Map.Entry<Long, PlayerData> a : i.data.playerList.entrySet())
            this.addPlayer(a.getValue(), t);

        if (i.mainCtr == null) i.mainCtr = i.playerCtrs.get(0);
        i.showMainCtr = i.mainCtr;
    }

    public void addPlayer(PlayerData t, float e) {
        int[][] i = ConfChapterType.get(InstanceConstants.LEAGUEGVECHAPTER_7).player_points;
        UnitGroupCtr a = new UnitGroupCtr();
        if(t.Pos <= 0){
            t.Pos = 1;
        }
        if (t.Pos <= i.length) {
            a.offsetY = i[t.Pos - 1][1];
            a.offsetX = -i[t.Pos - 1][0];
        } else {
//            this.battleMain.printLogErr("ChapterTeam20.addPlayer error, playerData " + JsonUtil.ToJson(t) + ", points " + JsonUtil.ToJson(i));
            a.offsetY = i[0][1];
            a.offsetX = -i[0][0];
        }
        a.playerData = t;
        List<UnitData> n = t.units;
        for (int s = 0; s < n.size(); s++) {
            UnitHuman h = null;
            if (n.get(s).config().type == UnitType.Player.getValue()) {
                h = this.battleMain.unitMgr.addT20Player(n.get(s), 0);
                a.player = h;
            }
            a.units.add(h);
        }
        a.positionSelected(e, true);
        this.battleMain.playerCtrs.add(a);
    }

    public void onAddPlayer(PlayerData t) {
        BattleMain e = this.battleMain;
        e.data.playerList.put(t.Id, t);
        this.addPlayer(t, 0);
    }

    private void onUnitDead(UnitHuman t) {
        BattleMain e = this.battleMain;
        if (!t.isCallType && t.config().type == UnitType.Player.getValue()) {
            this.playerDeadNum++;
            for (UnitGroupCtr a : e.playerCtrs) {
                if (a.playerData.Id == t.data.roleId) {
                    e.unitMgr.playerGroupDead(t.data.roleId);
                    break;
                }
            }
        }
    }

    protected UnitT20Boss addBoss() {
        ConfLeagueGveChapter t = ConfLeagueGveChapter.get(this.config.sn);
        UnitT20Boss e = this.battleMain.unitMgr.addT20Boss(t.bossID, 1);
        e.data.currenHp = e.data.getAttribByInt(AttribDefine.hp);
        return e;
    }

    @Override
    public void start() {
        BattleMain t = this.battleMain;
        UnitT20Boss e = this.addBoss();
        e.units = this.bossList;

        for (int[] r : this.mapConfig.points) {
            if (1 == r[0]) {
                e.Position = new Vector2D(r[1], r[2]);
                break;
            }
        }
        e.direction = -1;
        this.bossUnit = e;
        //battleEvent.emit(EventDefine.BossHpChange, e, 0L);
    }

    @Override
    public void toNextPart(ConfChapterVO t) {
    }

    private boolean checkPlayerDead() {
        //if (GlobalDefine.CLIENT_TYPE) {
            List<UnitGroupCtr> t = this.battleMain.playerCtrs;
            return this.playerDeadNum >= t.size();
        //}
        //return false;
    }

    @Override
    public void onUpdate(float t) {
        if (!this.over) {
            if (this.checkPlayerDead()) {
                this.over = true;
                this.toResult(1);
                return;
            }
            if (this.bossUnit.isDead()) {
                this.over = true;
                this.toResult(0);
                return;
            }
            if (this.timeModel) {
                if (this.chapterTime <= 0) {
                    this.over = true;
                    this.toResult(this.bossUnit.hpNum > 0 ? 0 : 1, true);
                    return;
                }
                this.chapterTime -= t;
                this.chapterTime = Math.max(this.chapterTime, 0);
            }
        }
    }

}
