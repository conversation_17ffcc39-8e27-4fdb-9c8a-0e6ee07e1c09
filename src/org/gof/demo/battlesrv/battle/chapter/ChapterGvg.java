package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.worldsrv.config.ConfFamilyBrawlChapter;
import org.gof.demo.worldsrv.config.ConfFamliyGvgBufflist;
import org.gof.demo.worldsrv.instance.ConfChapterVO;
import org.gof.demo.worldsrv.support.Log;

import java.util.List;

public class ChapterGvg extends ChapterArena {
    public ChapterGvg(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public ConfChapterVO getConfig(int id) {
        return new ConfChapterVO(ConfFamilyBrawlChapter.get(id));
    }

    @Override
    public void loadData(float t) {
        super.loadData(t);
        this.addBuff(this.battleMain.mainCtr.player);
        this.addBuff(this.arenaPlayerCtr.player);
    }

    private void addBuff(UnitHuman t) {
        if(t == null){
            return;
        }
        if (t.data == null) {
            return;
        }
        List<Integer> a = t.data.buffs;
        if (null != a && !a.isEmpty()) {
            for (int b : a) {
                ConfFamliyGvgBufflist e = ConfFamliyGvgBufflist.get(b);
                if (null == e)
//                        Console.WriteLine("GVG副本buff:" + a + "不存在!!!");
                    Log.temp.error("===GVG副本ConfFamliyGvgBufflist，不存在 {}, {}", b, a);
                else
                    t.skillctr.addChapterBuff(e.skill_id);
            }
        }
    }

    @Override
    public boolean updateTime(float t) {
        if (this.timeModel) {
            if (this.chapterTime <= 0) {
                this.over = true;
                UnitHuman a = this.arenaPlayerCtr.player;
                long e = a.data.currenHp;
                long i = (a = this.battleMain.mainCtr.player).data.currenHp;
                this.toResult(i >= e ? 0 : 1, true);
                return true;
            }
            this.chapterTime -= t;
            this.chapterTime = Math.max(this.chapterTime, 0);
        }
        return false;
    }
}