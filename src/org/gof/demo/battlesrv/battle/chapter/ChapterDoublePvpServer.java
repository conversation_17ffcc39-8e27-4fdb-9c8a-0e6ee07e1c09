package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.BattleMainServer;
import org.gof.demo.worldsrv.config.ConfPvpCompetitionChapter;
import org.gof.demo.worldsrv.instance.ConfChapterVO;

public class ChapterDoublePvpServer extends ChapterMultipleArena {
    public ChapterDoublePvpServer(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public ConfChapterVO getConfig(int id) {
        return new ConfChapterVO(ConfPvpCompetitionChapter.get(id));
    }

    @Override
    public boolean toResult(int i, boolean b )// b= false
    {
        if((this.battleMain instanceof BattleMainServer)){
            ((BattleMainServer)this.battleMain).toResult(i, b);
        }
        return false;
    }

}