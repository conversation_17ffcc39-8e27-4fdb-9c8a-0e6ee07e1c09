package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.BattleMainServer;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.worldsrv.config.ConfFamilyBrawlChapter;
import org.gof.demo.worldsrv.config.ConfFamliyGvgBufflist;
import org.gof.demo.worldsrv.instance.ConfChapterVO;
import org.gof.demo.worldsrv.support.Log;

import java.util.List;

public class ChapterTeam20Server extends ChapterTeam20 {
    public ChapterTeam20Server(BattleMain battleMain) {
        super(battleMain);
    }
    @Override
    public boolean toResult(int i, boolean b )// b= false
    {
        if((this.battleMain instanceof BattleMainServer)){
            ((BattleMainServer)this.battleMain).toResult(i, b);
        }
        return false;
    }
}