package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.worldsrv.config.ConfParkCrossPvpChapter;
import org.gof.demo.worldsrv.instance.ConfChapterVO;

import java.util.List;

public class ChapterCrossParkPvp extends ChapterArena {
    public ChapterCrossParkPvp(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public ConfChapterVO getConfig(int id) {
        return new ConfChapterVO(ConfParkCrossPvpChapter.get(id));
    }

    @Override
    public void loadData(float t) {
        super.loadData(t);

        this.addBuff(this.battleMain.mainCtr.player);
        this.addBuff(this.arenaPlayerCtr.player);
    }

    private void addBuff(UnitHuman t) {
        List<Integer> e = t.data.buffs;
        if (null != e && 0 != e.size())
            for (int _e : e)
                t.skillctr.addChapterBuff(_e);
    }

    @Override
    public void onUpdate(float t) {
        if (!this.over) {
            BattleMain e = this.battleMain;
            if (this.arenaPlayerCtr.player.dead) {
                this.over = true;
                this.toResult(0);
                return;
            }
            if (e.mainCtr.player.dead) {
                this.over = true;
                this.toResult(1);
                return;
            }
            this.updateTime(t);
        }
    }
}