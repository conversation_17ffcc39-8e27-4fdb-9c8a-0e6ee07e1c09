package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.UnitData;
import org.gof.demo.battlesrv.battle.module.UnitGroupCtr;
import org.gof.demo.battlesrv.battle.PlayerData;
import org.gof.demo.battlesrv.battle.enumVo.UnitType;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.config.ConfLevel;
import org.gof.demo.worldsrv.instance.ConfChapterVO;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.util.List;

public class ChapterArena extends BaseChapter {
    public UnitGroupCtr arenaPlayerCtr;

    public ChapterArena(BattleMain battleMain) {
        super(battleMain);
    }

    public void init() {
    }

    public void destroy() {
    }


    public void start() {
        BattleMain t = this.battleMain;
        t.hitThrowDis = false;
        int e = t.mainCtr.player.data.level;
        int a = this.arenaPlayerCtr.player.data.level;
        int i = Math.round((e + a) / 2.0f);
        ConfLevel o = ConfLevel.get(i);
        this.battleMain.injuryReduce = o.pvpInjuryReduce / 10000f;
        this.battleMain.shieldDecay = ConfGlobal.get(ConfGlobalKey.shield_correct.SN).value / 10000f;
        this.battleMain.treatDecay = ConfGlobal.get(ConfGlobalKey.hp_recovery_correct.SN).value / 10000f;
    }

    private UnitGroupCtr _addPlayer(float t, int a, PlayerData data, int teamId) {
        BattleMain n = this.battleMain;
        UnitGroupCtr o = new UnitGroupCtr();
        o.offsetY = 160;
        o.type = a;
        o.playerData = data;
        List<UnitData> l = data.units;
        for (UnitData unitData : l) {
            UnitHuman c = n.unitMgr.addPlayer(unitData, teamId);
            c.direction = 1 == a ? -1 : 1;
            if (c.config().type == UnitType.Player.getValue()) {
                o.player = c;
            }
            if (c.config().type == UnitType.FlyPet.getValue()) {
                o.player.flyPet = c;
            }
            o.units.add(c);
        }
        o.positionSelected(t + a == 1 ? 720 : 0, true);
        return o;
    }

    @Override
    public void loadData(float t) {
        BattleMain e = this.battleMain;
        int a = e.data.playerImage ? 1 : 0;
        UnitGroupCtr r = this._addPlayer(t, a, e.data.playerList.get(1L), 0);
        e.playerCtrs.add(r);
        e.mainCtr = r;
        e.showMainCtr = r;
        a = e.data.playerImage ? 0 : 1;
        this.arenaPlayerCtr = this._addPlayer(t, a, e.data.playerList.get(2L), 1);
        if (e.data.playerImage)
            e.showMainCtr = this.arenaPlayerCtr;
    }

    @Override
    public void toNextPart(ConfChapterVO t) {
    }

    @Override
    public void onUpdate(float t) {
        if (!this.over) {
            if (this.battleMain.mainCtr.player.dead) {
                this.over = true;
                this.toResult(1);
                return;
            }
            if (!this.updateTime(t)) {
                if (this.arenaPlayerCtr.player.dead) {
                    this.over = true;
                    this.toResult(0);
                }
            }
        }
    }
}
