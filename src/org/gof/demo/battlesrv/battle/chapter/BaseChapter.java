package org.gof.demo.battlesrv.battle.chapter;

import org.gof.core.support.ManagerBase;
import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.battle.module.UnitData;
import org.gof.demo.battlesrv.battle.module.UnitGroupCtr;
import org.gof.demo.battlesrv.battle.enumVo.UnitType;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.instance.ConfChapterVO;
import org.gof.demo.worldsrv.instance.InstanceConstants;

import java.util.List;

public abstract class BaseChapter {
    protected BattleMain battleMain;
    public ConfChapterVO config;
    public float chapterTime;
    public boolean timeModel = false;
    public float maxChapterTime;
    public ConfMap mapConfig;
    public boolean over = false;
    public int nextPartState = 0;

    public BaseChapter(BattleMain battleMain) {
        this.battleMain = battleMain;
    }

    public BattleMain getBattleMain(){
        return battleMain;
    }

    public ConfChapterVO getConfig() {
        return config;
    }

    public ConfChapterVO getConfig(int id) {
        setChapter(id);
        return GlobalConfVal.getConfChapterVO(id);
    }

    public void setChapter(int id) {
        if(battleMain.data.chapterType == InstanceConstants.LEAGUEGVECHAPTER_7){
            this.config = new ConfChapterVO(ConfLeagueGveChapter.get(id));
        } else if(battleMain.data.chapterType == InstanceConstants.PARKCROSSPVPCHAPTER_26){
            this.config = new ConfChapterVO(ConfParkCrossPvpChapter.get(id));
        } else if(battleMain.data.chapterType == InstanceConstants.FAMILYBRAWLCHAPTER_14){
            this.config = new ConfChapterVO(ConfFamilyBrawlChapter.get(id));
        } else if(battleMain.data.chapterType == InstanceConstants.KFWARCHAPTER_17){
            this.config = new ConfChapterVO(ConfKfWarChapter.get(id));
        } else if(battleMain.data.chapterType == InstanceConstants.KFWARCHAPTERMONSTER_18){
            this.config = new ConfChapterVO(ConfKfWarMonsterChapter.get(id));
        } else if(battleMain.data.chapterType == InstanceConstants.KUNGFURACE_CHAPTER_33){
            this.config = new ConfChapterVO(ConfPvpCompetitionChapter.get(id));
        }else{
            this.config = GlobalConfVal.getConfChapterVO(id);
        }
        this.mapConfig = ConfMap.get(this.config.map);
        setChapterTimeBasedOnChapterType(battleMain.data.chapterType);
    }

    public void setChapter(int id, boolean e) {
        setChapter(id);
    }

    private void setChapterTimeBasedOnChapterType(int type) {
        switch (type) {
            case InstanceConstants.FAMILYBRAWLCHAPTER_14:
            case InstanceConstants.PARKCROSSPVPCHAPTER_26:
                this.chapterTime = this.config.time > 0 ? this.config.time : 120;
                break;
            default:
                this.chapterTime = this.config.time;
                break;
        }
        this.maxChapterTime = this.chapterTime;
        this.timeModel = this.chapterTime > 0;
    }

    public void loadData(float t) {
        BattleMain e = this.battleMain;

        UnitGroupCtr i = new UnitGroupCtr();
        i.offsetY = 160;

        List<UnitData> units = e.data.playerList.get(1L).units;
        i.playerData = e.data.playerList.get(1L);

        for (UnitData unit : units) {
            UnitHuman c = e.unitMgr.addPlayer(unit);
            if (c.config().type == UnitType.Player.getValue()) {
                i.player = c;
            }
            if (c.config().type == UnitType.FlyPet.getValue()) {
                i.player.flyPet = c;
            }
            i.units.add(c);
        }

        i.positionSelected(t, true);
        e.playerCtrs.add(i);
        e.mainCtr = i;
        e.showMainCtr = i;

    }

    public boolean updateTime(float t) {
        if (this.timeModel) {
            if (this.chapterTime <= 0) {
                this.over = true;
                this.toResult(1, true);
                return true;
            }
            this.chapterTime -= t /*Mathf.Round(this.chapterTime - t)*/;
            this.chapterTime = Math.max(this.chapterTime, 0);
        }
        return false;
    }

    public boolean toResult(int i) {
        return toResult(i, false);
    }

    public boolean toResult(int i, boolean b) {
        return false;
    }

    public void toNextPart(ConfChapterVO t) {
    }

    public void onEndDrawUpdate() {
    }

    public abstract void onUpdate(float t);

    public abstract void destroy();

    public abstract void init();

    public abstract void start();
}

