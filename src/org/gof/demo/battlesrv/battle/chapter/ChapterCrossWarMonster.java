package org.gof.demo.battlesrv.battle.chapter;

import org.gof.demo.battlesrv.battle.BattleMain;
import org.gof.demo.battlesrv.battle.bentity.UnitHuman;
import org.gof.demo.battlesrv.support.Vector2D;
import org.gof.demo.worldsrv.config.ConfFamilyBrawlChapter;
import org.gof.demo.worldsrv.config.ConfFamliyGvgBufflist;
import org.gof.demo.worldsrv.config.ConfGlobal;
import org.gof.demo.worldsrv.config.ConfKfWarMonsterChapter;
import org.gof.demo.worldsrv.entity.Unit;
import org.gof.demo.worldsrv.instance.ConfChapterVO;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;

import java.util.List;

public class ChapterCrossWarMonster extends BaseChapter {

    public UnitHuman bossUnit;

    public ChapterCrossWarMonster(BattleMain battleMain) {
        super(battleMain);
    }

    @Override
    public ConfChapterVO getConfig(int id) {
        return new ConfChapterVO(ConfKfWarMonsterChapter.get(id));
    }

    @Override
    public void loadData(float t) {
        super.loadData(t);

        this.addBuff(this.battleMain.mainCtr.player);
    }

    private void addBuff(UnitHuman t) {
        List<Integer> e = t.data.buffs;
        if (null != e && 0 != e.size())
            for (int _e : e)
                t.skillctr.addChapterBuff(_e);
    }

    @Override
    public void onUpdate(float t) {
        if (!this.over){
            if (this.battleMain.mainCtr.player.isDead())
            {
                this.over = true;
                this.toResult(1);
                return;
            }
            if (!this.updateTime(t))
            {
                if (this.bossUnit.isDead())
                {
                    this.over = true;
                    this.toResult(0);
                }
            }
        }
    }

    @Override
    public void destroy() {

    }

    @Override
    public void init() {

    }

    @Override
    public void start() {
        BattleMain e = this.battleMain;
        UnitHuman n = e.unitMgr.addPlayer(e.data.monster, 1);
        for (int[] a : this.mapConfig.points) {
            if (1 == a[0])
            {
                n.Position = new Vector2D(a[1], a[2]);
                break;
            }
        }
        n.direction = -1;
        this.bossUnit = n;
        this.battleMain.treatDecay = ConfGlobal.get(ConfGlobalKey.hp_recovery_correct.SN).value / 10000f;
    }

}