package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum SkillType {
    USE(1),
    PASSIVE_ADD(2),
    PASSIVE_EFFECT(3),
    PARTNER_SKILL(4),
    ;
    private final int value;

    SkillType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static SkillType fromValue(int value) {
        for (SkillType type : SkillType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}
