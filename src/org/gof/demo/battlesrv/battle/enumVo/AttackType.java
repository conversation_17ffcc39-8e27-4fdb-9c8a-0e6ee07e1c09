package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum AttackType {
    Miss(0),
    Normal(1),
    Cirt(2),
    ;
    private final int value;

    AttackType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static AttackType fromValue(int value) {
        for (AttackType type : AttackType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}
