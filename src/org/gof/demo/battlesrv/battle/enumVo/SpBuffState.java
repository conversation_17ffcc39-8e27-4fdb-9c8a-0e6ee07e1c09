package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum SpBuffState {
    ShareDamage(0),
    StateTriger(1),
    DelayDamage(2),
    Pet<PERSON>ddBuff(3),
    <PERSON>p<PERSON><PERSON>eTriger(4),
    AddBuffTrigger(5),
    TotalDamageTrigger(6),
    ;
    private final int value;

    SpBuffState(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static SpBuffState fromValue(int value) {
        for (SpBuffState type : SpBuffState.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}
