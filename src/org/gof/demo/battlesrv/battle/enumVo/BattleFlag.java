package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum BattleFlag {
    NONE(0),
    NOT_HURT(1),
    LIMIT_SKILL(2),
    LIMIT_ACT(4),
    OPEN_GRAPHIC(8),
    SIMP_MODEL(16),
    UI_MASK(32),

    ;
    private final int value;

    BattleFlag(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static BattleFlag fromValue(int value) {
        for (BattleFlag type : BattleFlag.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}
