package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum BuffGroupType {
    HURT(1),
    CTR_DECREASE(2),
    CTR(3),
    ADD(4),
    TRAP_FOLLOW(10),
    SHIELD(20),
    NORMAL_ACT_NUM_TRIGGER(30),
    BULLET_NUM(40),
    USE_SKILL_NORMAL_ADD(50),
    SKILL_DAMAGE_ADD(60),
    SHARE_DAMAGE(70),
    ATTRIB_CONDITION(80),
    DESTROY_WHEN_NORMAL_AFTER(90),
    NORMAL_BULLET_NUM(100),
    STATE_TRIGER(110),
    DELAY_DEMAGE(120),
    ADDBUFF_TOPET(130),
    HP_CHANGE_TRIGER(140),
    AddBuffTrigger(150),
    UnitCallDamageAdd(160),
    DOUBLE_TRIGGER(170),
    TOTAL_DAMAGE_TRIGGER(180),
    USE_SKILL_ADD(190),
    FRAGILE_EFFECT(200),
    TRIGGER_BULLET(210),
    SKILL_COUNTER(220),
    IMMUNE_DEATH(230),
    BLOCK(240),
    DESTROY_WHEN_SKILL_AFTER(270),
    SKILL_REAL_DAMAGE(290),
    REMAKE_HP(320),
    IGNORE_BUFFIDS(330),
    SKILL_RETURN(340),
    CURRENT_HP (350),
    SKILL_BUFFTIME_ADD (360),
    GNORE_COPY (370),
    VAMPIRE (380),
    GIANT_SLAYER (390),
    DEFER_DAMAGE (400),
    ;
    private final int value;

    BuffGroupType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

}
