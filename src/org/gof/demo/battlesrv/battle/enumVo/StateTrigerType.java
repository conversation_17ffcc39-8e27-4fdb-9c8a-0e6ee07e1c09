package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum StateTrigerType {
    All(0),
    Double_Act(1),
    Counter_Act(2),
    Miss(3),
    <PERSON><PERSON>(4),
    BanAct(5),
    Skill_Crit(7),
    Shield(8),
    Skill_Effect(9),
    Normal_Act(10),
    ;
    private final int value;

    StateTrigerType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static StateTrigerType fromValue(int value) {
        for (StateTrigerType type : StateTrigerType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}
