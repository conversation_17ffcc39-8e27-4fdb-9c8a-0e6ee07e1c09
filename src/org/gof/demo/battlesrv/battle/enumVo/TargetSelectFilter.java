package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum TargetSelectFilter {
    NotFilter(0),
    NearTarget(1),
    FarTarget(2),
    RandomTarget(3),
    MaxHpTarget(4),
    MinHpTarget(5),
    MaxHpValueTarget(6),
    MinHpValueTarget(7),
    HpSmallTarget(8),
    HpBigTarget(9),
    LessHpValueTarget (10),
    MoreHpValueTarget  (11),
    LessAttTarget  (12),
    MoreAttTarget  (13)


    ;
    private final int value;

    TargetSelectFilter(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static TargetSelectFilter fromValue(int value) {
        for (TargetSelectFilter type : TargetSelectFilter.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}
