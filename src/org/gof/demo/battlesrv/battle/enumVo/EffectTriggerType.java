package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum EffectTriggerType {
    NORMAL_ATTACK(0),
    HIT(1),
    COUNTER(2),
    DOUBLE_ATTACK(3),
    USE_SKILL(4),
    USE_PAR(5),
    HP_Hurt(6),
    DIZZ(7),
    CRIT_ATTACK(8),
    NORMAL_DOUBLE_ATTACK(9),
    SKILL_CRIT(10),
    ALL_ATTACK(11),
    HP_Heal(12),
    Miss(13),
    ;
    private final int value;

    EffectTriggerType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static EffectTriggerType fromValue(int value) {
        for (EffectTriggerType type : EffectTriggerType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}
