package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum TargetFilter {
    All(-1),
    <PERSON>(1),
    <PERSON><PERSON><PERSON><PERSON>(2),
    <PERSON>(4),
    <PERSON><PERSON><PERSON>(8),
    <PERSON><PERSON><PERSON><PERSON>(16),
    <PERSON>(32),
    <PERSON>Part<PERSON>(64),
    CastPlayer (128) ,
    EnemyPlayer (256) ,
    EnemyBoss (512) ,
    FriendCall (1024) ,

    ;
    private final int value;

    TargetFilter(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static TargetFilter fromValue(int value) {
        for (TargetFilter type : TargetFilter.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }

}
