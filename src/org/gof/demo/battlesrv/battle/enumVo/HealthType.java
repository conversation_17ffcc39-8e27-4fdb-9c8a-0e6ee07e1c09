package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum HealthType {
    <PERSON>val<PERSON>,
    <PERSON>,
    Hurt_Crit,
    <PERSON>_<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>_Crit,
    Miss,
    Absorb,
    Break,
    Double_Act,
    Counter_Act,
    Skill_Hpsteal,
    Act_Hpsteal,
    Hurt_Share_Damage,
    Hurt_Share_Damage_Crit,
    <PERSON>_<PERSON>,
    Hurt_Double_Crit,
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON>_Bleed,
    <PERSON>_Damage,
    Hurt_Counter,
    Hurt_Counter_Crit,
    Hurt_Bleed_Crit,
}
