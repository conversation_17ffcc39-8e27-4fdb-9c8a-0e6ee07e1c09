package org.gof.demo.battlesrv.battle.enumVo;

/**
 * 设置读缓存
 * 因为开放后的危险性，暂定屏蔽此功能。
 */
public enum DmgType {

    Red(1),
    Add(2),

    ;
    private final int value;

    DmgType(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static DmgType fromValue(int value) {
        for (DmgType type : DmgType.values()) {
            if (type.value == value) {
                return type;
            }
        }
        return null;
    }
}
