package org.gof.demo.battlesrv.battle;

import org.gof.core.support.function.GofFunction1;

import java.util.ArrayList;
import java.util.List;

public class TimerGroup {
    public class TimerObj {
        public int id = 0;
        public float duration = 0;
        protected int loop = 1;
        public boolean running = false;
        protected GofFunction1<Object> _func;
        protected Object _target;
        protected float _curTime = 0;

        public TimerObj alloc(float i, int n, GofFunction1<Object> r, Object e) {//int n =1, Action<object> r =null, object e =null
            TimerObj o = new TimerObj();
            o.duration = i;
            o.loop = n;
            o._func = r;
            o._target = e;
            o.running = true;
            o._curTime = i;
            return o;
        }


        public TimerObj() {
        }

        public TimerObj(float i, int n, GofFunction1<Object> r, Object e) {//int n =1, Action<object> r =null, object e =null
            duration = i;
            loop = n;
            _func = r;
            _target = e;
            running = true;
            _curTime = i;
        }

        public void free() {
        }

        public void stop() {
            if (!this.running)
                return;
            this.running = false;
            this._func = null;
            this._target = null;
        }

        public void onUpdate(float t) {
            if (!this.running)
                return;

            if (this._curTime <= 0) {
                if (this._func != null)  _func.apply(_target);
                if (this.loop > 0) {
                    this.loop--;
                    this._curTime = /*Mathf.Round*/(this._curTime + this.duration);
                }
                if (0 == this.loop)
                    this.stop();
                else if (this.loop < 0)
                    this._curTime = /*Mathf.Round*/(this._curTime + this.duration);
            }
            this._curTime = /*Mathf.Round*/(this._curTime - t);
        }
    }


    protected String _name;
    protected int _timerId = 0;
    protected List<TimerObj> _list = new ArrayList<>();

    public TimerGroup(String name) {
        this._name = name;
    }

    public int start(float t, int i, GofFunction1<Object> n, Object r)//i = 1, Action<object> n = null, object r = null
    {
        TimerObj o = new TimerObj(t, i, n, r);
        _list.add(o);
        this._timerId+=1;
        o.id = this._timerId;
        return this._timerId;
    }

    public void stop(int t) {
        for (TimerObj o : _list) {
            if (o.id == t) {
                o.stop();
                break;
            }
        }
    }

    public TimerObj get(int t) {
        for (TimerObj o : _list) {
            if (o.id == t) {
                return o;
            }
        }
        return null;
    }

    public void update(float t, GofFunction1<TimerObj> onEnd) {
        List<TimerObj> n = this._list;
        List<TimerObj> removeList = new ArrayList<>();
        for (int r = 0; r < n.size(); r++) {
            TimerObj e = n.get(r);
            if (e.running)
                e.onUpdate(t);
            else {
                if (onEnd != null) onEnd.apply(e);
                removeList.add(e);
            }
        }
        for (TimerObj obj : removeList) {
            n.remove(obj);
        }
    }

    public void clear() {
        this._list.clear();
    }
}
