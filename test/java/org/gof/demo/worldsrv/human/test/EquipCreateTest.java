package org.gof.demo.worldsrv.human.test;

import org.gof.core.Node;
import org.gof.core.Port;
import org.gof.core.RecordTransient;
import org.gof.core.db.DBKey;
import org.gof.core.dbsrv.DB;
import org.gof.core.support.Distr;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.Human2;
import org.gof.demo.worldsrv.human.HumanData;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.test.TestBase;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static org.junit.jupiter.api.Assertions.*;

public class EquipCreateTest extends TestBase {


    @Test
    public void testGetList() throws ExecutionException, InterruptedException {
        CompletableFuture<Boolean> waitFuture = new CompletableFuture<>();
        Node node = Node.getInstance();
        Port port = node.getPort(Distr.PORT_DEFAULT);
        port.doAction(()->{
            DB db = DB.newInstance(Human.tableName);
            List<String> cols = new ArrayList<>();
            cols.add("id");
            db.findByQuery(false," where 1=1 limit 100,100 ", DBKey.COLUMN,cols);
            db.listenResult((timeout,returns,context)->{
               if(timeout){
                   logger.error("load humanId timeout");
                   return;
               }
                List<RecordTransient> list = returns.get();
                System.out.println(list);
//                List<Long> ids = new ArrayList<>();
//                list.forEach(item->ids.add(item.get("id")));

                List<Long> ids = new ArrayList<>();
                ids.add(600060000051650001L);
                ids.add(600060000051750001L);
                ids.add(600060000052050003L);
                ids.add(600060000052350002L);
                ids.add(600060000053650001L);

                HumanData.getList(ids, HumanManager.humanClasses,res->{
                    List<HumanData> dataList = res.result();
                    Assertions.assertEquals(ids.size(), dataList.size());
                    dataList.forEach(item-> System.out.println(item.human.getId()+":"+item.human.getName()));
                    waitFuture.complete(true);
                });
//                waitFuture.complete(true);
            });

        });
        waitFuture.get();
    }

//    public void testGetList

    @Test
    public void testLoad() throws InterruptedException, ExecutionException, TimeoutException {
        long humanId = 600010000000000001L;
        CompletableFuture<HumanData> future = new CompletableFuture<>();
        HumanData.getHumanDataAsync(humanId, res -> {
            future.complete(res.result());
        });
        HumanData humanData = future.get(3, TimeUnit.SECONDS);
        logger.info("humanData={}", humanData);
        assertNotNull(humanData);
        assertEquals(humanId, humanData.human.getId());
        assertEquals(humanId, humanData.human2.getId());
        assertEquals(humanId, humanData.profession.getId());
        assertEquals(humanId, humanData.unitPropPlus.getId());
        assertEquals(humanId, humanData.equip.getHumanId());
//        assertEquals(humanId, humanData.artifact.getId());
//        assertEquals(humanId, humanData.mount.getId());
//        assertEquals(humanId, humanData.relic.getId());
        CompletableFuture<HumanData> future2 = new CompletableFuture<>();
        HumanData.getHumanDataAsync(humanId, new Class<?>[]{Human2.class}, res -> {
            if (res.failed()) {
                future2.complete(null);
            } else {
                future2.complete(res.result());
            }

        });
        HumanData humanData2 = future2.get(3, TimeUnit.SECONDS);
        logger.info("humanData2={}", humanData2);
        assertNotNull(humanData2);
        assertNull(humanData2.human);
        assertEquals(humanId, humanData2.human2.getId());
    }
}
