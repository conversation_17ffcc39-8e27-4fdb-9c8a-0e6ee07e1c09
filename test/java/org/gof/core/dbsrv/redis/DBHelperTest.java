package org.gof.core.dbsrv.redis;

import org.gof.core.Node;
import org.gof.core.Port;
import org.gof.core.support.Config;
import org.gof.core.support.Distr;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.test.TestBase;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public class DBHelperTest extends TestBase {

    @Test
    public void testCountByQuery() throws ExecutionException, InterruptedException {
        Port port = Node.getInstance().getPort(Distr.PORT_DEFAULT);
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        port.doAction(() -> {
            Integer count = DBHelper.countByQuery(Human.tableName, " where 1=1 ");
            System.out.println("=============Human.count=" + count);
            future.complete(true);
        });
        future.get();
    }
}
