package org.gof.core.dbsrv.redis;

import org.gof.core.Node;
import org.gof.core.Port;
import org.gof.core.support.Distr;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.test.TestBase;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public class EntityManagerTest extends TestBase {

    @Test
    public void testGetEntityBatch() throws ExecutionException, InterruptedException {
        CompletableFuture<List<Human>> future = new CompletableFuture<>();
        Port port = Node.getInstance().getPort(Distr.PORT_DEFAULT);
        port.doAction(()->{
            List<Long> ids = new ArrayList<>(Arrays.asList(new Long[]{600010000000000076L,
                    600010000000000145L,
                    600010000000000179L,
                    600010000000000218L,
                    600010000000000246L,
                    600010000000000260L,}));

            EntityManager.batchGetEntity(Human.class, ids, res->{
                if(res.failed()){
                    logger.error("getEntityBatch failed !",res.cause());
                    future.completeExceptionally(res.cause());
                    return;
                }
                List<Human> humanList = res.result();
                future.complete(humanList);

            });
        });
        List<Human> humanList = future.get();
        humanList.forEach(human->{
            logger.info("id={}, account={}, name={}",human.getId(),human.getAccount(),human.getName());
        });
    }
}
