package org.gof.core.dbsrv.redis;


import com.alibaba.fastjson.JSONObject;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.core.json.jackson.DatabindCodec;
import io.vertx.redis.client.Request;
import io.vertx.redis.client.Response;
import org.gof.core.Record;
import org.gof.demo.worldsrv.entity.Equip;
import org.gof.demo.worldsrv.support.C;
import org.gof.test.TestBase;
import org.gof.test.TestUtil;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public class RedisToolsTest extends TestBase {

    @Test
    public void testBatchGetListEntities() throws ExecutionException, InterruptedException {
        //Equip.600060000051650001
        //Equip.600060000051750001
        //Equip.600060000052050003
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        String redisEntityKeyPrefix="Equip";
        List<Long> ids = new ArrayList<>();
        ids.add(600060000051650001L);
        ids.add(600060000051750001L);
        ids.add(600060000052050003L);
        Class<?>[] paramTypes = new Class<?>[2];
        paramTypes[0]=String.class;
        paramTypes[1]=List.class;

        List<Request> reqs = (List<Request>)TestUtil.invokeMethod(EntityManager.class, "buildBatchHGetAllReq", paramTypes, redisEntityKeyPrefix, ids);
        RedisTools.batch(EntityManager.redis,reqs,res->{
            List<List<Equip>> result = res.result();
            for (int i = 0; i < result.size(); i++) {
                logger.info("i={}, arrays={}",i, result.get(i));
            }
            future.complete(true);
        },(index,response)->{
            List<Equip> list = new ArrayList<>();
            boolean isKey = false;
            for (Response r : response) {
                isKey=!isKey;
                if(!isKey){
                    try {
                        String string = r.toString();
                        Equip equip = RedisTools.parseEntity(Equip.class,string);
                        list.add(equip);
                    }catch (Exception ex){
                        logger.error("从redis取出数据后，解析json对象出错，cls=");
                    }
                }
            }
            return list;
        });
        future.get();
    }

    @Test
    public void testRedisHmget(){
        List<String> args = new ArrayList<>();
        args.add("server");
        args.add("itemKey");
        Response response = AwaitUtil.awaitResult(handler -> {
            EntityManager.redisClient.hmget(args, res -> {
                if (res.failed()) {
                    handler.handle(Future.failedFuture(res.cause()));
                } else {
                    handler.handle(Future.succeededFuture(res.result()));
                }
            });
        });
        String infoStr = response.toString();
        System.out.println(infoStr);
        System.out.println("isVersion5x="+infoStr.contains("redis_version:5"));
    }

    @Test
    public void testRedisInfo(){
        List<String> args = new ArrayList<>();
        args.add("server");
        Response response = AwaitUtil.awaitResult(handler -> {
            EntityManager.redisClient.info(args, res -> {
                if (res.failed()) {
                    handler.handle(Future.failedFuture(res.cause()));
                } else {
                    handler.handle(Future.succeededFuture(res.result()));
                }
            });
        });
        String infoStr = response.toString();
        System.out.println(infoStr);
        System.out.println("isVersion5x="+infoStr.contains("redis_version:5"));
    }
}
