package org.gof.test;
import com.alibaba.fastjson.JSONObject;
import org.gof.demo.worldsrv.carPark.MountVo;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.HashMap;
import java.util.Map;

public class MountDataComparer {
    private static final String OLD_DB_URL = "***********************************************************************************************************************";
    private static final String NEW_DB_URL = "*****************************************************************************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "123456";

    public static void main(String[] args) {
        try {
            Class.forName("com.mysql.jdbc.Driver");

            try (Connection oldConn = DriverManager.getConnection(OLD_DB_URL, DB_USER, DB_PASSWORD);
                 Connection newConn = DriverManager.getConnection(NEW_DB_URL, DB_USER, DB_PASSWORD)) {

                // 从新库(kumo_210)获取数据
                Map<Long, Map<Integer, MountVo>> newData = loadMountData(newConn);
                // 从旧库(sword_game_dev)获取数据
                Map<Long, Map<Integer, MountVo>> oldData = loadMountData(oldConn);

                System.out.println("开始比对数据...");
                System.out.println("格式: 玩家ID | 坐骑ID | 新库经验值 | 旧库经验值");
                System.out.println("----------------------------------------");

                // 比对数据
                for (Map.Entry<Long, Map<Integer, MountVo>> entry : newData.entrySet()) {
                    long playerId = entry.getKey();
                    Map<Integer, MountVo> newMounts = entry.getValue();
                    Map<Integer, MountVo> oldMounts = oldData.get(playerId);

                    if (oldMounts == null) {
                        continue;
                    }

                    boolean playerHasIssue = false;
                    StringBuilder playerIssues = new StringBuilder();

                    // 比对每个坐骑的经验值
                    for (Map.Entry<Integer, MountVo> mountEntry : newMounts.entrySet()) {
                        int mountId = mountEntry.getKey();
                        MountVo newMount = mountEntry.getValue();
                        MountVo oldMount = oldMounts.get(mountId);

                        if (oldMount != null && oldMount.exp > newMount.exp) {
                            if (!playerHasIssue) {
                                playerIssues.append(String.format("\n玩家ID: %d 需要修复\n", playerId));
                                playerHasIssue = true;
                            }
                            playerIssues.append(String.format("坐骑ID: %d | 新库经验值: %d | 旧库经验值: %d\n",
                                    mountId, newMount.exp, oldMount.exp));
                        }
                    }

                    if (playerHasIssue) {
                        System.out.println(playerIssues.toString());
                        System.out.println("新库完整数据: " + MountVo.mapToJsonStr(newMounts));
                        System.out.println("旧库完整数据: " + MountVo.mapToJsonStr(oldMounts));
                        System.out.println("----------------------------------------");
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static Map<Long, Map<Integer, MountVo>> loadMountData(Connection conn) throws Exception {
        Map<Long, Map<Integer, MountVo>> result = new HashMap<>();

        try (Statement stmt = conn.createStatement()) {
            String sql = "SELECT id, mountMap FROM demo_car_park WHERE mountMap IS NOT NULL AND mountMap != ''";
            ResultSet rs = stmt.executeQuery(sql);

            while (rs.next()) {
                long playerId = rs.getLong("id");
                String mountMapJson = rs.getString("mountMap");

                try {
                    Map<Integer, MountVo> mountMap = MountVo.mapFromJsonStr(mountMapJson);
                    if (!mountMap.isEmpty()) {
                        result.put(playerId, mountMap);
                    }
                } catch (Exception e) {
                    System.err.println("解析数据出错，playerId: " + playerId);
                    e.printStackTrace();
                }
            }
        }

        return result;
    }
}
