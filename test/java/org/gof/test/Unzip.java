package org.gof.test;

import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.data.controldata.ControlCardEliminateData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlCastleData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlSlimeData;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgCarPark;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.StringZipUtils;

public class Unzip {
    public static void main(String[] args) {
        String zipStr = "H4sIAAAAAAAA/6tWyndWsjIy0lHKzEvLV7JSOrwhptTAwMAIRBoaHALzLI0gYgoQSuNw8+FdIKaFyeHVYNrUuAJMGx9eC9ZnoqSjVJJYnO2ak5qbmlfimVIMNNnQQMdMx1SpFgDXqyxxcwAAAA==";
        String unZipNCM = Utils.decompressJson(zipStr);
        ControlCardEliminateData controlData = new ControlCardEliminateData(unZipNCM);
//        String unZipStr = StringZipUtils.unzip(zipStr);
//        Define.p_battle_video unZip = Utils.decompressProtoLZ4(zipStr, Define.p_battle_video.parser());
//        System.out.println(unZip.toString());
        System.out.println(JsonFormatter.formatJson(unZipNCM));
//        byte[] bytes = new byte[0]{}
    }

}
