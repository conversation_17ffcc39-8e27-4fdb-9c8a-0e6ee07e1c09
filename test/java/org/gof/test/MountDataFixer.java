package org.gof.test;

import com.alibaba.fastjson.JSONObject;
import org.gof.demo.worldsrv.carPark.MountVo;

import java.io.FileWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Map;

public class MountDataFixer {
    private static final String DB_URL = "***********************************************************************************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "123456";
    private static final String OUTPUT_FILE = "mount_redis_commands.txt";
    // 设置过期时间为7天（以秒为单位）
    private static final int EXPIRE_SECONDS = 7 * 24 * 60 * 60;

    public static void main(String[] args) {
        try {
            Class.forName("com.mysql.jdbc.Driver");

            try (Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
                 Statement stmt = conn.createStatement();
                 FileWriter writer = new FileWriter(OUTPUT_FILE)) {

                String sql = "SELECT id, mountMap FROM demo_car_park WHERE mountMap IS NOT NULL AND mountMap != ''";
                ResultSet rs = stmt.executeQuery(sql);

                StringBuilder command = new StringBuilder("HSET mountMap");
                int totalCount = 0;

                while (rs.next()) {
                    long playerId = rs.getLong("id");
                    String mountMapJson = rs.getString("mountMap");

                    try {
                        Map<Integer, MountVo> mountMap = MountVo.mapFromJsonStr(mountMapJson);

                        boolean hasExpData = mountMap.values().stream()
                                .anyMatch(vo -> vo.exp > 0);

                        if (hasExpData) {
                            command.append(String.format(" \"%d\" '%s'",
                                    playerId,
                                    mountMapJson.replace("'", "\\'")));
                            totalCount++;
                        }
                    } catch (Exception e) {
                        System.err.println("处理玩家数据出错，playerId: " + playerId);
                        e.printStackTrace();
                    }
                }

                // 写入单条HSET命令
                if (totalCount > 0) {
                    writer.write(command.toString() + "\n");
                    writer.write(String.format("EXPIRE mountMap %d\n", EXPIRE_SECONDS));
                }

                System.out.println("Redis命令已生成到文件: " + OUTPUT_FILE);
                System.out.println("总共处理了 " + totalCount + " 条有效数据");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}