package test.org.gof.core.db;

import org.gof.core.Node;
import org.gof.core.Port;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.main.DBStartup;
import org.gof.core.dbsrv.redis.AwaitUtil;
import org.gof.core.dbsrv.redis.DBHelper;
import org.gof.core.support.Distr;
import org.gof.demo.worldsrv.entity.Human;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import io.vertx.core.Future;
import org.junit.jupiter.api.TestInstance;

import java.util.ArrayList;
import java.util.List;

@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class DBTest {

    @BeforeAll
    public void setup(){
        DBStartup.main(new String[0]);
    }

    @Test
    public void testFreeQuery(){
        Port port = Node.getInstance().getPort(Distr.PORT_ID_ALLOT);
        AwaitUtil.awaitResult(handler->{
            port.doAction(()->{
                DBHelper.freeQuery("select * from demo_human where level>10 limit 10", (timeout,returns,context)->{
                    Object o = returns.get();
                    System.out.println(o);
                    handler.handle(Future.succeededFuture(true));
                });
            });

        });
    }

    @Test
    public void testBatchInsert(){
        Port port = Node.getInstance().getPort(Distr.PORT_ID_ALLOT);
        AwaitUtil.awaitResult(handler->{
            port.doAction(()->{
                Human human = new Human();
                human.setId(101);
                human.setName("name_101");
                Human human2 = new Human();
                human2.setId(102);
                human2.setName("name_102");
                List<Human> entityList = new ArrayList<>();
                entityList.add(human);
                entityList.add(human2);
                deleteHumans(entityList);
                DBHelper.batchInsert(entityList,(_1,_2,_3)->{
                    DB db = DB.newInstance(Human.tableName);
                    db.get(101);
                    db.listenResult((returns,context)->{
                        Object o = returns.get();
                        System.out.println(o);
                        DBHelper.freeQuery("select * from demo_human where id in (101,102)", (timeout,returns1,context1)->{
                            Object o2 = returns1.get();
                            System.out.println(o2);
                            deleteHumans(entityList);
                            handler.handle(Future.succeededFuture(true));
                        });
                    });

                });

            });

        },Long.MAX_VALUE);
    }

    private void deleteHumans(List<Human> entityList){
        List<Long> ids = new ArrayList<>();
        for(Human human: entityList) {
            ids.add(human.getId());
        }
        DB db2 = DB.newInstance(Human.tableName);
        db2.delete(ids);

//        for(Human human: entityList) {
//            DB db = DB.newInstance(Human.tableName);
//            db.delete(human.getId());
//        }
//        DB db2 = DB.newInstance(Human.tableName);
//        db2.flush();
    }
}
