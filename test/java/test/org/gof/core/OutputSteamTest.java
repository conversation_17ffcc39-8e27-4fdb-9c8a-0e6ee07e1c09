package test.org.gof.core;

import com.alibaba.fastjson.JSON;
import org.gof.core.InputStream2;
import org.gof.core.OutputStream2;
import org.gof.core.Record;
import org.gof.core.support.Param;
import org.gof.demo.CommonSerializer;
import org.gof.demo.worldsrv.entity.Item;
import org.junit.jupiter.api.Test;
import test.org.gof.test.TestBase;

import java.util.HashMap;

public class OutputSteamTest extends TestBase {

    @Test
    public void testItemSave(){
        CommonSerializer.init();
        Record record = Record.newInstance(Item.tableName);
        record.set(Item.K.id,1L);
        HashMap<Integer, Integer> itemRecoverMap = new HashMap<>();
        itemRecoverMap.put(1,(int)(System.currentTimeMillis()/1000));
        record.set(Item.K.itemRecoverMap, JSON.toJSONString(itemRecoverMap));
        Item item = new Item(record);
        OutputStream2 out = new OutputStream2();
        out.write(item);
        InputStream2 in = new InputStream2(out.getChunk());
        Item item2 = in.read();
        System.out.println(item2.getId());
    }

    @Test
    public void testWriteOutLength(){
        OutputStream2 out = new OutputStream2();
        Param param = new Param("test","key1","test-val","value1");
        out.write(param);
        InputStream2 in = new InputStream2(out.getChunk());
        Object result = in.read();
        System.out.println(result);
        out.reset();

        Param param2 = new Param("core_id_allot",Long.valueOf(39900));
        OutputStream2 out2 = new OutputStream2();
        out2.write(param2);
        InputStream2 in2 = new InputStream2(out2.getChunk());
        Object result2 = in2.read();
        System.out.println(result2);
    }
}
