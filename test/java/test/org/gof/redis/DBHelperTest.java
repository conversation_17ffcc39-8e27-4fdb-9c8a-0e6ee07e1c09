package test.org.gof.redis;

import com.alibaba.fastjson.JSONArray;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Future;
import org.gof.core.Node;
import org.gof.core.Port;
import org.gof.core.RecordTransient;
import org.gof.core.db.DBKey;
import org.gof.core.dbsrv.DB;
import org.gof.core.dbsrv.redis.DBHelper;
import org.gof.core.support.Distr;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.callback.EntityTableSelectVo;
import org.gof.demo.worldsrv.callback.MemberCallback;
import org.gof.demo.worldsrv.entity.FlyHybridPartner;
import org.gof.demo.worldsrv.entity.Human;
import org.gof.demo.worldsrv.entity.Human2;
import org.gof.demo.worldsrv.rank.RankManager;
import org.gof.demo.worldsrv.rank.RankParamKey;
import org.gof.demo.worldsrv.support.Log;
import test.org.gof.test.TestWorldBase;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

public class DBHelperTest extends TestWorldBase {

    @Test
    public void testCountByQuery() throws ExecutionException, InterruptedException {
        Port port = Node.getInstance().getPort(Distr.PORT_DEFAULT);
        CompletableFuture<Boolean> future = new CompletableFuture<>();
        port.doAction(() -> {
            DB db = DB.newInstance(Human2.tableName);
            List<String> coList = new ArrayList<>();
            coList.add(Human2.K.id);
            coList.add(Human2.K.repSn);
            db.findByQuery(false, " WHERE repSn > 1", DBKey.COLUMN, coList);
            Param param = db.waitForResult();
            List<RecordTransient> list = param.get();
            for(RecordTransient rt : list) {
                StringBuilder scoreStr = new StringBuilder();
                StringBuilder memberStr = new StringBuilder();
                int repSn = rt.get(Human2.K.repSn);
                Long humanId = rt.get(Human2.K.id);
                scoreStr.append(repSn);
                memberStr.append(humanId);

                // 本服排行榜 key=RedisKeys.rankSn_list + rankSn + serverId, score=关卡sn, value=玩家id
                RankManager.inst().rankUpdate(RankManager.inst().getRedisRankTypeKey(RankParamKey.rankTypeRep_1003, 30417),
                        scoreStr.toString(), memberStr.toString());
            }
            future.complete(true);
        });
        future.get();
    }
}
