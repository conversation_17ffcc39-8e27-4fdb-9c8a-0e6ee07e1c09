package test.org.gof.distr.cross;

import com.google.protobuf.InvalidProtocolBufferException;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;
import org.gof.core.Node;
import org.gof.core.Port;
import org.gof.core.Record;
import org.gof.core.dbsrv.redis.AwaitUtil;
import org.gof.core.dbsrv.redis.CrossRedis;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.support.Distr;
import org.gof.core.support.S;
import org.gof.demo.distr.cross.CrossHumanLoader;
import org.gof.demo.worldsrv.entity.HumanBrief;
import org.gof.demo.worldsrv.human.HumanBriefLoadType;
import org.gof.demo.worldsrv.msg.Define;
import org.junit.jupiter.api.Test;
import test.org.gof.test.RedisTestBase;
import test.org.gof.test.TestCrossBase;
import test.org.gof.test.TestWorldBase;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

//public class HumanBriefTest extends TestCrossBase {
public class HumanBriefTest extends RedisTestBase {

    static {
        S.isBridge=true;
        S.isCross=true;
    }

    @Test
    public void testCrossHumanLoad(){
//        long humanId = 600060000240000001L;
        long humanId = 1232L;
        AwaitUtil.awaitResult(handler->{
            CrossHumanLoader.getHumanBrief(humanId, HumanBriefLoadType.BATTLE_INFO,res->{
                HumanBrief result = res.result();
                System.out.println(result.getName());
                Define.p_battle_role battleRole = null;
                try {
                    battleRole = Define.p_battle_role.parseFrom(result.getBattleRole());
                } catch (InvalidProtocolBufferException e) {
                    throw new RuntimeException(e);
                }
                long id = battleRole.getId();
                System.out.println(id);
                handler.handle(Future.succeededFuture());
            });
        });
    }

        @Test
        public void testRedisBytes(){
            Port port = Port.getCurrent();
            Record record = Record.newInstance(HumanBrief.tableName);
            HumanBrief humanBrief = new HumanBrief();
            humanBrief.setId(1232L);
            humanBrief.setName("test1232");
            humanBrief.setLevel(12);
            Define.p_battle_role.Builder dInfo = Define.p_battle_role.newBuilder();
            dInfo.setId(humanBrief.getId());
            dInfo.setName(humanBrief.getName());
            dInfo.setLev(humanBrief.getLevel());
            dInfo.setJob(1);
            for(int i=0;i<10;i++) {
                dInfo.addPetList(Define.p_role_pet.newBuilder().setPetId(1+i).setPetLev(10).setPetPos(7).build());
            }
            for(int i=0;i<10;i++) {
                dInfo.addAttrObjList(Define.p_attr_obj_list.newBuilder().addAttrList(Define.p_key_value.newBuilder().setK(10+i).setV(10000+i).build()).build());
            }
            humanBrief.setBattleRole(dInfo.build().toByteArray());
            String redisKey = "HumanBrief." + humanBrief.getId();
            AwaitUtil.awaitResult(handler->{
                CrossRedis.setHashJsonObject(redisKey,humanBrief.getAllObjNew(), res->{
                    if(res.failed()){
                        logger.error("设置HumanBrief失败", res.cause());
                        return;
                    }
                    CrossRedis.getHashJsonObject(redisKey,getRes->{
                        JsonObject result = getRes.result();
                        HumanBrief newHumanBrief = result.mapTo(HumanBrief.class);
                        try {
                            Define.p_battle_role battleRole = Define.p_battle_role.parser().parseFrom(newHumanBrief.getBattleRole());
                            System.out.println(battleRole.getName());
                            handler.handle(Future.succeededFuture(true));
                        }catch (Exception ex){
                            logger.error("parse battleRole err! ex="+ex, ex);
                        }
                    });
                });
            },500000);
        }

        @Test
        public void testBatchGetHumanBrief(){
            CompletableFuture<List<HumanBrief>> future = new CompletableFuture<>();
            List<HumanBrief> humanBriefs = AwaitUtil.awaitResult(handler->{
                Port port = Node.getInstance().getPort(Distr.PORT_DEFAULT);
                port.doAction(()-> {
                    List<Long> ids = new ArrayList<>();
                    ids.add(600060000160800001L);
                    ids.add(600070000008400001L);
                    EntityManager.batchGetEntity(HumanBrief.class, ids, res -> {
                        handler.handle(Future.succeededFuture(res.result()));
                    });
                });
            },120000);
            System.out.println(humanBriefs.size());
        }
}
