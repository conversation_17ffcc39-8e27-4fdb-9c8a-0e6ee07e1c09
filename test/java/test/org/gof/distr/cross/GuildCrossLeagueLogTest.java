package test.org.gof.distr.cross;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.InvalidProtocolBufferException;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.support.StringZipUtils;
import org.junit.jupiter.api.Test;
import test.org.gof.test.TestBase;

import java.nio.charset.StandardCharsets;

public class GuildCrossLeagueLogTest  {


    public static void main(String[]  args) throws InvalidProtocolBufferException {
        String json = "UEsDBBQACAgIAKCjR1oAAAAAAAAAAAAAAAABAAAAMHVWS47jNhC9i7fdQNOybNMDzIKkaJv+SfRn\\nbM0m6JG7Kckf+dNqq3swi8w2yD2STY6QywyQILdIFSV3Mgji1TNfsVgs1XvS59o+39XekdvaJdmr\\nde1di5B6k+Cv3uoQ4rrObc3kyXbN/805jkNIwyEVxyxH6qT8tYjTandua08vh4fau/pt7TnBzJ0O\\ncaqARrPdorB7/fD4f4c+FIfk9LC2td0/bSb3O8hV++Prr9++/vjt609//f7zn7/8VrPcD6dsC+Tn\\nWnyw4ek520Os8HtF7o/OB/d4FFx1Q6d4Xi91OHnxVkIccmEixaZekxeGWxx7DcDC4sJzAHuIOfde\\nAfcsnnoF4IHFsXcBPJRmoYimmk1VV0zUSjI54zoddQvGkXueRprFwI0ocAq45AY4gVwyu3LyEfa9\\n8Et66saM90q8AdzvMYX4Rk6Z12NjwIkjOWKN+B7WIX7RAswE99qEyRY3hks/XXIv6wmmdPtQJ74J\\n4V5Cbw3wOukKzrgPNcTGaDaHOBUpGbAc4xJm9Ea6kCcZQE710fJEycmYI5+aNz4QXOh5mvXFhK3k\\naNTgc8QykMP7hOO+iQrkxEgfekYuVB+Oqos5thpy7M5Qd/rBK8QuMwvMt4TzTgOsexIG0j8rjK3D\\nvgY7471CGTyX9Q4202u9u1jqjFO81wPca4FrLqO6xSLMf+CFl9WNRnwEnO9XFp/4tR8mPQOeiVeo\\n3Sd9ORSRPcN3+3LAfczXvkQ6YyHGwnMXoUiBDyTEXgT28aw3mr3imqrWQkVhz4kpqCslvOD23h0d\\n6Zsu3jV1oZaT7VGw6UOP1tijHOYlH9OuxXH01q+8iKoepHe4j22yvTBKMQ01wzM+07ucM8Rj/bJ+\\nJYCFYAuYyxTmnYkxzAUzRiwCn2ftrBfCXt7LhGTdy0f2/j2IaZNst7V3zherzO9k5dIGbTu0Tpv/\\n6GtyFPX141VfMnK2p/tp3FSBGg3PtNTUVBjBr/oSW8Cy1Je4L9cz0JeIEKuRWZUaE5/gv7Bc7HVA\\nYxI51Atllc58mKlSZ3Ov0hnOSKlBcuXkVYMF9NFy4+iqT3XlGldObfaShajPol9xeVzlHJ0fpcR9\\niXvlTrzihvSxylmM4pKjiVtywTivOILcGO7XKELgTIlji4XFU4s9xE65LhHfQXyXG5gBowzoMntq\\n7bqoNyY1/dTP516m8Pk74+XGFAo14oH2YpZmcKeFkszqvVfqPVTxxWgqXPQLBdoNHUYQg87FTVNv\\nMHYkOB9ZnYNG23GEuvSRX+G5iirp9x85zvoA8k/4K87qFvLmzbNA3OMiaRzDJeLdVOq7YcSvOjV9\\ngvngmYsmRw0NwFcqDe3BV3pehn44kMxgXF1yL5xg3CjUUpk95nkqmE4EajtxJUzRHu94SZpXLFjS\\n7BZeQOhGY99QR8Uh3wmseXReycHmI573CvcboteMXaiBuRhLeKSTzpx4KfQVfWwyT0ip+xn41HA9\\nx/VxIIPXT5dyfeEVnvDt+gLiZ3qrsWfpEuLJB+tlEaxfRtgPB2b4eFS2T46huhhqjtiFd8jJD4cW\\nw3p9RPp416auvO+S7iBfZLQ9cw945VmPMkHZF6Na4C3VTAyEn4GnpN2DGs8wZ+uCXiKtL7VZpLeF\\n9cYn8JLLTlucA6a09MMc7hR62C+fBnLM9NUDKV9cPVD3v/NA8DtT+Z1J4Z0pznYGA90H7eBaHffM\\n7R7Tl6Bp3HMDNadTjXwb+MD6bkCBZ1NtoiHX8C6b0KU2tMLuUlbvhFMxEN4FzgAtgH5mkGMFfXmW\\nG9UX8Hzh/X7f/QffAQ4str6i3no3S+gQvJFXXCT/46N6CLrqyqCXcNBRDzxOsTBjc9QUePDWG+8P\\n9x2+U6BFrW8iH7Q4OHJmlOxPvvfXRumv1SfNOdkkQD0l+K/ebtCO02675VdP9XH09mUFX16devvL\\n31BLBwj+5wtKrAUAALYJAAA=\\n";
        json="UEsDBBQACAgIAKCjR1oAAAAAAAAAAAAAAAABAAAAMHVWS47jNhC9i7fdQNOybNMDzIKkaJv+SfRn\n" +
                "bM0m6JG7Kckf+dNqq3swi8w2yD2STY6QywyQILdIFSV3Mgji1TNfsVgs1XvS59o+39XekdvaJdmr\n" +
                "de1di5B6k+Cv3uoQ4rrObc3kyXbN/805jkNIwyEVxyxH6qT8tYjTandua08vh4fau/pt7TnBzJ0O\n" +
                "caqARrPdorB7/fD4f4c+FIfk9LC2td0/bSb3O8hV++Prr9++/vjt609//f7zn7/8VrPcD6dsC+Tn\n" +
                "Wnyw4ek520Os8HtF7o/OB/d4FFx1Q6d4Xi91OHnxVkIccmEixaZekxeGWxx7DcDC4sJzAHuIOfde\n" +
                "AfcsnnoF4IHFsXcBPJRmoYimmk1VV0zUSjI54zoddQvGkXueRprFwI0ocAq45AY4gVwyu3LyEfa9\n" +
                "8Et66saM90q8AdzvMYX4Rk6Z12NjwIkjOWKN+B7WIX7RAswE99qEyRY3hks/XXIv6wmmdPtQJ74J\n" +
                "4V5Cbw3wOukKzrgPNcTGaDaHOBUpGbAc4xJm9Ea6kCcZQE710fJEycmYI5+aNz4QXOh5mvXFhK3k\n" +
                "aNTgc8QykMP7hOO+iQrkxEgfekYuVB+Oqos5thpy7M5Qd/rBK8QuMwvMt4TzTgOsexIG0j8rjK3D\n" +
                "vgY7471CGTyX9Q4202u9u1jqjFO81wPca4FrLqO6xSLMf+CFl9WNRnwEnO9XFp/4tR8mPQOeiVeo\n" +
                "3Sd9ORSRPcN3+3LAfczXvkQ6YyHGwnMXoUiBDyTEXgT28aw3mr3imqrWQkVhz4kpqCslvOD23h0d\n" +
                "6Zsu3jV1oZaT7VGw6UOP1tijHOYlH9OuxXH01q+8iKoepHe4j22yvTBKMQ01wzM+07ucM8Rj/bJ+\n" +
                "JYCFYAuYyxTmnYkxzAUzRiwCn2ftrBfCXt7LhGTdy0f2/j2IaZNst7V3zherzO9k5dIGbTu0Tpv/\n" +
                "6GtyFPX141VfMnK2p/tp3FSBGg3PtNTUVBjBr/oSW8Cy1Je4L9cz0JeIEKuRWZUaE5/gv7Bc7HVA\n" +
                "YxI51Atllc58mKlSZ3Ov0hnOSKlBcuXkVYMF9NFy4+iqT3XlGldObfaShajPol9xeVzlHJ0fpcR9\n" +
                "iXvlTrzihvSxylmM4pKjiVtywTivOILcGO7XKELgTIlji4XFU4s9xE65LhHfQXyXG5gBowzoMntq\n" +
                "7bqoNyY1/dTP516m8Pk74+XGFAo14oH2YpZmcKeFkszqvVfqPVTxxWgqXPQLBdoNHUYQg87FTVNv\n" +
                "MHYkOB9ZnYNG23GEuvSRX+G5iirp9x85zvoA8k/4K87qFvLmzbNA3OMiaRzDJeLdVOq7YcSvOjV9\n" +
                "gvngmYsmRw0NwFcqDe3BV3pehn44kMxgXF1yL5xg3CjUUpk95nkqmE4EajtxJUzRHu94SZpXLFjS\n" +
                "7BZeQOhGY99QR8Uh3wmseXReycHmI573CvcboteMXaiBuRhLeKSTzpx4KfQVfWwyT0ip+xn41HA9\n" +
                "x/VxIIPXT5dyfeEVnvDt+gLiZ3qrsWfpEuLJB+tlEaxfRtgPB2b4eFS2T46huhhqjtiFd8jJD4cW\n" +
                "w3p9RPp416auvO+S7iBfZLQ9cw945VmPMkHZF6Na4C3VTAyEn4GnpN2DGs8wZ+uCXiKtL7VZpLeF\n" +
                "9cYn8JLLTlucA6a09MMc7hR62C+fBnLM9NUDKV9cPVD3v/NA8DtT+Z1J4Z0pznYGA90H7eBaHffM\n" +
                "7R7Tl6Bp3HMDNadTjXwb+MD6bkCBZ1NtoiHX8C6b0KU2tMLuUlbvhFMxEN4FzgAtgH5mkGMFfXmW\n" +
                "G9UX8Hzh/X7f/QffAQ4str6i3no3S+gQvJFXXCT/46N6CLrqyqCXcNBRDzxOsTBjc9QUePDWG+8P\n" +
                "9x2+U6BFrW8iH7Q4OHJmlOxPvvfXRumv1SfNOdkkQD0l+K/ebtCO02675VdP9XH09mUFX16devvL\n" +
                "31BLBwj+5wtKrAUAALYJAAA=";
        String result = StringZipUtils.unzip(json);
        JSONObject jsonObj = JSON.parseObject(result);
        JSONObject atkRole = jsonObj.getJSONObject("atk_role");
        byte[] bytes = atkRole.getBytes("json");
        Define.p_battle_role battleRole = Define.p_battle_role.parseFrom(bytes);
        System.out.println(result);
    }















}
