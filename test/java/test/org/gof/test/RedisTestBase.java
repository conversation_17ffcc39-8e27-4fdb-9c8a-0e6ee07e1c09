package test.org.gof.test;

import io.vertx.core.Future;
import io.vertx.core.Vertx;
import org.gof.core.Port;
import org.gof.core.dbsrv.redis.AwaitUtil;
import org.gof.core.dbsrv.redis.DBMainVerticle;
import org.gof.demo.seam.DefaultPort;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class RedisTestBase {
    private static Vertx vertx=null;
    public static Logger logger ;

    @BeforeAll
    public static void initRedis(){
        System.setProperty("logFileName", "world-test");
        System.setProperty("vertx.logger-delegate-factory-class-name", "io.vertx.core.logging.Log4j2LogDelegateFactory");
        logger = LoggerFactory.getLogger(TestCrossBase.class);
        AwaitUtil.awaitResult(handler->{
            vertx = Vertx.vertx();
            Future<String> stringFuture = vertx.deployVerticle(new DBMainVerticle());
            stringFuture.onFailure(cause->handler.handle(Future.failedFuture(cause)));
            stringFuture.onSuccess(result->handler.handle(Future.succeededFuture(true)));
        });
        Port port = new DefaultPort("port0");
        Port.setCurrent(port);
    }

    @AfterAll
    public static void closeRedis(){
        vertx.close();
    }
}
