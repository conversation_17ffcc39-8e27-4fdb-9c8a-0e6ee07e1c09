# 合成大西瓜扫荡功能实现说明

## 新增功能概述

在原有的合成大西瓜活动基础上，新增了扫荡功能，允许玩家消耗体力直接获得积分，无需进行实际的游戏操作。

## 协议定义

### 客户端到服务器 (C2S)
```protobuf
message act_fruit_sweep_c2s {
    option (msgid) = 6574;
    uint32 act_type = 1;
}
```

### 服务器到客户端 (S2C)
```protobuf
message act_fruit_sweep_s2c {
    option (msgid) = 6574;
    uint32 act_type = 1;
    uint32 sweep_score = 2;        // 获得积分
    uint32 max_score = 3;          // 最大局内分数
    uint32 rank = 4;               // 当前排名
    uint64 total_score = 5;        // 累计分数
    uint32 stamina = 6;            // 体力
    uint32 next_recover_time = 7;  // 下次体力恢复时间   
}
```

## 配置表扩展

### ActivityStamina_0 配置表新增字段
- **param**: 一维数值数组，格式为 `[消耗的体力, 获得的积分]`
  - `param[0]`: 扫荡消耗的体力值
  - `param[1]`: 扫荡获得的积分值

### 配置示例
```
act_type: 5015
group_id: 1
param: [5, 100]  // 消耗5点体力，获得100积分
```

## 实现细节

### 1. 消息处理流程
1. **ActivityMsgHandler.act_fruit_sweep_c2s()** - 接收客户端扫荡请求
2. **ActivityManager.on_act_fruit_sweep_c2s()** - 转发到活动控制器
3. **ActivityControlFruitMerge.on_act_fruit_sweep_c2s()** - 执行扫荡逻辑

### 2. 扫荡逻辑实现

#### 基础验证
- 检查活动数据是否存在
- 验证活动期数配置
- 获取体力配置表数据
- 验证param配置格式

#### 体力处理
- 从配置表读取消耗体力值 `param[0]`
- 检查玩家当前体力是否足够
- 扣除相应体力
- 更新总消耗体力记录

#### 积分处理
- 从配置表读取获得积分值 `param[1]`
- 累加到玩家总积分
- 检查是否超过最大局内分数

#### 排行榜更新
- 如果获得积分超过最大局内分数，更新最大分数
- 更新排行榜数据
- 获取玩家当前排名

#### 任务进度更新
- 触发任务进度更新事件
- 使用 `TaskConditionTypeKey.TASK_TYPE_2008`

### 3. 响应数据构建
- 构建 `act_fruit_sweep_s2c` 响应消息
- 包含获得积分、最大分数、排名、累计分数、当前体力等信息
- 发送给客户端

## 核心方法

### on_act_fruit_sweep_c2s()
```java
public void on_act_fruit_sweep_c2s(HumanObject humanObj)
```
- 主要的扫荡处理方法
- 执行完整的扫荡逻辑
- 处理体力扣除、积分获得、排行榜更新等

### sendSweepResponse()
```java
private void sendSweepResponse(HumanObject humanObj, int sweepScore, 
                              ControlFruitMergeData fruitMergeData, int rank)
```
- 构建并发送扫荡响应消息
- 包含所有必要的返回数据

## 安全机制

### 1. 体力验证
- 严格检查体力是否足够
- 体力不足时拒绝扫荡请求
- 记录详细的错误日志

### 2. 配置验证
- 验证配置表数据完整性
- 检查param数组长度和内容
- 配置错误时拒绝操作

### 3. 数据一致性
- 原子性更新所有相关数据
- 确保体力、积分、排行榜数据同步
- 及时保存数据变更

## 与现有功能的集成

### 1. 体力系统
- 复用现有的 `ControlStaminaManager`
- 与手动游戏的体力消耗统一管理
- 支持体力恢复和道具补充

### 2. 排行榜系统
- 复用现有的排行榜更新逻辑
- 与手动游戏的分数更新保持一致
- 支持跨服排行榜

### 3. 任务系统
- 集成现有的任务进度更新机制
- 扫荡获得的积分也会触发任务进度
- 支持各种任务条件类型

## 使用场景

### 1. 快速获得积分
- 玩家体力充足但时间有限时
- 快速完成每日任务
- 提升排行榜排名

### 2. 批量操作
- 可以连续多次扫荡
- 快速消耗大量体力
- 高效获得积分奖励

## 注意事项

### 1. 平衡性考虑
- 扫荡获得的积分应该合理设置
- 不应该完全替代手动游戏
- 保持游戏的趣味性和挑战性

### 2. 资源消耗
- 扫荡会快速消耗体力
- 需要合理设置体力恢复机制
- 考虑体力道具的平衡

### 3. 数据监控
- 监控扫荡使用频率
- 关注对游戏经济的影响
- 及时调整配置参数

## 扩展建议

1. **批量扫荡**: 支持一次扫荡多次
2. **扫荡限制**: 添加每日扫荡次数限制
3. **扫荡奖励**: 扫荡时给予额外奖励
4. **VIP特权**: VIP玩家享受扫荡优惠
5. **扫荡记录**: 记录扫荡历史和统计数据
