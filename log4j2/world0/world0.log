2025-06-13 10:20:22,956 (WorldStartup.java:69) [INFO][GAME] 正在启动游戏服务器
2025-06-13 10:20:23,002 (WorldStartup.java:83) [INFO][GAME] 正在初始化事件容器
2025-06-13 10:20:23,435 (WorldStartup.java:86) [INFO][GAME] 正在初始化协议函数指针池
2025-06-13 10:20:26,586 (WorldStartup.java:94) [INFO][GAME] 加载策划数据
2025-06-13 10:20:27,021 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfBreakBigPrizePreview
2025-06-13 10:20:27,884 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfconfigAngel
2025-06-13 10:20:28,106 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyAdvance_0
2025-06-13 10:20:28,110 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyCd_0
2025-06-13 10:20:28,128 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEntry_0
2025-06-13 10:20:28,131 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEvolutionPro_0
2025-06-13 10:20:28,145 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyHybrid_0
2025-06-13 10:20:28,170 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyLevel_0
2025-06-13 10:20:28,178 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_achievement
2025-06-13 10:20:28,178 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_advance_0
2025-06-13 10:20:28,350 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfLoopBreakCumulativeTimes
2025-06-13 10:20:29,334 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonEquipment
2025-06-13 10:20:29,352 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonShipAppearance
2025-06-13 10:20:29,392 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverActivityGroup
2025-06-13 10:20:29,393 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverGroup
2025-06-13 10:20:29,512 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSlimeDungeon
2025-06-13 10:20:30,436 (GlobalConfVal.java:1310) [INFO][TEMP] ===加载副本表数量=70
2025-06-13 10:20:30,704 (GlobalConfVal.java:502) [INFO][TEMP] ===crossGroupSnInfoMap=[4, 6, 7, 8, 9, 10]
2025-06-13 10:20:30,706 (GlobalConfVal.java:2652) [INFO][TEMP] crossWarAttrBonusMap={1={0={1002=2000}, 1={1024=2000, 1001=2000}}, 3={0={1002=2000}, 2={1024=2000, 1001=2000}, 3={1024=2000, 1001=2000}}}
2025-06-13 10:20:30,706 (GlobalConfVal.java:2666) [INFO][TEMP] crossWarPlayerScoreList=[[1, 100, 100], [101, 105, 200], [106, 110, 300], [111, 115, 400], [116, 120, 500], [121, 125, 600], [126, 130, 700], [131, 135, 800], [136, 140, 900], [141, 150, 1000]]
2025-06-13 10:20:30,707 (GlobalConfVal.java:2667) [INFO][TEMP] crossWarMonsterScoreMap={1200002=75, 1200003=100, 1200001=50, 1200006=175, 1200007=200, 1200004=125, 1200005=150, 1200010=275, 1200011=300, 1200008=225, 1200009=250, 1200012=325}
2025-06-13 10:20:30,707 (GlobalConfVal.java:2668) [INFO][TEMP] crossWarSonOfLightScoreInfo=[10000, 1, 5]
2025-06-13 10:20:31,990 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379
2025-06-13 10:20:32,740 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/1
2025-06-13 10:20:32,750 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/2
2025-06-13 10:20:32,975 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-06-13 10:20:33,018 (EntityManager.java:58) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2025-06-13 10:20:33,020 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-06-13 10:20:33,021 (EntityManager.java:64) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin redis 
2025-06-13 10:20:33,021 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-06-13 10:20:33,022 (EntityManager.java:69) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin cross redis 
2025-06-13 10:20:33,024 (WorldStartup.java:129) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://127.0.0.1:20108
2025-06-13 10:20:33,043 (WorldStartup.java:132) [INFO][GAME] server init : begin start node...
2025-06-13 10:20:34,130 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2025-06-13 10:20:34,138 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2025-06-13 10:20:34,180 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-06-13 10:20:34,188 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2025-06-13 10:20:34,190 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-06-13 10:20:34,190 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2025-06-13 10:20:34,435 (WorldStartup.java:148) [INFO][GAME] server init : begin start platform...
2025-06-13 10:20:35,088 (WorldStartup.java:160) [INFO][GAME] server init : default port started...
2025-06-13 10:20:35,172 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2025-06-13 10:20:35,181 (ServerList.java:62) [ERROR][TEMP] 模式 true true false
2025-06-13 10:20:35,181 (WorldStartup.java:196) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2025-06-13 10:20:35,212 (GameServiceManager.java:32) [INFO][GAME] 开始初始化service
2025-06-13 10:20:37,374 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-06-13 10:20:38,255 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-06-13 10:20:38,444 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-06-13 10:20:38,614 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-06-13 10:20:38,765 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-06-13 10:20:38,858 (ActivityControlService.java:133) [INFO][CROSS_WAR] init m_ttCrossWarReward={"running":true,"interval":604800000,"startTime":"2025-06-15 23:15:00","nextTime":"2025-06-15 23:15:00"}
2025-06-13 10:20:38,935 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-06-13 10:20:39,012 (CarParkService.java:93) [ERROR][CAR_PARK] 开始加载公共停车场数据：3002401,3002402,3002403,3002404,3002405
2025-06-13 10:20:39,043 (ArenaRankedService.java:78) [INFO][TEMP] ===初始化跨服排位赛服务, serverId=30024
2025-06-13 10:20:39,156 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-06-13 10:20:39,302 (CarParkService.java:116) [ERROR][CAR_PARK] 加载公共停车场数据成功返回数量:5
2025-06-13 10:20:39,304 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002401
2025-06-13 10:20:39,304 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002402
2025-06-13 10:20:39,304 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002403
2025-06-13 10:20:39,304 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002404
2025-06-13 10:20:39,305 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002405
2025-06-13 10:20:39,324 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-06-13 10:20:39,331 (ArenaRankedService.java:1063) [ERROR][TEMP] ===arenaBridgeRankRoomKey:0数据为空。无需结算排位赛
2025-06-13 10:20:39,352 (ActivityServerData.java:200) [INFO][TEMP] ===检查活动循环初始化：serverId=30024 , roundSnList=[120001, 80001, 100001, 200001, 140001, 110001, 130001, 90001, 50001, 70002, 170001, 330001]
2025-06-13 10:20:39,353 (ActivityServerData.java:213) [INFO][TEMP] ===检查活动循环初始化：serverId=30024 , roundSnList=[120001, 80001, 100001, 200001, 140001, 110001, 130001, 90001, 50001, 70002, 170001, 330001]
2025-06-13 10:20:39,381 (GuildLeagueWarmUpService.java:863) [INFO][TEMP] ===serverId:30024, 乱斗是否跨服=true
2025-06-13 10:20:39,387 (ArenaService.java:92) [INFO][TEMP] ===初始化本服竞技场服务, 1737300900000
2025-06-13 10:20:39,388 (ArenaService.java:99) [INFO][TEMP] 本服竞技场赛季已经结束，不初始化， confSeason=3, timeNow=1749781239377, serverId 1 -> 60 timeNext=1736696100000, serverNoId=24
2025-06-13 10:20:39,388 (CheckWorldService.java:101) [INFO][TEMP] ===代码未实现，检测的先不处理
2025-06-13 10:20:39,478 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-06-13 10:20:39,571 (GuildService.java:174) [INFO][TEMP] =================init GuildService
2025-06-13 10:20:39,572 (GuildService.java:264) [INFO][TEMP] ===gve开始时间=1749787500000，2025-06-13 12:05:00
2025-06-13 10:20:39,573 (GuildService.java:269) [INFO][TEMP] ===gve结束时间=1749788100000，2025-06-13 12:15:00
2025-06-13 10:20:39,757 (WorldStartup.java:230) [INFO][GAME] server init : begin connect admin...
2025-06-13 10:20:39,760 (WorldStartup.java:235) [INFO][TEMP] ===连接中心管理服， nid=admin0, localBridgeId=world0， org.gof.core.RemoteNode@2570b316[remoteId=admin0,nodeType=ADMIN,remoteAddr=tcp://127.0.0.1:13000,localAlias=world0_30024,connected=false,main=true,createTime=2025-06-13 10:20:39,rogerTime=2025-06-13 10:20:39]
2025-06-13 10:20:39,762 (WorldStartup.java:240) [INFO][GAME] ====================
2025-06-13 10:20:39,762 (WorldStartup.java:241) [INFO][GAME] world0 started.
2025-06-13 10:20:39,762 (WorldStartup.java:242) [INFO][GAME] Listen:tcp://127.0.0.1:20108
2025-06-13 10:20:39,762 (WorldStartup.java:243) [INFO][GAME] ====================
2025-06-13 10:20:39,772 (HumanManager.java:5238) [INFO][TEMP] ===去后台请求屏蔽字库
2025-06-13 10:20:39,773 (HumanManager.java:5279) [INFO][TEMP] ===去后台请求跑马灯
2025-06-13 10:20:39,862 (WorldStartup.java:287) [INFO][GAME] 开启数据热更新扫描...
2025-06-13 10:20:39,931 (ClassScanProcess.java:58) [INFO][GAME] 开启类热更新扫描调度，每5分钟执行一次
2025-06-13 10:20:39,934 (WorldStartup.java:291) [INFO][GAME] 开启类热更新扫描...
2025-06-13 10:20:39,945 (WorldStartup.java:303) [ERROR][GAME] 开启连接服务
2025-06-13 10:20:39,946 (WorldStartup.java:305) [ERROR][GAME] 启动完成...
2025-06-13 10:20:39,954 (ServCheck.java:68) [ERROR][GAME] 
╔═══════════════════╤══════════════╗
║ service           │ costTime(ms) ║
╠═══════════════════╪══════════════╣
║ worldBoss         │ 288          ║
╟───────────────────┼──────────────╢
║ guild             │ 170          ║
╟───────────────────┼──────────────╢
║ flyHybrid         │ 109          ║
╟───────────────────┼──────────────╢
║ activity          │ 55           ║
╟───────────────────┼──────────────╢
║ farm              │ 28           ║
╟───────────────────┼──────────────╢
║ carPark           │ 21           ║
╟───────────────────┼──────────────╢
║ name              │ 17           ║
╟───────────────────┼──────────────╢
║ arenaRanked       │ 5            ║
╟───────────────────┼──────────────╢
║ arena             │ 4            ║
╟───────────────────┼──────────────╢
║                   │ 0            ║
╟───────────────────┼──────────────╢
║ checkWorld        │ 0            ║
╟───────────────────┼──────────────╢
║ mail              │ 0            ║
╟───────────────────┼──────────────╢
║ httpPush          │ 0            ║
╟───────────────────┼──────────────╢
║ pocketLine        │ 0            ║
╟───────────────────┼──────────────╢
║ serverSelect      │ 0            ║
╟───────────────────┼──────────────╢
║ humanGlobal       │ 0            ║
╟───────────────────┼──────────────╢
║ rank              │ 0            ║
╟───────────────────┼──────────────╢
║ dataReset         │ 0            ║
╟───────────────────┼──────────────╢
║ fillMail          │ 0            ║
╟───────────────────┼──────────────╢
║ gm                │ 0            ║
╟───────────────────┼──────────────╢
║ team              │ 0            ║
╟───────────────────┼──────────────╢
║ humanCreateApply  │ 0            ║
╟───────────────────┼──────────────╢
║ confirm           │ 0            ║
╟───────────────────┼──────────────╢
║ chat              │ 0            ║
╟───────────────────┼──────────────╢
║ guildLeagueWarmUp │ 0            ║
╚═══════════════════╧══════════════╝

2025-06-13 10:20:39,964 (:) [ERROR][io.vertx.core.impl.ContextImpl] Unhandled exception
java.lang.NullPointerException: null
	at org.gof.core.dbsrv.DBPartServiceProxy.get(DBPartServiceProxy.java:423) ~[bin/:?]
	at org.gof.core.dbsrv.DB.get(DB.java:221) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$loadFromDbToCache$53(EntityManager.java:1108) ~[bin/:?]
	at org.gof.core.dbsrv.redis.AsyncActionResult.lambda$futureSuccess$3(AsyncActionResult.java:60) ~[bin/:?]
	at io.vertx.core.impl.future.FutureImpl$1.onSuccess(FutureImpl.java:91) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl$ListenerArray.onSuccess(FutureImpl.java:310) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl.tryComplete(FutureImpl.java:259) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.Promise.complete(Promise.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at org.gof.core.dbsrv.redis.RedisTools.lambda$exists$52(RedisTools.java:1098) ~[bin/:?]
	at io.vertx.core.impl.future.FutureImpl$4.onSuccess(FutureImpl.java:176) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl.tryComplete(FutureImpl.java:259) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.Transformation$1.onSuccess(Transformation.java:61) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.SucceededFuture.addListener(SucceededFuture.java:88) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.Transformation.onSuccess(Transformation.java:42) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.lambda$emitSuccess$0(FutureBase.java:60) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) [netty-transport-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-06-13 10:20:41,544 (GuildService.java:4462) [INFO][TEMP] 同步完成
2025-06-13 10:20:50,655 (ClassScanProcess.java:135) [INFO][GAME] 开始一次扫描key=363957321744344066869,jarValue=363957321744344066869
