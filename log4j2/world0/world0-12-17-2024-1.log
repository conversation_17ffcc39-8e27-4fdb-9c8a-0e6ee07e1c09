2024-12-17 15:48:05,345 (WorldStartup.java:69) [INFO][GAME] 正在启动游戏服务器
2024-12-17 15:48:05,410 (WorldStartup.java:83) [INFO][GAME] 正在初始化事件容器
2024-12-17 15:48:05,837 (WorldStartup.java:86) [INFO][GAME] 正在初始化协议函数指针池
2024-12-17 15:48:08,825 (WorldStartup.java:94) [INFO][GAME] 加载策划数据
2024-12-17 15:48:09,245 (ConfBreakBigPrizePreview.java:283) [INFO][TEMP] 表ConfBreakBigPrizePreview有问题!
2024-12-17 15:48:10,356 (ConfFlyEgg.java:297) [INFO][TEMP] 表ConfFlyEgg有问题!
2024-12-17 15:48:10,415 (ConfFlyRemakeCost.java:269) [INFO][TEMP] 表ConfFlyRemakeCost有问题!
2024-12-17 15:48:10,419 (ConfFlyResolveReturn.java:276) [INFO][TEMP] 表ConfFlyResolveReturn有问题!
2024-12-17 15:48:10,423 (ConfFlyTotalAchievement.java:290) [INFO][TEMP] 表ConfFlyTotalAchievement有问题!
2024-12-17 15:48:10,425 (ConfFly_achievement.java:304) [INFO][TEMP] 表ConfFly_achievement有问题!
2024-12-17 15:48:13,123 (GlobalConfVal.java:2152) [INFO][TEMP] quizDiceRewardMap={3={7001=50}, 4={7001=50}, 5={7001=50}, 6={7001=50}, 7={7001=60}, 8={7001=70}, 9={7001=80}, 10={2=10, 7001=90}, 11={2=15, 7001=100}, 12={2=20, 7001=110}, 13={2=30, 7001=130}, 14={2=40, 7001=180}, 15={2=50, 7001=270}, 16={2=66, 7001=450}, 17={2=88, 7001=888, 1017=66}, 18={2=166, 7001=2666, 1017=88}}
2024-12-17 15:48:13,124 (GlobalConfVal.java:2173) [INFO][TEMP] quizRankRewardMap={1={1001=150, 7001=500}, 2={1001=100, 7001=400}, 3={1001=80, 7001=300}, 4={1001=50, 7001=200}, 5={1001=50, 7001=200}, 6={1001=20, 7001=100}}
2024-12-17 15:48:13,124 (GlobalConfVal.java:2188) [INFO][TEMP] quizRankRewardExpMap={1=5000, 2=4000, 3=3000, 4=2000, 5=2000, 6=1000}
2024-12-17 15:48:13,657 (GlobalConfVal.java:1208) [INFO][TEMP] ===加载副本表数量=70
2024-12-17 15:48:14,647 (GlobalConfVal.java:452) [INFO][TEMP] ===crossGroupSnInfoMap={4=org.gof.demo.distr.cross.domain.CrossGroupInfo@45c9b3, 5=org.gof.demo.distr.cross.domain.CrossGroupInfo@38b3f208, 6=org.gof.demo.distr.cross.domain.CrossGroupInfo@6680f714, 7=org.gof.demo.distr.cross.domain.CrossGroupInfo@53b1a3f8}
2024-12-17 15:48:15,709 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379
2024-12-17 15:48:15,956 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/1
2024-12-17 15:48:15,961 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/2
2024-12-17 15:48:16,094 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2024-12-17 15:48:16,105 (EntityManager.java:55) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2024-12-17 15:48:16,109 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2024-12-17 15:48:16,109 (EntityManager.java:61) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin redis 
2024-12-17 15:48:16,109 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2024-12-17 15:48:16,110 (EntityManager.java:66) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin cross redis 
2024-12-17 15:48:16,111 (WorldStartup.java:129) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://127.0.0.1:20108
2024-12-17 15:48:16,123 (WorldStartup.java:132) [INFO][GAME] server init : begin start node...
2024-12-17 15:48:16,569 (FieldTable.java:58) [INFO][GAME] server init : db executeQuery...
2024-12-17 15:48:16,613 (FieldTable.java:69) [INFO][GAME] server init : initTable...
2024-12-17 15:48:16,675 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2024-12-17 15:48:16,679 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2024-12-17 15:48:16,699 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2024-12-17 15:48:16,701 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2024-12-17 15:48:16,702 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2024-12-17 15:48:16,703 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2024-12-17 15:48:16,887 (WorldStartup.java:148) [INFO][GAME] server init : begin start platform...
2024-12-17 15:48:17,426 (WorldStartup.java:160) [INFO][GAME] server init : default port started...
2024-12-17 15:48:17,461 (WorldStartup.java:196) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2024-12-17 15:48:17,473 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2024-12-17 15:48:17,500 (GameServiceManager.java:27) [INFO][GAME] 开始初始化service
2024-12-17 15:48:27,365 (ActivityServerData.java:132) [INFO][TEMP] ===检查活动循环初始化：serverId=300572 , roundSnList=[120001, 90001, 110001, 50001, 200001, 140001]
2024-12-17 15:48:27,367 (ActivityServerData.java:145) [INFO][TEMP] ===检查活动循环初始化：serverId=300572 , roundSnList=[120001, 90001, 110001, 50001, 200001, 140001]
2024-12-17 15:48:28,754 (CheckWorldService.java:101) [INFO][TEMP] ===代码未实现，检测的先不处理
2024-12-17 15:48:28,764 (GuildLeagueWarmUpService.java:1199) [ERROR][TEMP] ===timeNow=1734421708746, interval=101491254，开始战斗时间2024-12-18 20:00:00
2024-12-17 15:48:29,247 (GuildService.java:176) [INFO][TEMP] =================init GuildService
2024-12-17 15:48:29,249 (GuildService.java:266) [INFO][TEMP] ===gve开始时间=1734434700000，2024-12-17 19:25:00
2024-12-17 15:48:29,249 (GuildService.java:271) [INFO][TEMP] ===gve结束时间=1734435300000，2024-12-17 19:35:00
2024-12-17 15:48:29,385 (GuildService.java:335) [INFO][TEMP] initCheckRank 0
2024-12-17 15:48:29,401 (WorldStartup.java:230) [INFO][GAME] server init : begin connect admin...
2024-12-17 15:48:29,406 (WorldStartup.java:235) [INFO][TEMP] ===连接中心管理服， nid=admin0, localBridgeId=world0， org.gof.core.RemoteNode@2f4545c6[remoteId=admin0,nodeType=ADMIN,remoteAddr=tcp://127.0.0.1:13000,localAlias=world0_300572,connected=false,main=true,createTime=2024-12-17 15:48:29,rogerTime=2024-12-17 15:48:29]
2024-12-17 15:48:29,407 (WorldStartup.java:240) [INFO][GAME] ====================
2024-12-17 15:48:29,407 (WorldStartup.java:241) [INFO][GAME] world0 started.
2024-12-17 15:48:29,407 (WorldStartup.java:242) [INFO][GAME] Listen:tcp://127.0.0.1:20108
2024-12-17 15:48:29,407 (WorldStartup.java:243) [INFO][GAME] ====================
2024-12-17 15:48:29,417 (HumanManager.java:4819) [INFO][TEMP] ===去后台请求屏蔽字库
2024-12-17 15:48:29,418 (HumanManager.java:4860) [INFO][TEMP] ===去后台请求跑马灯
2024-12-17 15:48:29,514 (WorldStartup.java:279) [INFO][GAME] 开启数据热更新扫描...
2024-12-17 15:48:29,561 (ClassScanProcess.java:58) [INFO][GAME] 开启类热更新扫描调度，每5分钟执行一次
2024-12-17 15:48:29,562 (WorldStartup.java:283) [INFO][GAME] 开启类热更新扫描...
2024-12-17 15:48:29,572 (WorldStartup.java:295) [ERROR][GAME] 开启连接服务
2024-12-17 15:48:29,573 (WorldStartup.java:297) [ERROR][GAME] 启动完成...
2024-12-17 15:48:29,578 (ServCheck.java:68) [ERROR][GAME] 
╔═══════════════════╤══════════════╗
║ service           │ costTime(ms) ║
╠═══════════════════╪══════════════╣
║ guild             │ 139          ║
╟───────────────────┼──────────────╢
║ name              │ 16           ║
╟───────────────────┼──────────────╢
║ carPark           │ 14           ║
╟───────────────────┼──────────────╢
║ activity          │ 10           ║
╟───────────────────┼──────────────╢
║ worldBoss         │ 9            ║
╟───────────────────┼──────────────╢
║ farm              │ 8            ║
╟───────────────────┼──────────────╢
║ arena             │ 4            ║
╟───────────────────┼──────────────╢
║ guildLeagueWarmUp │ 4            ║
╟───────────────────┼──────────────╢
║ checkWorld        │ 1            ║
╟───────────────────┼──────────────╢
║ arenaRanked       │ 1            ║
╟───────────────────┼──────────────╢
║                   │ 0            ║
╟───────────────────┼──────────────╢
║ mail              │ 0            ║
╟───────────────────┼──────────────╢
║ httpPush          │ 0            ║
╟───────────────────┼──────────────╢
║ pocketLine        │ 0            ║
╟───────────────────┼──────────────╢
║ humanGlobal       │ 0            ║
╟───────────────────┼──────────────╢
║ rank              │ 0            ║
╟───────────────────┼──────────────╢
║ dataReset         │ 0            ║
╟───────────────────┼──────────────╢
║ fillMail          │ 0            ║
╟───────────────────┼──────────────╢
║ gm                │ 0            ║
╟───────────────────┼──────────────╢
║ team              │ 0            ║
╟───────────────────┼──────────────╢
║ humanCreateApply  │ 0            ║
╟───────────────────┼──────────────╢
║ confirm           │ 0            ║
╟───────────────────┼──────────────╢
║ chat              │ 0            ║
╟───────────────────┼──────────────╢
║ captureSlave      │ 0            ║
╚═══════════════════╧══════════════╝

2024-12-17 15:48:32,608 (WorldStartup.java:248) [INFO][GAME] 触发关闭服务器操作,开始踢人
2024-12-17 15:48:32,613 (HumanGlobalService.java:600) [INFO][GAME] ===已踢出所有玩家
2024-12-17 15:48:32,614 (CarParkService.java:196) [ERROR][GAME] 保存停车场数据完成
2024-12-17 15:48:39,574 (ClassScanProcess.java:135) [INFO][GAME] 开始一次扫描key=216594811732434223521,jarValue=216594811732434223521
2024-12-17 15:48:51,881 (WorldStartup.java:266) [INFO][GAME] 关闭服务器-检查db更新队列完成! 15s后服务器将关闭
