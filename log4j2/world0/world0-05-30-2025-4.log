2025-05-30 16:53:20,894 (WorldStartup.java:69) [INFO][GAME] 正在启动游戏服务器
2025-05-30 16:53:20,945 (WorldStartup.java:83) [INFO][GAME] 正在初始化事件容器
2025-05-30 16:53:21,394 (WorldStartup.java:86) [INFO][GAME] 正在初始化协议函数指针池
2025-05-30 16:53:24,809 (WorldStartup.java:94) [INFO][GAME] 加载策划数据
2025-05-30 16:53:25,322 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfBreakBigPrizePreview
2025-05-30 16:53:26,301 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfconfigAngel
2025-05-30 16:53:26,570 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyAdvance_0
2025-05-30 16:53:26,577 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyCd_0
2025-05-30 16:53:26,602 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEntry_0
2025-05-30 16:53:26,607 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEvolutionPro_0
2025-05-30 16:53:26,624 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyHybrid_0
2025-05-30 16:53:26,645 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyLevel_0
2025-05-30 16:53:26,656 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_achievement
2025-05-30 16:53:26,657 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_advance_0
2025-05-30 16:53:26,820 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfLoopBreakCumulativeTimes
2025-05-30 16:53:27,966 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonEquipment
2025-05-30 16:53:27,988 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonShipAppearance
2025-05-30 16:53:28,031 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverActivityGroup
2025-05-30 16:53:28,032 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverGroup
2025-05-30 16:53:28,149 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSlimeDungeon
2025-05-30 16:53:29,088 (GlobalConfVal.java:1310) [INFO][TEMP] ===加载副本表数量=70
2025-05-30 16:53:29,355 (GlobalConfVal.java:502) [INFO][TEMP] ===crossGroupSnInfoMap=[4, 6, 7, 8, 9, 10]
2025-05-30 16:53:29,357 (GlobalConfVal.java:2652) [INFO][TEMP] crossWarAttrBonusMap={1={0={1002=2000}, 1={1024=2000, 1001=2000}}, 3={0={1002=2000}, 2={1024=2000, 1001=2000}, 3={1024=2000, 1001=2000}}}
2025-05-30 16:53:29,358 (GlobalConfVal.java:2666) [INFO][TEMP] crossWarPlayerScoreList=[[1, 100, 100], [101, 105, 200], [106, 110, 300], [111, 115, 400], [116, 120, 500], [121, 125, 600], [126, 130, 700], [131, 135, 800], [136, 140, 900], [141, 150, 1000]]
2025-05-30 16:53:29,358 (GlobalConfVal.java:2667) [INFO][TEMP] crossWarMonsterScoreMap={1200002=75, 1200003=100, 1200001=50, 1200006=175, 1200007=200, 1200004=125, 1200005=150, 1200010=275, 1200011=300, 1200008=225, 1200009=250, 1200012=325}
2025-05-30 16:53:29,359 (GlobalConfVal.java:2668) [INFO][TEMP] crossWarSonOfLightScoreInfo=[10000, 1, 5]
2025-05-30 16:53:30,725 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379
2025-05-30 16:53:31,444 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/1
2025-05-30 16:53:31,457 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/2
2025-05-30 16:53:31,659 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 16:53:31,702 (EntityManager.java:58) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2025-05-30 16:53:31,705 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 16:53:31,706 (EntityManager.java:64) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin redis 
2025-05-30 16:53:31,707 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 16:53:31,707 (EntityManager.java:69) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin cross redis 
2025-05-30 16:53:31,708 (WorldStartup.java:129) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://127.0.0.1:20108
2025-05-30 16:53:31,728 (WorldStartup.java:132) [INFO][GAME] server init : begin start node...
2025-05-30 16:53:32,372 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2025-05-30 16:53:32,381 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2025-05-30 16:53:32,425 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-05-30 16:53:32,432 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2025-05-30 16:53:32,435 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-05-30 16:53:32,436 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2025-05-30 16:53:32,699 (WorldStartup.java:148) [INFO][GAME] server init : begin start platform...
2025-05-30 16:53:33,374 (WorldStartup.java:160) [INFO][GAME] server init : default port started...
2025-05-30 16:53:33,465 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2025-05-30 16:53:33,471 (ServerList.java:62) [ERROR][TEMP] 模式 true true false
2025-05-30 16:53:33,472 (WorldStartup.java:196) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2025-05-30 16:53:33,508 (GameServiceManager.java:32) [INFO][GAME] 开始初始化service
2025-05-30 16:53:35,657 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 16:53:36,414 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 16:53:36,645 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 16:53:36,855 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 16:53:37,012 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 16:53:37,128 (ActivityControlService.java:133) [INFO][CROSS_WAR] init m_ttCrossWarReward={"running":true,"interval":604800000,"startTime":"2025-06-01 23:15:00","nextTime":"2025-06-01 23:15:00"}
2025-05-30 16:53:37,200 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 16:53:37,396 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 16:53:37,577 (CarParkService.java:93) [ERROR][CAR_PARK] 开始加载公共停车场数据：3002401,3002402,3002403,3002404,3002405
2025-05-30 16:53:37,588 (ArenaRankedService.java:78) [INFO][TEMP] ===初始化跨服排位赛服务, serverId=30024
2025-05-30 16:53:37,606 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 16:53:37,623 (ActivityServerData.java:200) [INFO][TEMP] ===检查活动循环初始化：serverId=30024 , roundSnList=[120001, 100001, 80001, 200001, 140001, 110001, 90001, 130001, 50001, 170001, 70002, 330001]
2025-05-30 16:53:37,624 (ActivityServerData.java:213) [INFO][TEMP] ===检查活动循环初始化：serverId=30024 , roundSnList=[120001, 100001, 80001, 200001, 140001, 110001, 90001, 130001, 50001, 170001, 70002, 330001]
2025-05-30 16:53:37,664 (CarParkService.java:116) [ERROR][CAR_PARK] 加载公共停车场数据成功返回数量:5
2025-05-30 16:53:37,714 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002401
2025-05-30 16:53:37,715 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002402
2025-05-30 16:53:37,717 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002403
2025-05-30 16:53:37,717 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002404
2025-05-30 16:53:37,717 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002405
2025-05-30 16:53:37,722 (GuildLeagueWarmUpService.java:863) [INFO][TEMP] ===serverId:30024, 乱斗是否跨服=true
2025-05-30 16:53:37,724 (CheckWorldService.java:101) [INFO][TEMP] ===代码未实现，检测的先不处理
2025-05-30 16:53:37,747 (ArenaService.java:92) [INFO][TEMP] ===初始化本服竞技场服务, 1737300900000
2025-05-30 16:53:37,748 (ArenaService.java:99) [INFO][TEMP] 本服竞技场赛季已经结束，不初始化， confSeason=3, timeNow=1748595217713, serverId 1 -> 60 timeNext=1736696100000, serverNoId=24
2025-05-30 16:53:37,767 (ArenaRankedService.java:1063) [ERROR][TEMP] ===arenaBridgeRankRoomKey:0数据为空。无需结算排位赛
2025-05-30 16:53:37,933 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 16:53:37,995 (GuildService.java:174) [INFO][TEMP] =================init GuildService
2025-05-30 16:53:37,997 (GuildService.java:264) [INFO][TEMP] ===gve开始时间=1748604300000，2025-05-30 19:25:00
2025-05-30 16:53:38,001 (GuildService.java:269) [INFO][TEMP] ===gve结束时间=1748604900000，2025-05-30 19:35:00
2025-05-30 16:53:38,268 (GuildService.java:469) [ERROR][TEMP] loadGuild 600240000011000281 end
2025-05-30 16:53:38,320 (WorldStartup.java:230) [INFO][GAME] server init : begin connect admin...
2025-05-30 16:53:38,328 (WorldStartup.java:235) [INFO][TEMP] ===连接中心管理服， nid=admin0, localBridgeId=world0， org.gof.core.RemoteNode@1a3b1f7e[remoteId=admin0,nodeType=ADMIN,remoteAddr=tcp://127.0.0.1:13000,localAlias=world0_30024,connected=false,main=true,createTime=2025-05-30 16:53:38,rogerTime=2025-05-30 16:53:38]
2025-05-30 16:53:38,330 (WorldStartup.java:240) [INFO][GAME] ====================
2025-05-30 16:53:38,331 (WorldStartup.java:241) [INFO][GAME] world0 started.
2025-05-30 16:53:38,331 (WorldStartup.java:242) [INFO][GAME] Listen:tcp://127.0.0.1:20108
2025-05-30 16:53:38,331 (WorldStartup.java:243) [INFO][GAME] ====================
2025-05-30 16:53:38,337 (HumanManager.java:5198) [INFO][TEMP] ===去后台请求屏蔽字库
2025-05-30 16:53:38,338 (HumanManager.java:5239) [INFO][TEMP] ===去后台请求跑马灯
2025-05-30 16:53:38,437 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,470 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,471 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,474 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,475 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,481 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,483 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,489 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,490 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,491 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,492 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,492 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,493 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,499 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,500 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,502 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,503 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,504 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,505 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,506 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,509 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,511 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,521 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,522 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,522 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,523 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,530 (WorldStartup.java:287) [INFO][GAME] 开启数据热更新扫描...
2025-05-30 16:53:38,559 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,578 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,598 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,599 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,600 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,601 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,604 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,615 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,615 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,616 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,617 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,617 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,618 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,625 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:38,638 (ClassScanProcess.java:58) [INFO][GAME] 开启类热更新扫描调度，每5分钟执行一次
2025-05-30 16:53:38,639 (WorldStartup.java:291) [INFO][GAME] 开启类热更新扫描...
2025-05-30 16:53:38,654 (WorldStartup.java:303) [ERROR][GAME] 开启连接服务
2025-05-30 16:53:38,654 (WorldStartup.java:305) [ERROR][GAME] 启动完成...
2025-05-30 16:53:38,663 (ServCheck.java:68) [ERROR][GAME] 
╔═══════════════════╤══════════════╗
║ service           │ costTime(ms) ║
╠═══════════════════╪══════════════╣
║ worldBoss         │ 293          ║
╟───────────────────┼──────────────╢
║ guild             │ 278          ║
╟───────────────────┼──────────────╢
║ flyHybrid         │ 122          ║
╟───────────────────┼──────────────╢
║ activity          │ 55           ║
╟───────────────────┼──────────────╢
║ farm              │ 29           ║
╟───────────────────┼──────────────╢
║ name              │ 16           ║
╟───────────────────┼──────────────╢
║ arena             │ 14           ║
╟───────────────────┼──────────────╢
║ carPark           │ 5            ║
╟───────────────────┼──────────────╢
║ arenaRanked       │ 1            ║
╟───────────────────┼──────────────╢
║ team              │ 1            ║
╟───────────────────┼──────────────╢
║                   │ 0            ║
╟───────────────────┼──────────────╢
║ checkWorld        │ 0            ║
╟───────────────────┼──────────────╢
║ mail              │ 0            ║
╟───────────────────┼──────────────╢
║ httpPush          │ 0            ║
╟───────────────────┼──────────────╢
║ pocketLine        │ 0            ║
╟───────────────────┼──────────────╢
║ serverSelect      │ 0            ║
╟───────────────────┼──────────────╢
║ humanGlobal       │ 0            ║
╟───────────────────┼──────────────╢
║ rank              │ 0            ║
╟───────────────────┼──────────────╢
║ dataReset         │ 0            ║
╟───────────────────┼──────────────╢
║ fillMail          │ 0            ║
╟───────────────────┼──────────────╢
║ gm                │ 0            ║
╟───────────────────┼──────────────╢
║ humanCreateApply  │ 0            ║
╟───────────────────┼──────────────╢
║ confirm           │ 0            ║
╟───────────────────┼──────────────╢
║ chat              │ 0            ║
╟───────────────────┼──────────────╢
║ guildLeagueWarmUp │ 0            ║
╚═══════════════════╧══════════════╝

2025-05-30 16:53:38,673 (:) [ERROR][io.vertx.core.impl.ContextImpl] Unhandled exception
java.lang.NullPointerException: null
	at org.gof.core.dbsrv.DBPartServiceProxy.get(DBPartServiceProxy.java:423) ~[bin/:?]
	at org.gof.core.dbsrv.DB.get(DB.java:221) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$loadFromDbToCache$53(EntityManager.java:1108) ~[bin/:?]
	at org.gof.core.dbsrv.redis.AsyncActionResult.lambda$futureSuccess$3(AsyncActionResult.java:60) ~[bin/:?]
	at io.vertx.core.impl.future.FutureImpl$1.onSuccess(FutureImpl.java:91) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl$ListenerArray.onSuccess(FutureImpl.java:310) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl.tryComplete(FutureImpl.java:259) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.Promise.complete(Promise.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at org.gof.core.dbsrv.redis.RedisTools.lambda$exists$52(RedisTools.java:1098) ~[bin/:?]
	at io.vertx.core.impl.future.FutureImpl$4.onSuccess(FutureImpl.java:176) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl.tryComplete(FutureImpl.java:259) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.Transformation$1.onSuccess(Transformation.java:61) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.SucceededFuture.addListener(SucceededFuture.java:88) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.Transformation.onSuccess(Transformation.java:42) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.lambda$emitSuccess$0(FutureBase.java:60) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) [netty-transport-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 16:53:42,148 (Port.java:466) [ERROR][CORE] 执行Call队列时发生错误: call=org.gof.core.Call@584fcda0[type=1000,fromNodeId=world0,fromPortId=dbPart0,to=org.gof.core.CallPoint@50d4d439[nodeId=world0,portId=dbLine0,servId=serv0,callerInfo=],callId=122,methodKey=2,methodParameters={demo_human2,0,true,SELECT id,repSn FROM demo_human2 WHERE repSn > 1,{}},returnValues={},methodKeyName=ORG_GOF_CORE_DBSRV_DBLINESERVICE_EXECUTE_STRING_LONG_BOOLEAN_STRING_OBJECTS], org.gof.core.support.SysException: [dbLine0]执行SQL=SELECT id,repSn FROM demo_human2 WHERE repSn > 1, 参数=[]
org.gof.core.support.SysException: [dbLine0]执行SQL=SELECT id,repSn FROM demo_human2 WHERE repSn > 1, 参数=[]
	at org.gof.core.dbsrv.DBLineService.execute(DBLineService.java:266) ~[bin/:?]
	at org.gof.core.Port.pulseCalls(Port.java:445) [bin/:?]
	at org.gof.core.Port.pulseCalls(Port.java:398) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:267) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
Caused by: java.sql.SQLException: Can not issue executeUpdate() or executeLargeUpdate() for SELECTs
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:965) ~[mysql-connector-java-5.1.48-bin.jar:5.1.48]
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:898) ~[mysql-connector-java-5.1.48-bin.jar:5.1.48]
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:887) ~[mysql-connector-java-5.1.48-bin.jar:5.1.48]
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:861) ~[mysql-connector-java-5.1.48-bin.jar:5.1.48]
	at com.mysql.jdbc.PreparedStatement.executeUpdateInternal(PreparedStatement.java:2103) ~[mysql-connector-java-5.1.48-bin.jar:5.1.48]
	at com.mysql.jdbc.PreparedStatement.executeUpdateInternal(PreparedStatement.java:2067) ~[mysql-connector-java-5.1.48-bin.jar:5.1.48]
	at com.mysql.jdbc.PreparedStatement.executeLargeUpdate(PreparedStatement.java:5175) ~[mysql-connector-java-5.1.48-bin.jar:5.1.48]
	at com.mysql.jdbc.PreparedStatement.executeUpdate(PreparedStatement.java:2052) ~[mysql-connector-java-5.1.48-bin.jar:5.1.48]
	at org.gof.core.dbsrv.DBLineService.execute(DBLineService.java:254) ~[bin/:?]
	... 6 more
2025-05-30 16:54:06,542 (ClassScanProcess.java:135) [INFO][GAME] 开始一次扫描key=363957321744344066869,jarValue=363957321744344066869
2025-05-30 16:54:06,560 (RankManager.java:967) [ERROR][TEMP] ===保存数据失败keys=[rankSn_list:_1003_30417, , ]
