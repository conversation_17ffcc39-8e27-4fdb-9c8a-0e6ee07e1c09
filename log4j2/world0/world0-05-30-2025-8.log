2025-05-30 17:40:34,284 (WorldStartup.java:69) [INFO][GAME] 正在启动游戏服务器
2025-05-30 17:40:34,351 (WorldStartup.java:83) [INFO][GAME] 正在初始化事件容器
2025-05-30 17:40:34,978 (WorldStartup.java:86) [INFO][GAME] 正在初始化协议函数指针池
2025-05-30 17:40:39,605 (WorldStartup.java:94) [INFO][GAME] 加载策划数据
2025-05-30 17:40:40,305 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfBreakBigPrizePreview
2025-05-30 17:40:41,375 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfconfigAngel
2025-05-30 17:40:41,716 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyAdvance_0
2025-05-30 17:40:41,722 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyCd_0
2025-05-30 17:40:41,754 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEntry_0
2025-05-30 17:40:41,761 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEvolutionPro_0
2025-05-30 17:40:41,777 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyHybrid_0
2025-05-30 17:40:41,801 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyLevel_0
2025-05-30 17:40:41,817 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_achievement
2025-05-30 17:40:41,819 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_advance_0
2025-05-30 17:40:42,184 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfLoopBreakCumulativeTimes
2025-05-30 17:40:44,028 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonEquipment
2025-05-30 17:40:44,062 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonShipAppearance
2025-05-30 17:40:44,135 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverActivityGroup
2025-05-30 17:40:44,136 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverGroup
2025-05-30 17:40:44,299 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSlimeDungeon
2025-05-30 17:40:45,907 (GlobalConfVal.java:1310) [INFO][TEMP] ===加载副本表数量=70
2025-05-30 17:40:46,302 (GlobalConfVal.java:502) [INFO][TEMP] ===crossGroupSnInfoMap=[4, 6, 7, 8, 9, 10]
2025-05-30 17:40:46,305 (GlobalConfVal.java:2652) [INFO][TEMP] crossWarAttrBonusMap={1={0={1002=2000}, 1={1024=2000, 1001=2000}}, 3={0={1002=2000}, 2={1024=2000, 1001=2000}, 3={1024=2000, 1001=2000}}}
2025-05-30 17:40:46,306 (GlobalConfVal.java:2666) [INFO][TEMP] crossWarPlayerScoreList=[[1, 100, 100], [101, 105, 200], [106, 110, 300], [111, 115, 400], [116, 120, 500], [121, 125, 600], [126, 130, 700], [131, 135, 800], [136, 140, 900], [141, 150, 1000]]
2025-05-30 17:40:46,312 (GlobalConfVal.java:2667) [INFO][TEMP] crossWarMonsterScoreMap={1200002=75, 1200003=100, 1200001=50, 1200006=175, 1200007=200, 1200004=125, 1200005=150, 1200010=275, 1200011=300, 1200008=225, 1200009=250, 1200012=325}
2025-05-30 17:40:46,313 (GlobalConfVal.java:2668) [INFO][TEMP] crossWarSonOfLightScoreInfo=[10000, 1, 5]
2025-05-30 17:40:48,051 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379
2025-05-30 17:40:49,149 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/1
2025-05-30 17:40:49,161 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/2
2025-05-30 17:40:49,450 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 17:40:49,504 (EntityManager.java:58) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2025-05-30 17:40:49,508 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 17:40:49,509 (EntityManager.java:64) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin redis 
2025-05-30 17:40:49,511 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 17:40:49,511 (EntityManager.java:69) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin cross redis 
2025-05-30 17:40:49,513 (WorldStartup.java:129) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://127.0.0.1:20108
2025-05-30 17:40:49,538 (WorldStartup.java:132) [INFO][GAME] server init : begin start node...
2025-05-30 17:40:51,103 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2025-05-30 17:40:51,116 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2025-05-30 17:40:51,185 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-05-30 17:40:51,195 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2025-05-30 17:40:51,199 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-05-30 17:40:51,200 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2025-05-30 17:40:51,517 (WorldStartup.java:148) [INFO][GAME] server init : begin start platform...
2025-05-30 17:40:52,378 (WorldStartup.java:160) [INFO][GAME] server init : default port started...
2025-05-30 17:40:52,503 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2025-05-30 17:40:52,520 (ServerList.java:62) [ERROR][TEMP] 模式 true true false
2025-05-30 17:40:52,521 (WorldStartup.java:196) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2025-05-30 17:40:52,568 (GameServiceManager.java:32) [INFO][GAME] 开始初始化service
2025-05-30 17:40:55,651 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 17:40:56,866 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 17:40:57,119 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 17:40:57,331 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 17:40:57,549 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 17:40:57,693 (ActivityControlService.java:133) [INFO][CROSS_WAR] init m_ttCrossWarReward={"running":true,"interval":604800000,"startTime":"2025-06-01 23:15:00","nextTime":"2025-06-01 23:15:00"}
2025-05-30 17:40:57,752 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 17:40:58,001 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 17:40:58,214 (CarParkService.java:93) [ERROR][CAR_PARK] 开始加载公共停车场数据：3002401,3002402,3002403,3002404,3002405
2025-05-30 17:40:58,229 (ArenaRankedService.java:78) [INFO][TEMP] ===初始化跨服排位赛服务, serverId=30024
2025-05-30 17:40:58,243 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 17:40:58,264 (ActivityServerData.java:200) [INFO][TEMP] ===检查活动循环初始化：serverId=30024 , roundSnList=[120001, 100001, 80001, 200001, 140001, 110001, 90001, 130001, 50001, 170001, 70002, 330001]
2025-05-30 17:40:58,265 (ActivityServerData.java:213) [INFO][TEMP] ===检查活动循环初始化：serverId=30024 , roundSnList=[120001, 100001, 80001, 200001, 140001, 110001, 90001, 130001, 50001, 170001, 70002, 330001]
2025-05-30 17:40:58,353 (CarParkService.java:116) [ERROR][CAR_PARK] 加载公共停车场数据成功返回数量:5
2025-05-30 17:40:58,359 (GuildLeagueWarmUpService.java:863) [INFO][TEMP] ===serverId:30024, 乱斗是否跨服=true
2025-05-30 17:40:58,363 (CheckWorldService.java:101) [INFO][TEMP] ===代码未实现，检测的先不处理
2025-05-30 17:40:58,393 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002401
2025-05-30 17:40:58,394 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002402
2025-05-30 17:40:58,394 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002403
2025-05-30 17:40:58,394 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002404
2025-05-30 17:40:58,395 (CarParkService.java:127) [ERROR][CAR_PARK] 成功加载公共停车场数据: 3002405
2025-05-30 17:40:58,398 (ArenaService.java:92) [INFO][TEMP] ===初始化本服竞技场服务, 1737300900000
2025-05-30 17:40:58,399 (ArenaService.java:99) [INFO][TEMP] 本服竞技场赛季已经结束，不初始化， confSeason=3, timeNow=1748598058349, serverId 1 -> 60 timeNext=1736696100000, serverNoId=24
2025-05-30 17:40:58,427 (ArenaRankedService.java:1063) [ERROR][TEMP] ===arenaBridgeRankRoomKey:0数据为空。无需结算排位赛
2025-05-30 17:40:58,664 (PackageClass.java:228) [INFO][TEMP] ===findByFile=H:\Projects\sword\dev\server\lzWorldSrv\bin\org\gof\demo\worldsrv\msg continue!!!
2025-05-30 17:40:58,779 (GuildService.java:174) [INFO][TEMP] =================init GuildService
2025-05-30 17:40:58,785 (GuildService.java:264) [INFO][TEMP] ===gve开始时间=1748604300000，2025-05-30 19:25:00
2025-05-30 17:40:58,789 (GuildService.java:269) [INFO][TEMP] ===gve结束时间=1748604900000，2025-05-30 19:35:00
2025-05-30 17:40:59,284 (GuildService.java:469) [ERROR][TEMP] loadGuild 600240000011000281 end
2025-05-30 17:40:59,370 (WorldStartup.java:230) [INFO][GAME] server init : begin connect admin...
2025-05-30 17:40:59,375 (WorldStartup.java:235) [INFO][TEMP] ===连接中心管理服， nid=admin0, localBridgeId=world0， org.gof.core.RemoteNode@1a3b1f7e[remoteId=admin0,nodeType=ADMIN,remoteAddr=tcp://127.0.0.1:13000,localAlias=world0_30024,connected=false,main=true,createTime=2025-05-30 17:40:59,rogerTime=2025-05-30 17:40:59]
2025-05-30 17:40:59,378 (WorldStartup.java:240) [INFO][GAME] ====================
2025-05-30 17:40:59,378 (WorldStartup.java:241) [INFO][GAME] world0 started.
2025-05-30 17:40:59,379 (WorldStartup.java:242) [INFO][GAME] Listen:tcp://127.0.0.1:20108
2025-05-30 17:40:59,379 (WorldStartup.java:243) [INFO][GAME] ====================
2025-05-30 17:40:59,383 (HumanManager.java:5198) [INFO][TEMP] ===去后台请求屏蔽字库
2025-05-30 17:40:59,385 (HumanManager.java:5239) [INFO][TEMP] ===去后台请求跑马灯
2025-05-30 17:40:59,757 (WorldStartup.java:287) [INFO][GAME] 开启数据热更新扫描...
2025-05-30 17:40:59,722 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,766 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,770 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,772 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,779 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,804 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,805 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,806 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,814 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,817 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,820 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,833 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,834 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,835 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,842 (ClassScanProcess.java:58) [INFO][GAME] 开启类热更新扫描调度，每5分钟执行一次
2025-05-30 17:40:59,843 (WorldStartup.java:291) [INFO][GAME] 开启类热更新扫描...
2025-05-30 17:40:59,850 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,851 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,853 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,855 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,855 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,856 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,859 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,860 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,883 (WorldStartup.java:303) [ERROR][GAME] 开启连接服务
2025-05-30 17:40:59,884 (WorldStartup.java:305) [ERROR][GAME] 启动完成...
2025-05-30 17:40:59,906 (ServCheck.java:68) [ERROR][GAME] 
╔═══════════════════╤══════════════╗
║ service           │ costTime(ms) ║
╠═══════════════════╪══════════════╣
║ guild             │ 520          ║
╟───────────────────┼──────────────╢
║ worldBoss         │ 364          ║
╟───────────────────┼──────────────╢
║ flyHybrid         │ 96           ║
╟───────────────────┼──────────────╢
║ activity          │ 73           ║
╟───────────────────┼──────────────╢
║ farm              │ 68           ║
╟───────────────────┼──────────────╢
║ name              │ 46           ║
╟───────────────────┼──────────────╢
║ arena             │ 31           ║
╟───────────────────┼──────────────╢
║ carPark           │ 8            ║
╟───────────────────┼──────────────╢
║ guildLeagueWarmUp │ 3            ║
╟───────────────────┼──────────────╢
║ fillMail          │ 1            ║
╟───────────────────┼──────────────╢
║ arenaRanked       │ 1            ║
╟───────────────────┼──────────────╢
║                   │ 0            ║
╟───────────────────┼──────────────╢
║ checkWorld        │ 0            ║
╟───────────────────┼──────────────╢
║ mail              │ 0            ║
╟───────────────────┼──────────────╢
║ httpPush          │ 0            ║
╟───────────────────┼──────────────╢
║ pocketLine        │ 0            ║
╟───────────────────┼──────────────╢
║ serverSelect      │ 0            ║
╟───────────────────┼──────────────╢
║ humanGlobal       │ 0            ║
╟───────────────────┼──────────────╢
║ rank              │ 0            ║
╟───────────────────┼──────────────╢
║ dataReset         │ 0            ║
╟───────────────────┼──────────────╢
║ gm                │ 0            ║
╟───────────────────┼──────────────╢
║ team              │ 0            ║
╟───────────────────┼──────────────╢
║ humanCreateApply  │ 0            ║
╟───────────────────┼──────────────╢
║ confirm           │ 0            ║
╟───────────────────┼──────────────╢
║ chat              │ 0            ║
╚═══════════════════╧══════════════╝

2025-05-30 17:40:59,918 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,919 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,919 (:) [ERROR][io.vertx.core.impl.ContextImpl] Unhandled exception
java.lang.NullPointerException: null
	at org.gof.core.dbsrv.DBPartServiceProxy.get(DBPartServiceProxy.java:423) ~[bin/:?]
	at org.gof.core.dbsrv.DB.get(DB.java:221) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$loadFromDbToCache$53(EntityManager.java:1108) ~[bin/:?]
	at org.gof.core.dbsrv.redis.AsyncActionResult.lambda$futureSuccess$3(AsyncActionResult.java:60) ~[bin/:?]
	at io.vertx.core.impl.future.FutureImpl$1.onSuccess(FutureImpl.java:91) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl$ListenerArray.onSuccess(FutureImpl.java:310) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl.tryComplete(FutureImpl.java:259) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.Promise.complete(Promise.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at org.gof.core.dbsrv.redis.RedisTools.lambda$exists$52(RedisTools.java:1098) ~[bin/:?]
	at io.vertx.core.impl.future.FutureImpl$4.onSuccess(FutureImpl.java:176) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl.tryComplete(FutureImpl.java:259) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.Transformation$1.onSuccess(Transformation.java:61) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.SucceededFuture.addListener(SucceededFuture.java:88) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.Transformation.onSuccess(Transformation.java:42) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.lambda$emitSuccess$0(FutureBase.java:60) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:174) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) [netty-transport-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,920 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,930 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,939 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,941 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,942 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,943 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,943 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,944 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,945 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,946 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,949 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,951 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,952 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,953 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,953 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:40:59,953 (Port.java:503) [ERROR][CORE] pulseCallResults exception:java.lang.NullPointerException 
java.lang.NullPointerException: null
	at org.gof.demo.worldsrv.worldBoss.WorldBossService.lambda$null$1(WorldBossService.java:162) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$getListByPage$3(HumanData.java:324) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$doGetList$4(HumanData.java:367) ~[bin/:?]
	at org.gof.demo.worldsrv.human.HumanData.lambda$batchLoad$6(HumanData.java:479) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$null$4(EntityManager.java:421) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$batchGetEntityFromDB$1(EntityManager.java:314) ~[bin/:?]
	at org.gof.core.CallResultAsync.onResult(CallResultAsync.java:70) ~[bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:496) [bin/:?]
	at org.gof.core.Port.pulseCallResults(Port.java:478) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:271) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:41:03,475 (Port.java:635) [ERROR][CORE] 执行Action回调时发生错误: java.lang.ClassCastException: java.lang.Integer cannot be cast to java.lang.Long
java.lang.ClassCastException: java.lang.Integer cannot be cast to java.lang.Long
	at test.org.gof.redis.DBHelperTest.lambda$testCountByQuery$0(DBHelperTest.java:51) ~[bin/:?]
	at org.gof.core.Port.pulseActions(Port.java:632) [bin/:?]
	at org.gof.core.Port.pulseActions(Port.java:622) [bin/:?]
	at org.gof.core.Port.pulse(Port.java:291) [bin/:?]
	at org.gof.core.Port.caseRunOnce(Port.java:1243) [bin/:?]
	at org.gof.core.support.ThreadHandler.run(ThreadHandler.java:83) [bin/:?]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2025-05-30 17:41:09,845 (ClassScanProcess.java:135) [INFO][GAME] 开始一次扫描key=363957321744344066869,jarValue=363957321744344066869
2025-05-30 17:41:35,353 (WorldStartup.java:248) [INFO][GAME] 触发关闭服务器操作,开始踢人
2025-05-30 17:41:35,372 (HumanGlobalService.java:639) [INFO][GAME] ===已踢出所有玩家
2025-05-30 17:41:35,405 (CarParkService.java:211) [ERROR][GAME] 保存停车场数据完成
