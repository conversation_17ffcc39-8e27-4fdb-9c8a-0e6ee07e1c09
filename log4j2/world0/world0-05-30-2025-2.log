2025-05-30 16:45:58,482 (WorldStartup.java:69) [INFO][GAME] 正在启动游戏服务器
2025-05-30 16:45:58,531 (WorldStartup.java:83) [INFO][GAME] 正在初始化事件容器
2025-05-30 16:45:59,019 (WorldStartup.java:86) [INFO][GAME] 正在初始化协议函数指针池
2025-05-30 16:46:02,354 (WorldStartup.java:94) [INFO][GAME] 加载策划数据
2025-05-30 16:46:02,827 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfBreakBigPrizePreview
2025-05-30 16:46:03,717 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfconfigAngel
2025-05-30 16:46:03,965 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyAdvance_0
2025-05-30 16:46:03,969 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyCd_0
2025-05-30 16:46:03,990 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEntry_0
2025-05-30 16:46:03,994 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEvolutionPro_0
2025-05-30 16:46:04,006 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyHybrid_0
2025-05-30 16:46:04,023 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyLevel_0
2025-05-30 16:46:04,032 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_achievement
2025-05-30 16:46:04,033 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_advance_0
2025-05-30 16:46:04,177 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfLoopBreakCumulativeTimes
2025-05-30 16:46:05,307 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonEquipment
2025-05-30 16:46:05,329 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonShipAppearance
2025-05-30 16:46:05,375 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverActivityGroup
2025-05-30 16:46:05,375 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverGroup
2025-05-30 16:46:05,493 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSlimeDungeon
2025-05-30 16:46:06,471 (GlobalConfVal.java:1310) [INFO][TEMP] ===加载副本表数量=70
2025-05-30 16:46:06,736 (GlobalConfVal.java:502) [INFO][TEMP] ===crossGroupSnInfoMap=[4, 6, 7, 8, 9, 10]
2025-05-30 16:46:06,737 (GlobalConfVal.java:2652) [INFO][TEMP] crossWarAttrBonusMap={1={0={1002=2000}, 1={1024=2000, 1001=2000}}, 3={0={1002=2000}, 2={1024=2000, 1001=2000}, 3={1024=2000, 1001=2000}}}
2025-05-30 16:46:06,738 (GlobalConfVal.java:2666) [INFO][TEMP] crossWarPlayerScoreList=[[1, 100, 100], [101, 105, 200], [106, 110, 300], [111, 115, 400], [116, 120, 500], [121, 125, 600], [126, 130, 700], [131, 135, 800], [136, 140, 900], [141, 150, 1000]]
2025-05-30 16:46:06,738 (GlobalConfVal.java:2667) [INFO][TEMP] crossWarMonsterScoreMap={1200002=75, 1200003=100, 1200001=50, 1200006=175, 1200007=200, 1200004=125, 1200005=150, 1200010=275, 1200011=300, 1200008=225, 1200009=250, 1200012=325}
2025-05-30 16:46:06,738 (GlobalConfVal.java:2668) [INFO][TEMP] crossWarSonOfLightScoreInfo=[10000, 1, 5]
2025-05-30 16:46:08,118 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379
2025-05-30 16:46:08,822 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/1
2025-05-30 16:46:08,831 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/2
2025-05-30 16:46:09,041 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 16:46:09,085 (EntityManager.java:58) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2025-05-30 16:46:09,087 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 16:46:09,089 (EntityManager.java:64) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin redis 
2025-05-30 16:46:09,090 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 16:46:09,090 (EntityManager.java:69) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin cross redis 
2025-05-30 16:46:09,091 (WorldStartup.java:129) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://127.0.0.1:20108
2025-05-30 16:46:09,111 (WorldStartup.java:132) [INFO][GAME] server init : begin start node...
2025-05-30 16:46:10,305 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2025-05-30 16:46:10,313 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2025-05-30 16:46:10,356 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-05-30 16:46:10,363 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2025-05-30 16:46:10,365 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-05-30 16:46:10,366 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2025-05-30 16:46:10,609 (WorldStartup.java:148) [INFO][GAME] server init : begin start platform...
2025-05-30 16:46:11,325 (WorldStartup.java:160) [INFO][GAME] server init : default port started...
2025-05-30 16:46:11,422 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2025-05-30 16:46:11,423 (ServerList.java:62) [ERROR][TEMP] 模式 true true false
2025-05-30 16:46:11,424 (WorldStartup.java:196) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2025-05-30 16:46:11,460 (GameServiceManager.java:32) [INFO][GAME] 开始初始化service
