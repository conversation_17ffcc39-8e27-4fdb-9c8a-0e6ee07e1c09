2024-12-17 15:54:15,342 (WorldStartup.java:69) [INFO][GAME] 正在启动游戏服务器
2024-12-17 15:54:15,397 (WorldStartup.java:83) [INFO][GAME] 正在初始化事件容器
2024-12-17 15:54:15,855 (WorldStartup.java:86) [INFO][GAME] 正在初始化协议函数指针池
2024-12-17 15:54:18,655 (WorldStartup.java:94) [INFO][GAME] 加载策划数据
2024-12-17 15:54:19,132 (ConfBreakBigPrizePreview.java:283) [INFO][TEMP] 表ConfBreakBigPrizePreview有问题!
2024-12-17 15:54:20,325 (ConfFlyEgg.java:297) [INFO][TEMP] 表ConfFlyEgg有问题!
2024-12-17 15:54:20,381 (ConfFlyRemakeCost.java:269) [INFO][TEMP] 表ConfFlyRemakeCost有问题!
2024-12-17 15:54:20,384 (ConfFlyResolveReturn.java:276) [INFO][TEMP] 表ConfFlyResolveReturn有问题!
2024-12-17 15:54:20,388 (ConfFlyTotalAchievement.java:290) [INFO][TEMP] 表ConfFlyTotalAchievement有问题!
2024-12-17 15:54:20,392 (ConfFly_achievement.java:304) [INFO][TEMP] 表ConfFly_achievement有问题!
2024-12-17 15:54:22,982 (GlobalConfVal.java:2152) [INFO][TEMP] quizDiceRewardMap={3={7001=50}, 4={7001=50}, 5={7001=50}, 6={7001=50}, 7={7001=60}, 8={7001=70}, 9={7001=80}, 10={2=10, 7001=90}, 11={2=15, 7001=100}, 12={2=20, 7001=110}, 13={2=30, 7001=130}, 14={2=40, 7001=180}, 15={2=50, 7001=270}, 16={2=66, 7001=450}, 17={2=88, 7001=888, 1017=66}, 18={2=166, 7001=2666, 1017=88}}
2024-12-17 15:54:22,983 (GlobalConfVal.java:2173) [INFO][TEMP] quizRankRewardMap={1={1001=150, 7001=500}, 2={1001=100, 7001=400}, 3={1001=80, 7001=300}, 4={1001=50, 7001=200}, 5={1001=50, 7001=200}, 6={1001=20, 7001=100}}
2024-12-17 15:54:22,984 (GlobalConfVal.java:2188) [INFO][TEMP] quizRankRewardExpMap={1=5000, 2=4000, 3=3000, 4=2000, 5=2000, 6=1000}
2024-12-17 15:54:23,469 (GlobalConfVal.java:1208) [INFO][TEMP] ===加载副本表数量=70
2024-12-17 15:54:24,419 (GlobalConfVal.java:452) [INFO][TEMP] ===crossGroupSnInfoMap={4=org.gof.demo.distr.cross.domain.CrossGroupInfo@68f6e55d, 5=org.gof.demo.distr.cross.domain.CrossGroupInfo@6fff46bf, 6=org.gof.demo.distr.cross.domain.CrossGroupInfo@1835dc92, 7=org.gof.demo.distr.cross.domain.CrossGroupInfo@3aaa3c39}
2024-12-17 15:54:25,459 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379
2024-12-17 15:54:25,699 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/1
2024-12-17 15:54:25,704 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/2
2024-12-17 15:54:25,935 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2024-12-17 15:54:25,950 (EntityManager.java:55) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2024-12-17 15:54:25,956 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2024-12-17 15:54:25,956 (EntityManager.java:61) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin redis 
2024-12-17 15:54:25,957 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2024-12-17 15:54:25,957 (EntityManager.java:66) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin cross redis 
2024-12-17 15:54:25,958 (WorldStartup.java:129) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://127.0.0.1:20108
2024-12-17 15:54:25,977 (WorldStartup.java:132) [INFO][GAME] server init : begin start node...
2024-12-17 15:54:26,299 (FieldTable.java:58) [INFO][GAME] server init : db executeQuery...
2024-12-17 15:54:26,359 (FieldTable.java:69) [INFO][GAME] server init : initTable...
2024-12-17 15:54:26,426 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2024-12-17 15:54:26,431 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2024-12-17 15:54:26,453 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2024-12-17 15:54:26,455 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2024-12-17 15:54:26,456 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2024-12-17 15:54:26,456 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2024-12-17 15:54:26,647 (WorldStartup.java:148) [INFO][GAME] server init : begin start platform...
2024-12-17 15:54:27,309 (WorldStartup.java:160) [INFO][GAME] server init : default port started...
2024-12-17 15:54:27,340 (WorldStartup.java:196) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2024-12-17 15:54:27,352 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2024-12-17 15:54:27,385 (GameServiceManager.java:27) [INFO][GAME] 开始初始化service
2024-12-17 15:54:38,230 (ActivityServerData.java:132) [INFO][TEMP] ===检查活动循环初始化：serverId=300572 , roundSnList=[120001, 90001, 110001, 50001, 200001, 140001]
2024-12-17 15:54:38,233 (ActivityServerData.java:145) [INFO][TEMP] ===检查活动循环初始化：serverId=300572 , roundSnList=[120001, 90001, 110001, 50001, 200001, 140001]
2024-12-17 15:54:39,556 (CheckWorldService.java:101) [INFO][TEMP] ===代码未实现，检测的先不处理
2024-12-17 15:54:39,565 (GuildLeagueWarmUpService.java:1199) [ERROR][TEMP] ===timeNow=1734422079548, interval=101120452，开始战斗时间2024-12-18 20:00:00
2024-12-17 15:54:40,113 (GuildService.java:176) [INFO][TEMP] =================init GuildService
2024-12-17 15:54:40,114 (GuildService.java:266) [INFO][TEMP] ===gve开始时间=1734434700000，2024-12-17 19:25:00
2024-12-17 15:54:40,114 (GuildService.java:271) [INFO][TEMP] ===gve结束时间=1734435300000，2024-12-17 19:35:00
2024-12-17 15:54:40,235 (GuildService.java:335) [INFO][TEMP] initCheckRank 0
2024-12-17 15:54:40,251 (WorldStartup.java:230) [INFO][GAME] server init : begin connect admin...
2024-12-17 15:54:40,255 (WorldStartup.java:235) [INFO][TEMP] ===连接中心管理服， nid=admin0, localBridgeId=world0， org.gof.core.RemoteNode@352e4b6d[remoteId=admin0,nodeType=ADMIN,remoteAddr=tcp://127.0.0.1:13000,localAlias=world0_300572,connected=false,main=true,createTime=2024-12-17 15:54:40,rogerTime=2024-12-17 15:54:40]
2024-12-17 15:54:40,256 (WorldStartup.java:240) [INFO][GAME] ====================
2024-12-17 15:54:40,256 (WorldStartup.java:241) [INFO][GAME] world0 started.
2024-12-17 15:54:40,256 (WorldStartup.java:242) [INFO][GAME] Listen:tcp://127.0.0.1:20108
2024-12-17 15:54:40,256 (WorldStartup.java:243) [INFO][GAME] ====================
2024-12-17 15:54:40,268 (HumanManager.java:4819) [INFO][TEMP] ===去后台请求屏蔽字库
2024-12-17 15:54:40,268 (HumanManager.java:4860) [INFO][TEMP] ===去后台请求跑马灯
2024-12-17 15:54:40,356 (WorldStartup.java:279) [INFO][GAME] 开启数据热更新扫描...
2024-12-17 15:54:40,408 (ClassScanProcess.java:58) [INFO][GAME] 开启类热更新扫描调度，每5分钟执行一次
2024-12-17 15:54:40,409 (WorldStartup.java:283) [INFO][GAME] 开启类热更新扫描...
2024-12-17 15:54:40,423 (WorldStartup.java:295) [ERROR][GAME] 开启连接服务
2024-12-17 15:54:40,424 (WorldStartup.java:297) [ERROR][GAME] 启动完成...
2024-12-17 15:54:40,432 (ServCheck.java:68) [ERROR][GAME] 
╔═══════════════════╤══════════════╗
║ service           │ costTime(ms) ║
╠═══════════════════╪══════════════╣
║ guild             │ 124          ║
╟───────────────────┼──────────────╢
║ name              │ 16           ║
╟───────────────────┼──────────────╢
║ worldBoss         │ 9            ║
╟───────────────────┼──────────────╢
║ activity          │ 8            ║
╟───────────────────┼──────────────╢
║ farm              │ 8            ║
╟───────────────────┼──────────────╢
║ guildLeagueWarmUp │ 5            ║
╟───────────────────┼──────────────╢
║ arena             │ 4            ║
╟───────────────────┼──────────────╢
║ carPark           │ 3            ║
╟───────────────────┼──────────────╢
║ fillMail          │ 1            ║
╟───────────────────┼──────────────╢
║ arenaRanked       │ 1            ║
╟───────────────────┼──────────────╢
║                   │ 0            ║
╟───────────────────┼──────────────╢
║ checkWorld        │ 0            ║
╟───────────────────┼──────────────╢
║ mail              │ 0            ║
╟───────────────────┼──────────────╢
║ httpPush          │ 0            ║
╟───────────────────┼──────────────╢
║ pocketLine        │ 0            ║
╟───────────────────┼──────────────╢
║ humanGlobal       │ 0            ║
╟───────────────────┼──────────────╢
║ rank              │ 0            ║
╟───────────────────┼──────────────╢
║ dataReset         │ 0            ║
╟───────────────────┼──────────────╢
║ gm                │ 0            ║
╟───────────────────┼──────────────╢
║ team              │ 0            ║
╟───────────────────┼──────────────╢
║ humanCreateApply  │ 0            ║
╟───────────────────┼──────────────╢
║ confirm           │ 0            ║
╟───────────────────┼──────────────╢
║ chat              │ 0            ║
╟───────────────────┼──────────────╢
║ captureSlave      │ 0            ║
╚═══════════════════╧══════════════╝

2024-12-17 15:54:40,439 (:) [ERROR][io.vertx.core.impl.ContextImpl] Unhandled exception
java.lang.NullPointerException: null
	at org.gof.core.dbsrv.DBPartServiceProxy.get(DBPartServiceProxy.java:423) ~[bin/:?]
	at org.gof.core.dbsrv.DB.get(DB.java:221) ~[bin/:?]
	at org.gof.core.dbsrv.redis.EntityManager.lambda$loadFromDbToCache$53(EntityManager.java:1093) ~[bin/:?]
	at org.gof.core.dbsrv.redis.AsyncActionResult.lambda$futureSuccess$3(AsyncActionResult.java:60) ~[bin/:?]
	at io.vertx.core.impl.future.FutureImpl$1.onSuccess(FutureImpl.java:91) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl$ListenerArray.onSuccess(FutureImpl.java:310) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl.tryComplete(FutureImpl.java:259) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.Promise.complete(Promise.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at org.gof.core.dbsrv.redis.RedisTools.lambda$exists$52(RedisTools.java:1081) ~[bin/:?]
	at io.vertx.core.impl.future.FutureImpl$4.onSuccess(FutureImpl.java:176) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl.tryComplete(FutureImpl.java:259) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.Transformation$1.onSuccess(Transformation.java:61) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.SucceededFuture.addListener(SucceededFuture.java:88) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.Transformation.onSuccess(Transformation.java:42) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.lambda$emitSuccess$0(FutureBase.java:60) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:174) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) [netty-transport-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) [netty-common-4.1.89.Final.jar:4.1.89.Final]
	at java.lang.Thread.run(Thread.java:748) [?:1.8.0_211]
2024-12-17 15:55:01,458 (ClassScanProcess.java:135) [INFO][GAME] 开始一次扫描key=216594811732434223521,jarValue=216594811732434223521
2024-12-17 15:55:03,979 (EntityManagerTest.java:63) [INFO][test.org.gof.test.TestWorldBase] id=400060000030700001, account={}, name={}
