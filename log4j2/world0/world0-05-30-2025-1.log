2025-05-30 16:45:07,068 (WorldStartup.java:69) [INFO][GAME] 正在启动游戏服务器
2025-05-30 16:45:07,148 (WorldStartup.java:83) [INFO][GAME] 正在初始化事件容器
2025-05-30 16:45:07,650 (WorldStartup.java:86) [INFO][GAME] 正在初始化协议函数指针池
2025-05-30 16:45:11,418 (WorldStartup.java:94) [INFO][GAME] 加载策划数据
2025-05-30 16:45:11,959 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfBreakBigPrizePreview
2025-05-30 16:45:12,874 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfconfigAngel
2025-05-30 16:45:13,124 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyAdvance_0
2025-05-30 16:45:13,128 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyCd_0
2025-05-30 16:45:13,149 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEntry_0
2025-05-30 16:45:13,153 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyEvolutionPro_0
2025-05-30 16:45:13,166 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyHybrid_0
2025-05-30 16:45:13,185 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFlyLevel_0
2025-05-30 16:45:13,196 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_achievement
2025-05-30 16:45:13,197 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfFly_advance_0
2025-05-30 16:45:13,376 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfLoopBreakCumulativeTimes
2025-05-30 16:45:14,622 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonEquipment
2025-05-30 16:45:14,648 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeasonShipAppearance
2025-05-30 16:45:14,702 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverActivityGroup
2025-05-30 16:45:14,703 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSeverGroup
2025-05-30 16:45:14,837 (DataReloadManager.java:143) [INFO][GAME] 加载策划配置失败 没有json文件对应的class, classNme=org.gof.demo.worldsrv.config.ConfSlimeDungeon
2025-05-30 16:45:15,857 (GlobalConfVal.java:1310) [INFO][TEMP] ===加载副本表数量=70
2025-05-30 16:45:16,292 (GlobalConfVal.java:502) [INFO][TEMP] ===crossGroupSnInfoMap=[4, 6, 7, 8, 9, 10]
2025-05-30 16:45:16,294 (GlobalConfVal.java:2652) [INFO][TEMP] crossWarAttrBonusMap={1={0={1002=2000}, 1={1024=2000, 1001=2000}}, 3={0={1002=2000}, 2={1024=2000, 1001=2000}, 3={1024=2000, 1001=2000}}}
2025-05-30 16:45:16,296 (GlobalConfVal.java:2666) [INFO][TEMP] crossWarPlayerScoreList=[[1, 100, 100], [101, 105, 200], [106, 110, 300], [111, 115, 400], [116, 120, 500], [121, 125, 600], [126, 130, 700], [131, 135, 800], [136, 140, 900], [141, 150, 1000]]
2025-05-30 16:45:16,297 (GlobalConfVal.java:2667) [INFO][TEMP] crossWarMonsterScoreMap={1200002=75, 1200003=100, 1200001=50, 1200006=175, 1200007=200, 1200004=125, 1200005=150, 1200010=275, 1200011=300, 1200008=225, 1200009=250, 1200012=325}
2025-05-30 16:45:16,297 (GlobalConfVal.java:2668) [INFO][TEMP] crossWarSonOfLightScoreInfo=[10000, 1, 5]
2025-05-30 16:45:17,733 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379
2025-05-30 16:45:18,433 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/1
2025-05-30 16:45:18,441 (RedisTools.java:62) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379/2
2025-05-30 16:45:18,640 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 16:45:18,690 (EntityManager.java:58) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2025-05-30 16:45:18,693 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 16:45:18,695 (EntityManager.java:64) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin redis 
2025-05-30 16:45:18,696 (RedisTools.java:70) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2025-05-30 16:45:18,697 (EntityManager.java:69) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init admin cross redis 
2025-05-30 16:45:18,698 (WorldStartup.java:129) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://127.0.0.1:20108
2025-05-30 16:45:18,718 (WorldStartup.java:132) [INFO][GAME] server init : begin start node...
2025-05-30 16:45:19,923 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2025-05-30 16:45:19,932 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2025-05-30 16:45:19,975 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-05-30 16:45:19,983 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2025-05-30 16:45:19,985 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2025-05-30 16:45:19,985 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2025-05-30 16:45:20,270 (WorldStartup.java:148) [INFO][GAME] server init : begin start platform...
2025-05-30 16:45:20,979 (WorldStartup.java:160) [INFO][GAME] server init : default port started...
2025-05-30 16:45:21,054 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2025-05-30 16:45:21,063 (ServerList.java:62) [ERROR][TEMP] 模式 true true false
2025-05-30 16:45:21,063 (WorldStartup.java:196) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2025-05-30 16:45:21,103 (GameServiceManager.java:32) [INFO][GAME] 开始初始化service
