2024-11-04 00:14:06,073 (WorldStartup.java:76) [INFO][GAME] 正在启动游戏服务器
2024-11-04 00:14:06,083 (WorldStartup.java:87) [INFO][GAME] 正在初始化事件容器
2024-11-04 00:14:06,477 (WorldStartup.java:90) [INFO][GAME] 正在初始化协议函数指针池
2024-11-04 00:14:08,857 (WorldStartup.java:98) [INFO][GAME] 加载策划数据
2024-11-04 00:14:09,271 (ConfBreakBigPrizePreview.java:283) [INFO][TEMP] 表ConfBreakBigPrizePreview有问题!
2024-11-04 00:14:14,074 (GlobalConfVal.java:1009) [INFO][TEMP] ===加载副本表数量=70
2024-11-04 00:14:15,115 (WorldStartup.java:106) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://************:20108
2024-11-04 00:14:16,186 (RedisTools.java:43) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://192.168.0.223:6379
2024-11-04 00:14:16,541 (RedisTools.java:51) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2024-11-04 00:14:16,561 (MysqlTool.java:97) [INFO][org.gof.core.dbsrv.redis.MysqlTool] MySQLPool setIdleTimeout:0
2024-11-04 00:14:16,596 (MysqlTool.java:40) [INFO][org.gof.core.dbsrv.redis.MysqlTool] ===createMySQLPool poolOptions={"connectionTimeout":30,"connectionTimeoutUnit":"SECONDS","eventLoopSize":0,"idleTimeout":0,"idleTimeoutUnit":"SECONDS","maxLifetime":0,"maxLifetimeUnit":"SECONDS","maxSize":40,"maxWaitQueueSize":-1,"name":"__vertx.DEFAULT","poolCleanerPeriod":1000,"shared":false}
2024-11-04 00:14:16,612 (EntityManager.java:50) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2024-11-04 00:14:22,278 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:14:22,325 (DBStartup.java:57) [INFO][CORE] ===初始化表信息sword_game_dev, ***********************************************************************************************************************, root, 123456
2024-11-04 00:14:22,385 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2024-11-04 00:14:22,389 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2024-11-04 00:14:22,411 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2024-11-04 00:14:22,412 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2024-11-04 00:14:22,413 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2024-11-04 00:14:22,414 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2024-11-04 00:14:22,423 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@70997a94[nodeId=world0,portId=dbPart0]
2024-11-04 00:14:22,433 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@6ee5f485, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-04 00:14:22,434 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@77b5148c[nodeId=world0,portId=dbPart1]
2024-11-04 00:14:22,435 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@36359723, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-04 00:14:22,436 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@49fdbe2b[nodeId=world0,portId=dbPart2]
2024-11-04 00:14:22,436 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@53eba4b8, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-04 00:14:22,438 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@6fa02932[nodeId=world0,portId=dbLine0]
2024-11-04 00:14:22,439 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@7c46c9c3, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-04 00:14:22,441 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@7a65a360[nodeId=world0,portId=dbLine1]
2024-11-04 00:14:22,442 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@30a7653e, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-04 00:14:22,444 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@33a8c9c9[nodeId=world0,portId=dbLine2]
2024-11-04 00:14:22,445 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@382dc417, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-04 00:14:22,447 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@2415e4c7[nodeId=world0,portId=dbLine3]
2024-11-04 00:14:22,447 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@72ce812e, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-04 00:14:22,449 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@592ca48c[nodeId=world0,portId=dbLine4]
2024-11-04 00:14:22,498 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@5fed9976, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-04 00:14:22,501 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@7302ff13[nodeId=world0,portId=idAllot]
2024-11-04 00:14:22,515 (Port.java:781) [INFO][CORE] addService = org.gof.core.statistics.StatisticsService@4017fe2c, serv0, class org.gof.core.statistics.StatisticsService
2024-11-04 00:14:22,515 (DBStartup.java:112) [INFO][CORE] ================================================
2024-11-04 00:14:22,515 (DBStartup.java:113) [INFO][CORE] pwdbsrv started.
2024-11-04 00:14:22,516 (DBStartup.java:114) [INFO][CORE] Listen:tcp://************:20108
2024-11-04 00:14:22,516 (DBStartup.java:115) [INFO][CORE] ================================================
2024-11-04 00:14:22,519 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@7cfb0c4c[nodeId=world0,portId=idAllot]
2024-11-04 00:14:22,530 (Node.java:375) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@5f08fe00[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:14:22,rogerTime=2024-11-04 00:14:22]
2024-11-04 00:14:22,538 (BufferPool.java:34) [INFO][CORE] 创建新的BufferPool缓冲池，已创建总数量：count=1
2024-11-04 00:14:22,556 (BufferPool.java:34) [INFO][CORE] 创建新的BufferPool缓冲池，已创建总数量：count=3
2024-11-04 00:14:22,556 (BufferPool.java:34) [INFO][CORE] 创建新的BufferPool缓冲池，已创建总数量：count=2
2024-11-04 00:14:22,582 (Node.java:348) [WARN][CORE] [bridge1]建立主动远程Node连接：remote=org.gof.core.RemoteNode@5f08fe00[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:14:22,rogerTime=2024-11-04 00:14:22]
2024-11-04 00:14:22,583 (WorldStartup.java:157) [ERROR][TEMP] ===连接排行跨服， nid=bridge1, localBridgeId=worldBridge30572, org.gof.core.RemoteNode@5f08fe00[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:14:22,rogerTime=2024-11-04 00:14:22]
2024-11-04 00:14:22,587 (Node.java:375) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@1c046c92[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:14:22,rogerTime=2024-11-04 00:14:22]
2024-11-04 00:14:22,595 (Node.java:348) [WARN][CORE] [admin0]建立主动远程Node连接：remote=org.gof.core.RemoteNode@1c046c92[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:14:22,rogerTime=2024-11-04 00:14:22]
2024-11-04 00:14:22,596 (WorldStartup.java:165) [ERROR][TEMP] ===连接管理服， nid=admin0, localBridgeId=worldBridge30572， org.gof.core.RemoteNode@1c046c92[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:14:22,rogerTime=2024-11-04 00:14:22]
2024-11-04 00:14:22,599 (Node.java:375) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@27960b1e[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:14:22,rogerTime=2024-11-04 00:14:22]
2024-11-04 00:14:22,607 (Node.java:348) [WARN][CORE] [bridgeLeague1]建立主动远程Node连接：remote=org.gof.core.RemoteNode@27960b1e[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:14:22,rogerTime=2024-11-04 00:14:22]
2024-11-04 00:14:22,608 (WorldStartup.java:176) [ERROR][TEMP] ===连接联盟服， nid=bridgeLeague1, localBridgeId=worldBridge30572, org.gof.core.RemoteNode@27960b1e[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:14:22,rogerTime=2024-11-04 00:14:22]
2024-11-04 00:14:22,632 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@773c2214[nodeId=world0,portId=http0]
2024-11-04 00:14:22,634 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@6ee88e21[nodeId=world0,portId=http1]
2024-11-04 00:14:22,636 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@28ee0a3c[nodeId=world0,portId=http2]
2024-11-04 00:14:22,638 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@79957f11[nodeId=world0,portId=http3]
2024-11-04 00:14:22,638 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:14:22,641 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@4aebee4b[nodeId=world0,portId=http4]
2024-11-04 00:14:22,644 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@7cf63b9a[nodeId=world0,portId=login0]
2024-11-04 00:14:22,645 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@265a094b, login, class org.gof.platform.login.LoginService
2024-11-04 00:14:22,647 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@22a260ff[nodeId=world0,portId=login1]
2024-11-04 00:14:22,648 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@54c425b1, login, class org.gof.platform.login.LoginService
2024-11-04 00:14:22,650 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@16d0e521[nodeId=world0,portId=login2]
2024-11-04 00:14:22,651 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@634ca3e7, login, class org.gof.platform.login.LoginService
2024-11-04 00:14:22,653 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@1a7cb3a4[nodeId=world0,portId=login3]
2024-11-04 00:14:22,654 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@1c297897, login, class org.gof.platform.login.LoginService
2024-11-04 00:14:22,655 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@6274f21c[nodeId=world0,portId=login4]
2024-11-04 00:14:22,656 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@35cec305, login, class org.gof.platform.login.LoginService
2024-11-04 00:14:22,658 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@cbd40c1[nodeId=world0,portId=login5]
2024-11-04 00:14:22,659 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@4fa86cb8, login, class org.gof.platform.login.LoginService
2024-11-04 00:14:22,661 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@1e545821[nodeId=world0,portId=login6]
2024-11-04 00:14:22,662 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@12952aff, login, class org.gof.platform.login.LoginService
2024-11-04 00:14:22,664 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@7f977fba[nodeId=world0,portId=login7]
2024-11-04 00:14:22,665 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@446e7065, login, class org.gof.platform.login.LoginService
2024-11-04 00:14:22,667 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@7f5614f9[nodeId=world0,portId=login8]
2024-11-04 00:14:22,668 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@480cbe2e, login, class org.gof.platform.login.LoginService
2024-11-04 00:14:22,670 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@5eb0a686[nodeId=world0,portId=login9]
2024-11-04 00:14:22,671 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@73608eb0, login, class org.gof.platform.login.LoginService
2024-11-04 00:14:22,676 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@2ee39e73[nodeId=world0,portId=httpSend0]
2024-11-04 00:14:22,689 (Port.java:463) [ERROR][CORE] 处理Call返回值时未发现接受对象: call=org.gof.core.Call@6151d4f5[type=2000,fromNodeId=world0,fromPortId=dbPart2,to=org.gof.core.CallPoint@35752ece[nodeId=world0,portId=idAllot,servId=<null>,callerInfo=],callId=1,methodKey=0,methodParameters=<null>,returnValues={KEY_SINGLE_PARAM=org.gof.core.Record@57039cb3[tableName=core_id_allot,id=30572]},methodKeyName=ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GET_STRING_LONG], port=org.gof.core.dbsrv.DBPort@7cfb0c4c[nodeId=world0,portId=idAllot], node=org.gof.core.Node@389a1cda[id=world0,addr=tcp://************:20108]
2024-11-04 00:14:22,702 (Port.java:781) [INFO][CORE] addService = org.gof.core.support.idAllot.IdAllotService@5bcc479, idAllot, class org.gof.core.support.idAllot.IdAllotService
2024-11-04 00:14:23,202 (Port.java:781) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@430df350, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-04 00:14:23,205 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@69a5c6be[nodeId=world0,portId=httpSend1]
2024-11-04 00:14:23,208 (Port.java:781) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@3cb6e0ee, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-04 00:14:23,210 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@70b48eb3[nodeId=world0,portId=httpSend2]
2024-11-04 00:14:23,212 (Port.java:781) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@77083e41, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-04 00:14:23,215 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@194012a2[nodeId=world0,portId=gift0]
2024-11-04 00:14:23,217 (Port.java:781) [INFO][CORE] addService = org.gof.platform.gift.GiftService@60591e1b, gift, class org.gof.platform.gift.GiftService
2024-11-04 00:14:23,219 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@2ee4706d[nodeId=world0,portId=gift1]
2024-11-04 00:14:23,220 (Port.java:781) [INFO][CORE] addService = org.gof.platform.gift.GiftService@6650a6c, gift, class org.gof.platform.gift.GiftService
2024-11-04 00:14:23,221 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@74a820bf[nodeId=world0,portId=gift2]
2024-11-04 00:14:23,222 (Port.java:781) [INFO][CORE] addService = org.gof.platform.gift.GiftService@5a30722c, gift, class org.gof.platform.gift.GiftService
2024-11-04 00:14:23,223 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@111cba40[nodeId=world0,portId=gift3]
2024-11-04 00:14:23,224 (Port.java:781) [INFO][CORE] addService = org.gof.platform.gift.GiftService@48e41b5d, gift, class org.gof.platform.gift.GiftService
2024-11-04 00:14:23,225 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@27898e13[nodeId=world0,portId=gift4]
2024-11-04 00:14:23,226 (Port.java:781) [INFO][CORE] addService = org.gof.platform.gift.GiftService@4f5f474c, gift, class org.gof.platform.gift.GiftService
2024-11-04 00:14:23,229 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.chat.ChatPort@4a1a412e[nodeId=world0,portId=chat0]
2024-11-04 00:14:23,230 (Port.java:781) [INFO][CORE] addService = org.gof.platform.chat.ChatService@5fd43e58, chat, class org.gof.platform.chat.ChatService
2024-11-04 00:14:23,233 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.chat.ChatPort@50cdfafa[nodeId=world0,portId=chat1]
2024-11-04 00:14:23,233 (Port.java:781) [INFO][CORE] addService = org.gof.platform.chat.ChatService@2e952845, chat, class org.gof.platform.chat.ChatService
2024-11-04 00:14:23,242 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.DefaultPort@49232c6f[nodeId=world0,portId=port0]
2024-11-04 00:14:23,244 (Port.java:781) [INFO][CORE] addService = org.gof.demo.seam.SeamService@5faeeb56, seam, class org.gof.demo.seam.SeamService
2024-11-04 00:14:23,245 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.intergration.PFService@5396eeb1, worldPF, class org.gof.demo.worldsrv.intergration.PFService
2024-11-04 00:14:23,246 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.test.TestService@279126f5, test, class org.gof.demo.worldsrv.test.TestService
2024-11-04 00:14:23,274 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.common.GameService@3079c26a, gameValue, class org.gof.demo.worldsrv.common.GameService
2024-11-04 00:14:23,278 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.common.ServerListService@377dfb8d, serverList, class org.gof.demo.worldsrv.common.ServerListService
2024-11-04 00:14:23,279 (WorldStartup.java:260) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2024-11-04 00:14:23,288 (Port.java:781) [INFO][CORE] addService = org.gof.demo.seam.account.AccountService@203e705e, gate, class org.gof.demo.seam.account.AccountService
2024-11-04 00:14:23,289 (Port.java:781) [INFO][CORE] addService = org.gof.core.statistics.StatisticsService@78c74647, serv0, class org.gof.core.statistics.StatisticsService
2024-11-04 00:14:23,292 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@46bb0bdf[nodeId=world0,portId=humanPort0]
2024-11-04 00:14:23,293 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2024-11-04 00:14:23,293 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@561f9d92, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-04 00:14:23,294 (GameServiceManager.java:28) [INFO][GAME] 开始初始化service
2024-11-04 00:14:23,297 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@********[nodeId=world0,portId=game0]
2024-11-04 00:14:23,330 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=800001, end=900000, portId=humanPort0, costTime = 37
2024-11-04 00:14:23,334 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=700001, end=800000, portId=port0, costTime = 90
2024-11-04 00:14:23,337 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=900001, end=1000000, portId=game0, costTime = 40
2024-11-04 00:14:23,394 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:14:31,529 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@70700b8a[nodeId=world0,portId=game1]
2024-11-04 00:14:31,562 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.name.NameService@4f9b9841, name, class org.gof.demo.worldsrv.name.NameService
2024-11-04 00:14:31,563 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.confirm.ConfirmGlobalService@6ac7c4ca, confirm, class org.gof.demo.worldsrv.confirm.ConfirmGlobalService
2024-11-04 00:14:31,563 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.gm.GMGameService@7844fb81, gm, class org.gof.demo.worldsrv.gm.GMGameService
2024-11-04 00:14:31,564 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.common.HumanCreateApplyService@23a6f939, humanCreateApply, class org.gof.demo.worldsrv.common.HumanCreateApplyService
2024-11-04 00:14:31,577 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanGlobalService@1e4b8fe6, humanGlobal, class org.gof.demo.worldsrv.human.HumanGlobalService
2024-11-04 00:14:31,590 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=1000001, end=1100000, portId=game1, costTime = 59
2024-11-04 00:14:31,887 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@2e62e227[nodeId=world0,portId=game2]
2024-11-04 00:14:31,917 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.inform.ChatService@2fb30c8f, chat, class org.gof.demo.worldsrv.inform.ChatService
2024-11-04 00:14:31,924 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.activity.ActivityControlService@4e35924e, activity, class org.gof.demo.worldsrv.activity.ActivityControlService
2024-11-04 00:14:31,945 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=1100001, end=1200000, portId=game2, costTime = 57
2024-11-04 00:14:32,104 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.httpPush.HttpPushService@59fb0be7, httpPush, class org.gof.demo.worldsrv.httpPush.HttpPushService
2024-11-04 00:14:32,105 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.rank.RankService@41320859, rank, class org.gof.demo.worldsrv.rank.RankService
2024-11-04 00:14:32,105 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.common.DataResetService@3ce8550c, dataReset, class org.gof.demo.worldsrv.common.DataResetService
2024-11-04 00:14:32,106 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.mail.MailService@4088d01a, mail, class org.gof.demo.worldsrv.mail.MailService
2024-11-04 00:14:32,260 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@71df5f30[nodeId=world0,portId=game3]
2024-11-04 00:14:32,269 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.arena.ArenaRankedService@2ecef562, arenaRanked, class org.gof.demo.worldsrv.arena.ArenaRankedService
2024-11-04 00:14:32,270 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.pocketLine.PocketLineService@20091ecb, pocketLine, class org.gof.demo.worldsrv.pocketLine.PocketLineService
2024-11-04 00:14:32,292 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=1200001, end=1300000, portId=game3, costTime = 32
2024-11-04 00:14:32,320 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.worldBoss.WorldBossService@453e15eb, worldBoss, class org.gof.demo.worldsrv.worldBoss.WorldBossService
2024-11-04 00:14:32,681 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.mail.FillMailService@32f7096d, fillMail, class org.gof.demo.worldsrv.mail.FillMailService
2024-11-04 00:14:32,685 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@539dd2d0[nodeId=world0,portId=game4]
2024-11-04 00:14:32,686 (GuildService.java:152) [INFO][TEMP] =================init GuildService
2024-11-04 00:14:32,687 (GuildService.java:232) [INFO][TEMP] ===gve开始时间=1730693100000，2024-11-04 12:05:00
2024-11-04 00:14:32,687 (GuildService.java:237) [INFO][TEMP] ===gve结束时间=1730693700000，2024-11-04 12:15:00
2024-11-04 00:14:32,688 (GuildService.java:304) [ERROR][TEMP] initCheckRank 0
2024-11-04 00:14:32,690 (GuildService.java:2767) [ERROR][TEMP] 自动公会报名，本次报名数量：0
2024-11-04 00:14:32,733 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:14:32,737 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=1300001, end=1400000, portId=game4, costTime = 51
2024-11-04 00:14:32,776 (GuildService.java:304) [ERROR][TEMP] initCheckRank 0
2024-11-04 00:14:32,787 (GuildService.java:175) [ERROR][TEMP] 没有找到公会ID，无法加载数据
2024-11-04 00:14:32,788 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.guild.GuildService@73e2d8b0, guild, class org.gof.demo.worldsrv.guild.GuildService
2024-11-04 00:14:32,822 (FarmService.java:74) [INFO][FARM] 批量查询加载农场FarmService:loadFarm,size=0
2024-11-04 00:14:32,831 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.home.FarmService@3ca5c8e6, farm, class org.gof.demo.worldsrv.home.FarmService
2024-11-04 00:14:32,870 (CarParkService.java:95) [ERROR][GAME] groupId is 0, serverId = 30572
2024-11-04 00:14:32,870 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.carPark.CarParkService@3f3d2641, carPark, class org.gof.demo.worldsrv.carPark.CarParkService
2024-11-04 00:14:32,871 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.captureSlave.CaptureSlaveService@637a465c, captureSlave, class org.gof.demo.worldsrv.captureSlave.CaptureSlaveService
2024-11-04 00:14:32,871 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.team.TeamService@71ea9fe3, team, class org.gof.demo.worldsrv.team.TeamService
2024-11-04 00:14:33,141 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:14:33,162 (GuildLeagueWarmUpService.java:1043) [ERROR][TEMP] ===timeNow=1730650473110, interval=243926890，开始战斗时间2024-11-06 20:00:00
2024-11-04 00:14:33,163 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.guild.league.GuildLeagueWarmUpService@4409b739, guildLeagueWarmUp, class org.gof.demo.worldsrv.guild.league.GuildLeagueWarmUpService
2024-11-04 00:14:33,287 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.arena.ArenaService@622d3b47, arena, class org.gof.demo.worldsrv.arena.ArenaService
2024-11-04 00:14:33,288 (CheckWorldService.java:95) [INFO][GAME] 准备注册游戏服信息到中心服！serverId=30572，nodeId=worldBridge30572
2024-11-04 00:14:33,314 (CheckWorldService.java:100) [INFO][TEMP] ===代码未实现，检测的先不处理
2024-11-04 00:14:33,314 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.check.CheckWorldService@1d44be58, checkWorld, class org.gof.demo.worldsrv.check.CheckWorldService
2024-11-04 00:14:33,335 (HumanManager.java:4444) [INFO][GAME] 游戏启动时
2024-11-04 00:14:33,336 (WorldStartup.java:303) [ERROR][GAME] ====================
2024-11-04 00:14:33,336 (WorldStartup.java:304) [ERROR][GAME] world0 started.
2024-11-04 00:14:33,337 (WorldStartup.java:305) [ERROR][GAME] Listen:tcp://************:20108
2024-11-04 00:14:33,337 (WorldStartup.java:306) [ERROR][GAME] ====================
2024-11-04 00:14:33,391 (WorldStartup.java:359) [ERROR][GAME] 开启数据热更新扫描...
2024-11-04 00:14:33,377 (HumanManager.java:4503) [INFO][TEMP] ===去后台请求屏蔽字库
2024-11-04 00:14:33,396 (HumanManager.java:4544) [INFO][TEMP] ===去后台请求跑马灯
2024-11-04 00:14:33,403 (HumanManager.java:4510) [INFO][TEMP] ===ip=************，addr=tcp://************:13000
2024-11-04 00:14:33,405 (HumanManager.java:4526) [ERROR][TEMP] ===通知admin注册ip地址url=http://************:8018/gameServer, jo={port=8018, ip=************, serverId=30572}
2024-11-04 00:14:33,464 (ClassScanProcess.java:58) [ERROR][GAME] 开启类热更新扫描调度，每5分钟执行一次
2024-11-04 00:14:33,465 (WorldStartup.java:363) [ERROR][GAME] 开启类热更新扫描...
2024-11-04 00:14:33,468 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@7aa63f50[nodeId=world0,portId=0]
2024-11-04 00:14:33,468 (Port.java:781) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@142918a0, conn, class org.gof.core.connsrv.ConnService
2024-11-04 00:14:33,470 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@6eab92f3[nodeId=world0,portId=1]
2024-11-04 00:14:33,471 (Port.java:781) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@321b7b9e, conn, class org.gof.core.connsrv.ConnService
2024-11-04 00:14:33,479 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@55b74e6b[nodeId=world0,portId=2]
2024-11-04 00:14:33,480 (Port.java:781) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@3c1908c8, conn, class org.gof.core.connsrv.ConnService
2024-11-04 00:14:33,482 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@28db2afb[nodeId=world0,portId=3]
2024-11-04 00:14:33,482 (Port.java:781) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@5c703860, conn, class org.gof.core.connsrv.ConnService
2024-11-04 00:14:33,485 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@37fffef3[nodeId=world0,portId=4]
2024-11-04 00:14:33,486 (Port.java:781) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@6a116354, conn, class org.gof.core.connsrv.ConnService
2024-11-04 00:14:33,486 (WorldStartup.java:375) [ERROR][GAME] 开启连接服务
2024-11-04 00:14:33,487 (WorldStartup.java:377) [ERROR][GAME] 启动完成...
2024-11-04 00:14:33,494 (ServCheck.java:68) [ERROR][GAME] 
╔═══════════════════╤══════════════╗
║ service           │ costTime(ms) ║
╠═══════════════════╪══════════════╣
║ httpPush          │ 179          ║
╟───────────────────┼──────────────╢
║ arena             │ 119          ║
╟───────────────────┼──────────────╢
║ guild             │ 101          ║
╟───────────────────┼──────────────╢
║ guildLeagueWarmUp │ 47           ║
╟───────────────────┼──────────────╢
║ worldBoss         │ 47           ║
╟───────────────────┼──────────────╢
║ farm              │ 42           ║
╟───────────────────┼──────────────╢
║ carPark           │ 37           ║
╟───────────────────┼──────────────╢
║ name              │ 31           ║
╟───────────────────┼──────────────╢
║ checkWorld        │ 25           ║
╟───────────────────┼──────────────╢
║ activity          │ 6            ║
╟───────────────────┼──────────────╢
║ humanGlobal       │ 1            ║
╟───────────────────┼──────────────╢
║ arenaRanked       │ 0            ║
╟───────────────────┼──────────────╢
║ mail              │ 0            ║
╟───────────────────┼──────────────╢
║ gm                │ 0            ║
╟───────────────────┼──────────────╢
║ team              │ 0            ║
╟───────────────────┼──────────────╢
║ humanCreateApply  │ 0            ║
╟───────────────────┼──────────────╢
║ confirm           │ 0            ║
╟───────────────────┼──────────────╢
║ chat              │ 0            ║
╟───────────────────┼──────────────╢
║ pocketLine        │ 0            ║
╟───────────────────┼──────────────╢
║ rank              │ 0            ║
╟───────────────────┼──────────────╢
║ dataReset         │ 0            ║
╟───────────────────┼──────────────╢
║ fillMail          │ 0            ║
╟───────────────────┼──────────────╢
║ captureSlave      │ 0            ║
╚═══════════════════╧══════════════╝

2024-11-04 00:14:33,522 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共509项): 
占比最高0 共509项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@2df1a303]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@121744f3]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@697b8d6a]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@2a59ecfd]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@a10ab0e]
1条完整的Call数据：
org.gof.core.Call@22884663[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@172d9f85[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@7276a799},returnValues={},methodKeyName=<null>]
2024-11-04 00:14:33,535 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共508项): 
占比最高0 共508项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@1deac089]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@6640a506]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@21cd8c38]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@55d8d329]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@1e7daf93]
1条完整的Call数据：
org.gof.core.Call@7d7641cf[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@3017be15[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@234562a5},returnValues={},methodKeyName=<null>]
2024-11-04 00:14:33,554 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共507项): 
占比最高0 共507项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@2df1a303]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@1f001b99]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@126ae97a]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@1a32e1d8]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@706c5eef]
1条完整的Call数据：
org.gof.core.Call@6faf5fa8[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@2a4b4a1e[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@277a42ae},returnValues={},methodKeyName=<null>]
2024-11-04 00:14:33,575 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共506项): 
占比最高0 共506项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@20b4b081]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@505f0533]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@254552a4]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@fc0522f]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@af5dbee]
1条完整的Call数据：
org.gof.core.Call@222b311e[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@2df4878d[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@20a8e936},returnValues={},methodKeyName=<null>]
2024-11-04 00:14:33,595 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共505项): 
占比最高0 共505项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@7f7e1e3d]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@76bf26b0]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@11b98008]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@264b0b15]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@29cb7617]
1条完整的Call数据：
org.gof.core.Call@264e5f38[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@7b2a2239[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@5478dea},returnValues={},methodKeyName=<null>]
2024-11-04 00:14:33,615 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共504项): 
占比最高0 共504项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@23ad1ed5]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@93cdb73]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@30ad2f63]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@3621c31d]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@7db128ba]
1条完整的Call数据：
org.gof.core.Call@12b9933c[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@4bb6703f[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@73b77b7f},returnValues={},methodKeyName=<null>]
2024-11-04 00:14:33,635 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共503项): 
占比最高0 共503项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@59c92ae2]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@21869e6c]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@69a275a0]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@55e334ee]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@456d9a8a]
1条完整的Call数据：
org.gof.core.Call@500d19b5[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@2a786961[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@66706c84},returnValues={},methodKeyName=<null>]
2024-11-04 00:14:33,669 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共502项): 
占比最高0 共502项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@77186bf2]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@1ec23711]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@7b337c01]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@3404482e]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@f7f2a7]
1条完整的Call数据：
org.gof.core.Call@f022717[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@6f318182[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@7962e172},returnValues={},methodKeyName=<null>]
2024-11-04 00:14:33,689 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共501项): 
占比最高0 共501项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@581beb7c]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@3b455c6c]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@55c25307]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@69701942]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@6e375f1b]
1条完整的Call数据：
org.gof.core.Call@7f00c88d[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@381de601[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@514e4249},returnValues={},methodKeyName=<null>]
2024-11-04 00:14:33,709 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共500项): 
占比最高0 共500项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@7ce8f47b]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@2b923809]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@8ed6b38]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@2592d839]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@1ee2bbdc]
1条完整的Call数据：
org.gof.core.Call@240a9a79[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@47dd916b[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1158/966595390@743dcab9},returnValues={},methodKeyName=<null>]
2024-11-04 00:14:35,816 (Port.java:300) [WARN][CORE_EFFECT] [httpSend1]本次心跳操作总时间较长，达到了2405毫秒。portName=httpSend1, callCount=2, resultCount=0, countQueue=0, time={call=2405}
2024-11-04 00:14:47,246 (ClassScanProcess.java:135) [ERROR][GAME] 开始一次扫描key=216594811709003132515,jarValue=216594811709003132515
2024-11-04 00:14:47,256 (RedisToolsTest.java:25) [INFO][RANK] 获取排行榜成功, rankSn=1
2024-11-04 00:16:09,210 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:16:09,249 (Port.java:781) [INFO][CORE] addService = org.gof.core.support.idAllot.IdAllotService@49691a43, idAllot, class org.gof.core.support.idAllot.IdAllotService
2024-11-04 00:16:09,250 (Port.java:300) [WARN][CORE_EFFECT] [idAllot]本次心跳操作总时间较长，达到了106705毫秒。portName=idAllot, callCount=0, resultCount=0, countQueue=1, time={result=1, queue=106703}
2024-11-04 00:16:09,266 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:16:09,279 (WorldStartup.java:312) [INFO][GAME] 触发关闭服务器操作,开始踢人
2024-11-04 00:16:09,280 (HumanGlobalService.java:566) [ERROR][TEMP] ===踢出所有玩家
2024-11-04 00:16:09,280 (WorldStartup.java:317) [INFO][GAME] 关闭服务器-踢人请求已通知到HumanGlobalService,开始检查数据落地队列
2024-11-04 00:16:09,301 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:16:09,324 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:16:09,348 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:16:09,374 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:16:18,845 (WorldStartup.java:76) [INFO][GAME] 正在启动游戏服务器
2024-11-04 00:16:18,856 (WorldStartup.java:87) [INFO][GAME] 正在初始化事件容器
2024-11-04 00:16:19,266 (WorldStartup.java:90) [INFO][GAME] 正在初始化协议函数指针池
2024-11-04 00:16:21,741 (WorldStartup.java:98) [INFO][GAME] 加载策划数据
2024-11-04 00:16:22,166 (ConfBreakBigPrizePreview.java:283) [INFO][TEMP] 表ConfBreakBigPrizePreview有问题!
2024-11-04 00:16:26,959 (GlobalConfVal.java:1009) [INFO][TEMP] ===加载副本表数量=70
2024-11-04 00:16:28,085 (WorldStartup.java:106) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://************:20108
2024-11-04 00:16:29,161 (RedisTools.java:43) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379
2024-11-04 00:16:29,525 (RedisTools.java:51) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2024-11-04 00:16:29,546 (MysqlTool.java:97) [INFO][org.gof.core.dbsrv.redis.MysqlTool] MySQLPool setIdleTimeout:0
2024-11-04 00:16:29,584 (MysqlTool.java:40) [INFO][org.gof.core.dbsrv.redis.MysqlTool] ===createMySQLPool poolOptions={"connectionTimeout":30,"connectionTimeoutUnit":"SECONDS","eventLoopSize":0,"idleTimeout":0,"idleTimeoutUnit":"SECONDS","maxLifetime":0,"maxLifetimeUnit":"SECONDS","maxSize":40,"maxWaitQueueSize":-1,"name":"__vertx.DEFAULT","poolCleanerPeriod":1000,"shared":false}
2024-11-04 00:16:29,600 (EntityManager.java:50) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2024-11-04 00:16:35,310 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:16:35,356 (DBStartup.java:57) [INFO][CORE] ===初始化表信息sword_game_dev, ***********************************************************************************************************************, root, 123456
2024-11-04 00:16:35,410 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2024-11-04 00:16:35,416 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2024-11-04 00:16:35,440 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2024-11-04 00:16:35,441 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2024-11-04 00:16:35,442 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2024-11-04 00:16:35,442 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2024-11-04 00:16:35,452 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@70997a94[nodeId=world0,portId=dbPart0]
2024-11-04 00:16:35,469 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@6ee5f485, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-04 00:16:35,473 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@77b5148c[nodeId=world0,portId=dbPart1]
2024-11-04 00:16:35,473 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@36359723, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-04 00:16:35,476 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@49fdbe2b[nodeId=world0,portId=dbPart2]
2024-11-04 00:16:35,476 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@53eba4b8, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-04 00:16:35,479 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@6fa02932[nodeId=world0,portId=dbLine0]
2024-11-04 00:16:35,481 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@7c46c9c3, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-04 00:16:35,483 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@7a65a360[nodeId=world0,portId=dbLine1]
2024-11-04 00:16:35,484 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@30a7653e, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-04 00:16:35,486 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@33a8c9c9[nodeId=world0,portId=dbLine2]
2024-11-04 00:16:35,486 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@382dc417, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-04 00:16:35,488 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@2415e4c7[nodeId=world0,portId=dbLine3]
2024-11-04 00:16:35,489 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@72ce812e, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-04 00:16:35,556 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@592ca48c[nodeId=world0,portId=dbLine4]
2024-11-04 00:16:35,557 (Port.java:781) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@5fed9976, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-04 00:16:35,562 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@7302ff13[nodeId=world0,portId=idAllot]
2024-11-04 00:16:35,572 (Port.java:781) [INFO][CORE] addService = org.gof.core.statistics.StatisticsService@4017fe2c, serv0, class org.gof.core.statistics.StatisticsService
2024-11-04 00:16:35,572 (DBStartup.java:112) [INFO][CORE] ================================================
2024-11-04 00:16:35,573 (DBStartup.java:113) [INFO][CORE] pwdbsrv started.
2024-11-04 00:16:35,573 (DBStartup.java:114) [INFO][CORE] Listen:tcp://************:20108
2024-11-04 00:16:35,573 (DBStartup.java:115) [INFO][CORE] ================================================
2024-11-04 00:16:35,576 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@7cfb0c4c[nodeId=world0,portId=idAllot]
2024-11-04 00:16:35,589 (Node.java:375) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@5f08fe00[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:16:35,rogerTime=2024-11-04 00:16:35]
2024-11-04 00:16:35,600 (BufferPool.java:34) [INFO][CORE] 创建新的BufferPool缓冲池，已创建总数量：count=1
2024-11-04 00:16:35,600 (BufferPool.java:34) [INFO][CORE] 创建新的BufferPool缓冲池，已创建总数量：count=2
2024-11-04 00:16:35,610 (BufferPool.java:34) [INFO][CORE] 创建新的BufferPool缓冲池，已创建总数量：count=3
2024-11-04 00:16:35,633 (Node.java:348) [WARN][CORE] [bridge1]建立主动远程Node连接：remote=org.gof.core.RemoteNode@5f08fe00[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:16:35,rogerTime=2024-11-04 00:16:35]
2024-11-04 00:16:35,633 (WorldStartup.java:157) [ERROR][TEMP] ===连接排行跨服， nid=bridge1, localBridgeId=worldBridge30572, org.gof.core.RemoteNode@5f08fe00[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:16:35,rogerTime=2024-11-04 00:16:35]
2024-11-04 00:16:35,636 (Node.java:375) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@4665428b[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:16:35,rogerTime=2024-11-04 00:16:35]
2024-11-04 00:16:35,645 (Node.java:348) [WARN][CORE] [admin0]建立主动远程Node连接：remote=org.gof.core.RemoteNode@4665428b[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:16:35,rogerTime=2024-11-04 00:16:35]
2024-11-04 00:16:35,645 (WorldStartup.java:165) [ERROR][TEMP] ===连接管理服， nid=admin0, localBridgeId=worldBridge30572， org.gof.core.RemoteNode@4665428b[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:16:35,rogerTime=2024-11-04 00:16:35]
2024-11-04 00:16:35,649 (Node.java:375) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@4a70d302[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:16:35,rogerTime=2024-11-04 00:16:35]
2024-11-04 00:16:35,657 (Node.java:348) [WARN][CORE] [bridgeLeague1]建立主动远程Node连接：remote=org.gof.core.RemoteNode@4a70d302[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:16:35,rogerTime=2024-11-04 00:16:35]
2024-11-04 00:16:35,658 (WorldStartup.java:176) [ERROR][TEMP] ===连接联盟服， nid=bridgeLeague1, localBridgeId=worldBridge30572, org.gof.core.RemoteNode@4a70d302[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-04 00:16:35,rogerTime=2024-11-04 00:16:35]
2024-11-04 00:16:35,683 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@565aa4ac[nodeId=world0,portId=http0]
2024-11-04 00:16:35,685 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@1f78d415[nodeId=world0,portId=http1]
2024-11-04 00:16:35,686 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:16:35,688 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@643ba1ed[nodeId=world0,portId=http2]
2024-11-04 00:16:35,691 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@153cb763[nodeId=world0,portId=http3]
2024-11-04 00:16:35,692 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@6f731759[nodeId=world0,portId=http4]
2024-11-04 00:16:35,696 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@7a83ccd2[nodeId=world0,portId=login0]
2024-11-04 00:16:35,697 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@599a9cb2, login, class org.gof.platform.login.LoginService
2024-11-04 00:16:35,699 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@4b55ff0a[nodeId=world0,portId=login1]
2024-11-04 00:16:35,699 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@46a795de, login, class org.gof.platform.login.LoginService
2024-11-04 00:16:35,701 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@4bf03fee[nodeId=world0,portId=login2]
2024-11-04 00:16:35,702 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@31834a2b, login, class org.gof.platform.login.LoginService
2024-11-04 00:16:35,704 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@68af87ad[nodeId=world0,portId=login3]
2024-11-04 00:16:35,704 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@43d65a81, login, class org.gof.platform.login.LoginService
2024-11-04 00:16:35,707 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@601eb4af[nodeId=world0,portId=login4]
2024-11-04 00:16:35,707 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@11ede87f, login, class org.gof.platform.login.LoginService
2024-11-04 00:16:35,709 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@51cab489[nodeId=world0,portId=login5]
2024-11-04 00:16:35,710 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@2f9a10df, login, class org.gof.platform.login.LoginService
2024-11-04 00:16:35,711 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@110b7837[nodeId=world0,portId=login6]
2024-11-04 00:16:35,712 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@6ee88e21, login, class org.gof.platform.login.LoginService
2024-11-04 00:16:35,713 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@28ee0a3c[nodeId=world0,portId=login7]
2024-11-04 00:16:35,714 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@2dd1086, login, class org.gof.platform.login.LoginService
2024-11-04 00:16:35,715 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@217235f5[nodeId=world0,portId=login8]
2024-11-04 00:16:35,716 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@4b41587d, login, class org.gof.platform.login.LoginService
2024-11-04 00:16:35,718 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@28393e82[nodeId=world0,portId=login9]
2024-11-04 00:16:35,719 (Port.java:781) [INFO][CORE] addService = org.gof.platform.login.LoginService@697a0948, login, class org.gof.platform.login.LoginService
2024-11-04 00:16:35,722 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@1f536481[nodeId=world0,portId=httpSend0]
2024-11-04 00:16:35,734 (Port.java:463) [ERROR][CORE] 处理Call返回值时未发现接受对象: call=org.gof.core.Call@6d20d425[type=2000,fromNodeId=world0,fromPortId=dbPart2,to=org.gof.core.CallPoint@63e737f1[nodeId=world0,portId=idAllot,servId=<null>,callerInfo=],callId=1,methodKey=0,methodParameters=<null>,returnValues={KEY_SINGLE_PARAM=org.gof.core.Record@22da3dca[tableName=core_id_allot,id=30572]},methodKeyName=ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GET_STRING_LONG], port=org.gof.core.dbsrv.DBPort@7cfb0c4c[nodeId=world0,portId=idAllot], node=org.gof.core.Node@49b04448[id=world0,addr=tcp://************:20108]
2024-11-04 00:16:35,748 (Port.java:781) [INFO][CORE] addService = org.gof.core.support.idAllot.IdAllotService@31329d04, idAllot, class org.gof.core.support.idAllot.IdAllotService
2024-11-04 00:16:36,211 (Port.java:781) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@34edd065, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-04 00:16:36,213 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@1eeb5818[nodeId=world0,portId=httpSend1]
2024-11-04 00:16:36,216 (Port.java:781) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@72a8361b, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-04 00:16:36,218 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@75793f17[nodeId=world0,portId=httpSend2]
2024-11-04 00:16:36,221 (Port.java:781) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@5ea7bc4, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-04 00:16:36,224 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@7a64cb0c[nodeId=world0,portId=gift0]
2024-11-04 00:16:36,225 (Port.java:781) [INFO][CORE] addService = org.gof.platform.gift.GiftService@6c4ce583, gift, class org.gof.platform.gift.GiftService
2024-11-04 00:16:36,228 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@272778ae[nodeId=world0,portId=gift1]
2024-11-04 00:16:36,228 (Port.java:781) [INFO][CORE] addService = org.gof.platform.gift.GiftService@2cccf134, gift, class org.gof.platform.gift.GiftService
2024-11-04 00:16:36,230 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@6842c101[nodeId=world0,portId=gift2]
2024-11-04 00:16:36,230 (Port.java:781) [INFO][CORE] addService = org.gof.platform.gift.GiftService@234cd86c, gift, class org.gof.platform.gift.GiftService
2024-11-04 00:16:36,233 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@47fcefb3[nodeId=world0,portId=gift3]
2024-11-04 00:16:36,234 (Port.java:781) [INFO][CORE] addService = org.gof.platform.gift.GiftService@236c098, gift, class org.gof.platform.gift.GiftService
2024-11-04 00:16:36,235 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@32ab408e[nodeId=world0,portId=gift4]
2024-11-04 00:16:36,236 (Port.java:781) [INFO][CORE] addService = org.gof.platform.gift.GiftService@6ad6443, gift, class org.gof.platform.gift.GiftService
2024-11-04 00:16:36,238 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.chat.ChatPort@3a788fe0[nodeId=world0,portId=chat0]
2024-11-04 00:16:36,239 (Port.java:781) [INFO][CORE] addService = org.gof.platform.chat.ChatService@1373e3ee, chat, class org.gof.platform.chat.ChatService
2024-11-04 00:16:36,240 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.chat.ChatPort@451e2fa9[nodeId=world0,portId=chat1]
2024-11-04 00:16:36,241 (Port.java:781) [INFO][CORE] addService = org.gof.platform.chat.ChatService@497ed877, chat, class org.gof.platform.chat.ChatService
2024-11-04 00:16:36,251 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.DefaultPort@60591e1b[nodeId=world0,portId=port0]
2024-11-04 00:16:36,252 (Port.java:781) [INFO][CORE] addService = org.gof.demo.seam.SeamService@29c53c4f, seam, class org.gof.demo.seam.SeamService
2024-11-04 00:16:36,253 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.intergration.PFService@3009eed7, worldPF, class org.gof.demo.worldsrv.intergration.PFService
2024-11-04 00:16:36,256 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.test.TestService@2ee4706d, test, class org.gof.demo.worldsrv.test.TestService
2024-11-04 00:16:36,277 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.common.GameService@15d236fd, gameValue, class org.gof.demo.worldsrv.common.GameService
2024-11-04 00:16:36,283 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.common.ServerListService@5ca4dce5, serverList, class org.gof.demo.worldsrv.common.ServerListService
2024-11-04 00:16:36,283 (WorldStartup.java:260) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2024-11-04 00:16:36,291 (Port.java:781) [INFO][CORE] addService = org.gof.demo.seam.account.AccountService@40bd0f8, gate, class org.gof.demo.seam.account.AccountService
2024-11-04 00:16:36,292 (Port.java:781) [INFO][CORE] addService = org.gof.core.statistics.StatisticsService@7eb27768, serv0, class org.gof.core.statistics.StatisticsService
2024-11-04 00:16:36,294 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@325162e9[nodeId=world0,portId=humanPort0]
2024-11-04 00:16:36,296 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@1ee40b5c, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-04 00:16:36,296 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2024-11-04 00:16:36,297 (GameServiceManager.java:28) [INFO][GAME] 开始初始化service
2024-11-04 00:16:36,300 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@7741ae1b[nodeId=world0,portId=game0]
2024-11-04 00:16:36,326 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=1500001, end=1600000, portId=humanPort0, costTime = 31
2024-11-04 00:16:36,327 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=1400001, end=1500000, portId=port0, costTime = 76
2024-11-04 00:16:36,337 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=1600001, end=1700000, portId=game0, costTime = 37
2024-11-04 00:16:36,378 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:16:44,611 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@3ef089ce[nodeId=world0,portId=game1]
2024-11-04 00:16:44,627 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.gm.GMGameService@70de7fb6, gm, class org.gof.demo.worldsrv.gm.GMGameService
2024-11-04 00:16:44,644 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanGlobalService@40e32095, humanGlobal, class org.gof.demo.worldsrv.human.HumanGlobalService
2024-11-04 00:16:44,644 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.confirm.ConfirmGlobalService@41f942b9, confirm, class org.gof.demo.worldsrv.confirm.ConfirmGlobalService
2024-11-04 00:16:44,656 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=1700001, end=1800000, portId=game1, costTime = 45
2024-11-04 00:16:44,677 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.name.NameService@61ecc304, name, class org.gof.demo.worldsrv.name.NameService
2024-11-04 00:16:44,677 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.common.HumanCreateApplyService@13a61bfc, humanCreateApply, class org.gof.demo.worldsrv.common.HumanCreateApplyService
2024-11-04 00:16:45,029 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@be083e1[nodeId=world0,portId=game2]
2024-11-04 00:16:45,068 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=1800001, end=1900000, portId=game2, costTime = 39
2024-11-04 00:16:45,072 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.httpPush.HttpPushService@109af030, httpPush, class org.gof.demo.worldsrv.httpPush.HttpPushService
2024-11-04 00:16:45,078 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.inform.ChatService@445e6773, chat, class org.gof.demo.worldsrv.inform.ChatService
2024-11-04 00:16:45,079 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.mail.MailService@38214b8, mail, class org.gof.demo.worldsrv.mail.MailService
2024-11-04 00:16:45,095 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.activity.ActivityControlService@7598572e, activity, class org.gof.demo.worldsrv.activity.ActivityControlService
2024-11-04 00:16:45,095 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.common.DataResetService@e052f04, dataReset, class org.gof.demo.worldsrv.common.DataResetService
2024-11-04 00:16:45,096 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.rank.RankService@6dcb149f, rank, class org.gof.demo.worldsrv.rank.RankService
2024-11-04 00:16:45,433 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@5ca86715[nodeId=world0,portId=game3]
2024-11-04 00:16:45,465 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=1900001, end=2000000, portId=game3, costTime = 31
2024-11-04 00:16:45,510 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:16:45,576 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.worldBoss.WorldBossService@2a6aca5b, worldBoss, class org.gof.demo.worldsrv.worldBoss.WorldBossService
2024-11-04 00:16:45,580 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.arena.ArenaRankedService@28886281, arenaRanked, class org.gof.demo.worldsrv.arena.ArenaRankedService
2024-11-04 00:16:45,581 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.pocketLine.PocketLineService@43f87c26, pocketLine, class org.gof.demo.worldsrv.pocketLine.PocketLineService
2024-11-04 00:16:45,905 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@408f70ab[nodeId=world0,portId=game4]
2024-11-04 00:16:45,938 (FarmService.java:74) [INFO][FARM] 批量查询加载农场FarmService:loadFarm,size=0
2024-11-04 00:16:45,943 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=2000001, end=2100000, portId=game4, costTime = 37
2024-11-04 00:16:45,949 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.home.FarmService@15017d8c, farm, class org.gof.demo.worldsrv.home.FarmService
2024-11-04 00:16:45,950 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.team.TeamService@6d64c62c, team, class org.gof.demo.worldsrv.team.TeamService
2024-11-04 00:16:45,953 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.mail.FillMailService@50c9e296, fillMail, class org.gof.demo.worldsrv.mail.FillMailService
2024-11-04 00:16:45,953 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.captureSlave.CaptureSlaveService@1bd71b05, captureSlave, class org.gof.demo.worldsrv.captureSlave.CaptureSlaveService
2024-11-04 00:16:45,959 (GuildService.java:152) [INFO][TEMP] =================init GuildService
2024-11-04 00:16:45,960 (GuildService.java:232) [INFO][TEMP] ===gve开始时间=1730693100000，2024-11-04 12:05:00
2024-11-04 00:16:45,960 (GuildService.java:237) [INFO][TEMP] ===gve结束时间=1730693700000，2024-11-04 12:15:00
2024-11-04 00:16:45,962 (GuildService.java:304) [ERROR][TEMP] initCheckRank 0
2024-11-04 00:16:45,964 (GuildService.java:2767) [ERROR][TEMP] 自动公会报名，本次报名数量：0
2024-11-04 00:16:46,047 (GuildService.java:175) [ERROR][TEMP] 没有找到公会ID，无法加载数据
2024-11-04 00:16:46,047 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.guild.GuildService@4943824e, guild, class org.gof.demo.worldsrv.guild.GuildService
2024-11-04 00:16:46,053 (Port.java:463) [ERROR][CORE] 处理Call返回值时未发现接受对象: call=org.gof.core.Call@23d76f21[type=2000,fromNodeId=world0,fromPortId=dbPart1,to=org.gof.core.CallPoint@2559871e[nodeId=world0,portId=game3,servId=<null>,callerInfo=],callId=4,methodKey=0,methodParameters=<null>,returnValues={KEY_SINGLE_PARAM=[]},methodKeyName=ORG_GOF_CORE_DBSRV_DBPARTSERVICE_FINDBY_BOOLEAN_STRING_OBJECTS], port=org.gof.demo.worldsrv.common.GamePort@5ca86715[nodeId=world0,portId=game3], node=org.gof.core.Node@49b04448[id=world0,addr=tcp://************:20108]
2024-11-04 00:16:46,087 (CarParkService.java:95) [ERROR][GAME] groupId is 0, serverId = 30572
2024-11-04 00:16:46,088 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.carPark.CarParkService@30692ad6, carPark, class org.gof.demo.worldsrv.carPark.CarParkService
2024-11-04 00:16:46,398 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:16:47,197 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.arena.ArenaService@4073fd5b, arena, class org.gof.demo.worldsrv.arena.ArenaService
2024-11-04 00:16:47,198 (CheckWorldService.java:95) [INFO][GAME] 准备注册游戏服信息到中心服！serverId=30572，nodeId=worldBridge30572
2024-11-04 00:16:47,200 (CheckWorldService.java:100) [INFO][TEMP] ===代码未实现，检测的先不处理
2024-11-04 00:16:47,200 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.check.CheckWorldService@14d38e45, checkWorld, class org.gof.demo.worldsrv.check.CheckWorldService
2024-11-04 00:16:47,303 (GuildLeagueWarmUpService.java:1043) [ERROR][TEMP] ===timeNow=1730650606373, interval=243793627，开始战斗时间2024-11-06 20:00:00
2024-11-04 00:16:47,303 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.guild.league.GuildLeagueWarmUpService@168bad40, guildLeagueWarmUp, class org.gof.demo.worldsrv.guild.league.GuildLeagueWarmUpService
2024-11-04 00:16:47,304 (Port.java:300) [WARN][CORE_EFFECT] [game4]本次心跳操作总时间较长，达到了931毫秒。portName=game4, callCount=0, resultCount=0, countQueue=3, time={queue=930}
2024-11-04 00:16:47,309 (HumanManager.java:4444) [INFO][GAME] 游戏启动时
2024-11-04 00:16:47,311 (WorldStartup.java:303) [ERROR][GAME] ====================
2024-11-04 00:16:47,312 (WorldStartup.java:304) [ERROR][GAME] world0 started.
2024-11-04 00:16:47,312 (WorldStartup.java:305) [ERROR][GAME] Listen:tcp://************:20108
2024-11-04 00:16:47,312 (WorldStartup.java:306) [ERROR][GAME] ====================
2024-11-04 00:16:47,337 (HumanManager.java:4503) [INFO][TEMP] ===去后台请求屏蔽字库
2024-11-04 00:16:47,337 (HumanManager.java:4544) [INFO][TEMP] ===去后台请求跑马灯
2024-11-04 00:16:47,338 (HumanManager.java:4510) [INFO][TEMP] ===ip=************，addr=tcp://************:13000
2024-11-04 00:16:47,338 (HumanManager.java:4526) [ERROR][TEMP] ===通知admin注册ip地址url=http://************:8018/gameServer, jo={port=8018, ip=************, serverId=30572}
2024-11-04 00:16:47,356 (WorldStartup.java:359) [ERROR][GAME] 开启数据热更新扫描...
2024-11-04 00:16:47,383 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共507项): 
占比最高0 共507项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@30c7832e]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@4c2a6123]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@207ba99e]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@2304e17e]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@2f56c8c3]
1条完整的Call数据：
org.gof.core.Call@306a8773[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@4da0ffb9[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@49abe862},returnValues={},methodKeyName=<null>]
2024-11-04 00:16:47,399 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共506项): 
占比最高0 共506项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@3417653c]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@7e001930]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@68fe166a]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@2917a230]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@26260cab]
1条完整的Call数据：
org.gof.core.Call@581148c6[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@404448e8[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@545664f2},returnValues={},methodKeyName=<null>]
2024-11-04 00:16:47,417 (ClassScanProcess.java:58) [ERROR][GAME] 开启类热更新扫描调度，每5分钟执行一次
2024-11-04 00:16:47,418 (WorldStartup.java:363) [ERROR][GAME] 开启类热更新扫描...
2024-11-04 00:16:47,418 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共505项): 
占比最高0 共505项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@10064e69]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@72674789]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@2917a230]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@15f1ccc3]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@4c67d0bb]
1条完整的Call数据：
org.gof.core.Call@6e30aa58[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@2623f318[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@3dafb4cb},returnValues={},methodKeyName=<null>]
2024-11-04 00:16:47,421 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@1c2b65cc[nodeId=world0,portId=0]
2024-11-04 00:16:47,422 (Port.java:781) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@390a7532, conn, class org.gof.core.connsrv.ConnService
2024-11-04 00:16:47,425 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@3a0baec0[nodeId=world0,portId=1]
2024-11-04 00:16:47,425 (Port.java:781) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@50c2ef56, conn, class org.gof.core.connsrv.ConnService
2024-11-04 00:16:47,428 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@520ee6b3[nodeId=world0,portId=2]
2024-11-04 00:16:47,428 (Port.java:781) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@15f11bfb, conn, class org.gof.core.connsrv.ConnService
2024-11-04 00:16:47,430 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@27210a3b[nodeId=world0,portId=3]
2024-11-04 00:16:47,430 (Port.java:781) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@3a12f3e7, conn, class org.gof.core.connsrv.ConnService
2024-11-04 00:16:47,432 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@5fafa76d[nodeId=world0,portId=4]
2024-11-04 00:16:47,432 (Port.java:781) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@720ffab4, conn, class org.gof.core.connsrv.ConnService
2024-11-04 00:16:47,433 (WorldStartup.java:375) [ERROR][GAME] 开启连接服务
2024-11-04 00:16:47,433 (WorldStartup.java:377) [ERROR][GAME] 启动完成...
2024-11-04 00:16:47,438 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共504项): 
占比最高0 共504项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@24b65818]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@1d34c4a5]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@5d7d3224]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@6839e977]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@7e590af0]
1条完整的Call数据：
org.gof.core.Call@4573f872[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@71ab0eea[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@567e23f9},returnValues={},methodKeyName=<null>]
2024-11-04 00:16:47,445 (ServCheck.java:68) [ERROR][GAME] 
╔═══════════════════╤══════════════╗
║ service           │ costTime(ms) ║
╠═══════════════════╪══════════════╣
║ arena             │ 820          ║
╟───────────────────┼──────────────╢
║ worldBoss         │ 134          ║
╟───────────────────┼──────────────╢
║ guildLeagueWarmUp │ 94           ║
╟───────────────────┼──────────────╢
║ guild             │ 88           ║
╟───────────────────┼──────────────╢
║ carPark           │ 39           ║
╟───────────────────┼──────────────╢
║ farm              │ 34           ║
╟───────────────────┼──────────────╢
║ name              │ 30           ║
╟───────────────────┼──────────────╢
║ httpPush          │ 29           ║
╟───────────────────┼──────────────╢
║ activity          │ 15           ║
╟───────────────────┼──────────────╢
║ checkWorld        │ 2            ║
╟───────────────────┼──────────────╢
║ humanGlobal       │ 1            ║
╟───────────────────┼──────────────╢
║ arenaRanked       │ 0            ║
╟───────────────────┼──────────────╢
║ mail              │ 0            ║
╟───────────────────┼──────────────╢
║ gm                │ 0            ║
╟───────────────────┼──────────────╢
║ team              │ 0            ║
╟───────────────────┼──────────────╢
║ humanCreateApply  │ 0            ║
╟───────────────────┼──────────────╢
║ confirm           │ 0            ║
╟───────────────────┼──────────────╢
║ chat              │ 0            ║
╟───────────────────┼──────────────╢
║ pocketLine        │ 0            ║
╟───────────────────┼──────────────╢
║ rank              │ 0            ║
╟───────────────────┼──────────────╢
║ dataReset         │ 0            ║
╟───────────────────┼──────────────╢
║ fillMail          │ 0            ║
╟───────────────────┼──────────────╢
║ captureSlave      │ 0            ║
╚═══════════════════╧══════════════╝

2024-11-04 00:16:47,459 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共503项): 
占比最高0 共503项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@3aac694e]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@1359fcaf]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@341f64d6]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@616683c4]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@385960bf]
1条完整的Call数据：
org.gof.core.Call@64b95f9f[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@2bcfbe0a[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@7e626014},returnValues={},methodKeyName=<null>]
2024-11-04 00:16:47,481 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共502项): 
占比最高0 共502项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@2326587e]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@754f9dfe]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@18c02183]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@78b27327]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@69c631e7]
1条完整的Call数据：
org.gof.core.Call@60efe14c[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@793a18eb[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@12fa77a0},returnValues={},methodKeyName=<null>]
2024-11-04 00:16:47,500 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共501项): 
占比最高0 共501项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@55914891]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@7cc5ff88]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@3dc3d0c7]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@2e9329c3]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@71992940]
1条完整的Call数据：
org.gof.core.Call@2273a144[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@1951825[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@367ff4e5},returnValues={},methodKeyName=<null>]
2024-11-04 00:16:47,520 (Port.java:347) [WARN][CORE_EFFECT] [game4]本帧处理的Call请求过多(共500项): 
占比最高0 共500项
随机5条调用参数：
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@3152e144]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@78b27327]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@18af937b]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@51be9457]
[org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@7fc6c833]
1条完整的Call数据：
org.gof.core.Call@59b84e4e[type=6000,fromNodeId=<null>,fromPortId=<null>,to=org.gof.core.CallPoint@3de82a49[nodeId=<null>,portId=<null>,servId=<null>,callerInfo=<null>],callId=0,methodKey=0,methodParameters={org.gof.core.dbsrv.redis.AsyncActionResult$$Lambda$1159/1675802912@18d10d8f},returnValues={},methodKeyName=<null>]
2024-11-04 00:16:49,738 (Port.java:300) [WARN][CORE_EFFECT] [httpSend0]本次心跳操作总时间较长，达到了2391毫秒。portName=httpSend0, callCount=3, resultCount=0, countQueue=0, time={call=2390}
2024-11-04 00:16:53,188 (RedisToolsTest.java:25) [INFO][RANK] 获取排行榜成功, rankSn=1
2024-11-04 00:22:16,289 (HumanGlobalService.java:169) [ERROR][TEMP] Human Global Count ======0========
2024-11-04 00:22:16,289 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=2100, sn=21000001, round= 21000001
2024-11-04 00:22:16,288 (ClassScanProcess.java:135) [ERROR][GAME] 开始一次扫描key=216594811709003132515,jarValue=216594811709003132515
2024-11-04 00:22:16,294 (ClassScanProcess.java:135) [ERROR][GAME] 开始一次扫描key=216594811709003132515,jarValue=216594811709003132515
2024-11-04 00:22:23,360 (Node.java:658) [ERROR][CORE] ====humanPort 超过5分钟无反馈，出现异常 删除重启 humanPort0===
2024-11-04 00:23:57,625 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@6306f2dc[nodeId=world0,portId=humanPort0]
2024-11-04 00:23:57,626 (Port.java:781) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@2cd88323, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-04 00:23:57,626 (Node.java:667) [INFO][CORE] ====humanPort 重启 humanPort0===
2024-11-04 00:23:57,626 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,622 (:) [WARN][io.vertx.core.impl.BlockedThreadChecker] Thread Thread[vert.x-eventloop-thread-14,5,main] has been blocked for 7037 ms, time limit is 2000 ms
io.vertx.core.VertxException: Thread blocked
	at org.gof.core.dbsrv.redis.RedisTools.toJsonObject(RedisTools.java:168) ~[bin/:?]
	at org.gof.core.dbsrv.redis.RedisTools.lambda$getHashJsonObject$16(RedisTools.java:427) ~[bin/:?]
	at org.gof.core.dbsrv.redis.RedisTools$$Lambda$1189/1949651946.handle(Unknown Source) ~[?:?]
	at io.vertx.core.impl.future.FutureImpl$4.onSuccess(FutureImpl.java:176) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureImpl.tryComplete(FutureImpl.java:259) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.Transformation$1.onSuccess(Transformation.java:61) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.emitSuccess(FutureBase.java:66) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.SucceededFuture.addListener(SucceededFuture.java:88) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.Transformation.onSuccess(Transformation.java:42) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase.lambda$emitSuccess$0(FutureBase.java:60) ~[vertx-core-4.5.10.jar:4.5.10]
	at io.vertx.core.impl.future.FutureBase$$Lambda$1108/1521172601.run(Unknown Source) ~[?:?]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask$$$capture(AbstractEventExecutor.java:174) ~[netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java) ~[netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:167) ~[netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.89.Final.jar:4.1.89.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.89.Final.jar:4.1.89.Final]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_211]
2024-11-04 00:23:57,656 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,660 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=2015, sn=21020004, round= 21020004
2024-11-04 00:23:57,668 (WorldStartup.java:312) [INFO][GAME] 触发关闭服务器操作,开始踢人
2024-11-04 00:23:57,674 (HumanGlobalService.java:566) [ERROR][TEMP] ===踢出所有玩家
2024-11-04 00:23:57,674 (WorldStartup.java:317) [INFO][GAME] 关闭服务器-踢人请求已通知到HumanGlobalService,开始检查数据落地队列
2024-11-04 00:23:57,690 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,699 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=3000, sn=30000001, round= 30000001
2024-11-04 00:23:57,699 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:23:57,716 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=2100001, end=2200000, portId=humanPort0, costTime = 67
2024-11-04 00:23:57,721 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:23:57,729 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,749 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=85, sn=850001, round= 850001
2024-11-04 00:23:57,752 (Port.java:463) [ERROR][CORE] 处理Call返回值时未发现接受对象: call=org.gof.core.Call@7f67be93[type=2000,fromNodeId=world0,fromPortId=dbPart2,to=org.gof.core.CallPoint@5420713c[nodeId=world0,portId=idAllot,servId=<null>,callerInfo=],callId=2,methodKey=0,methodParameters=<null>,returnValues={KEY_SINGLE_PARAM=org.gof.core.Record@613aa626[tableName=core_id_allot,id=30572]},methodKeyName=ORG_GOF_CORE_DBSRV_DBPARTSERVICE_GET_STRING_LONG], port=org.gof.core.dbsrv.DBPort@7cfb0c4c[nodeId=world0,portId=idAllot], node=org.gof.core.Node@49b04448[id=world0,addr=tcp://************:20108]
2024-11-04 00:23:57,760 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,785 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,797 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=166, sn=1660001, round= 1660001
2024-11-04 00:23:57,808 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,835 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,836 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:23:57,850 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=87, sn=870001, round= 870001
2024-11-04 00:23:57,866 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,890 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,898 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=168, sn=1680001, round= 1680001
2024-11-04 00:23:57,901 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:23:57,917 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,943 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:57,945 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=89, sn=890001, round= 890001
2024-11-04 00:23:57,979 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,006 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=170, sn=1700001, round= 1700001
2024-11-04 00:23:58,014 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,042 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,049 (ActivityServerData.java:510) [ERROR][TEMP] ===循环活动开启， activitySn=120001, round=1, openTime=1730217600000, closeTime=1730822399000
2024-11-04 00:23:58,067 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,092 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,099 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=2101, sn=21010001, round= 21010001
2024-11-04 00:23:58,119 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,140 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,149 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=1, sn=10002, round= 10002
2024-11-04 00:23:58,167 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,173 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-04 00:23:58,189 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=165, sn=1650001, round= 1650001
2024-11-04 00:23:58,192 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,226 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,243 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=84, sn=840001, round= 840001
2024-11-04 00:23:58,253 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,283 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,300 (ActivityServerData.java:510) [ERROR][TEMP] ===循环活动开启， activitySn=50001, round=1, openTime=1730476800000, closeTime=1730822399000
2024-11-04 00:23:58,312 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,339 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,354 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=167, sn=1670001, round= 1670001
2024-11-04 00:23:58,362 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,388 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,402 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=86, sn=860001, round= 860001
2024-11-04 00:23:58,413 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-04 00:23:58,450 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=169, sn=1690001, round= 1690001
2024-11-04 00:23:58,497 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=88, sn=880001, round= 880001
2024-11-04 00:23:58,546 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=171, sn=1710001, round= 1710001
2024-11-04 00:23:58,593 (ActivityServerData.java:322) [INFO][TEMP] ===非循环活动。type=90, sn=900001, round= 900001
2024-11-04 00:23:58,657 (ActivityServerData.java:304) [ERROR][TEMP] ===开启活动：serverId=30572:[21000001, 21020004, 30000001, 850001, 1660001, 870001, 1680001, 890001, 1700001, 120001, 21010001, 10002, 1650001, 840001, 50001, 1670001, 860001, 1690001, 880001, 1710001, 900001], openSnList=[120001, 21000001, 21020004, 30000001, 1650001, 1670001, 1690001, 1710001, 860001, 880001, 900001, 840001, 21010001, 50001, 10002, 1660001, 1680001, 1700001, 870001, 890001, 850001]
2024-11-04 00:23:58,658 (Port.java:300) [WARN][CORE_EFFECT] [game1]本次心跳操作总时间较长，达到了102370毫秒。portName=game1, callCount=0, resultCount=0, countQueue=0, time={serivce=102369}
2024-11-04 00:23:59,675 (DBPartService.java:384) [INFO][CORE_DB] [dbPart2]远程调用刷新数据库全部写缓存。
2024-11-04 00:23:59,675 (DBPartService.java:384) [INFO][CORE_DB] [dbPart1]远程调用刷新数据库全部写缓存。
2024-11-04 00:23:59,675 (DBPartService.java:384) [INFO][CORE_DB] [dbPart0]远程调用刷新数据库全部写缓存。
2024-11-04 00:23:59,677 (WorldStartup.java:331) [INFO][GAME] 关闭服务器-检查db更新队列, 当前队列剩余数量：0 
2024-11-04 00:24:01,677 (WorldStartup.java:331) [INFO][GAME] 关闭服务器-检查db更新队列, 当前队列剩余数量：0 
2024-11-04 00:24:03,689 (WorldStartup.java:331) [INFO][GAME] 关闭服务器-检查db更新队列, 当前队列剩余数量：0 
2024-11-04 00:24:05,699 (WorldStartup.java:331) [INFO][GAME] 关闭服务器-检查db更新队列, 当前队列剩余数量：0 
2024-11-04 00:24:07,714 (WorldStartup.java:331) [INFO][GAME] 关闭服务器-检查db更新队列, 当前队列剩余数量：0 
2024-11-04 00:24:07,715 (WorldStartup.java:345) [INFO][GAME] 关闭服务器-检查db更新队列完成! 15s后服务器将关闭
2024-11-04 00:24:13,123 (Node.java:673) [INFO][CORE] ====prot status : active = 1 , dead= 0 , dead list:[]
2024-11-10 17:11:29,997 (WorldStartup.java:71) [INFO][GAME] 正在启动游戏服务器
2024-11-10 17:11:30,051 (WorldStartup.java:85) [INFO][GAME] 正在初始化事件容器
2024-11-10 17:11:30,394 (WorldStartup.java:88) [INFO][GAME] 正在初始化协议函数指针池
2024-11-10 17:11:32,793 (WorldStartup.java:96) [INFO][GAME] 加载策划数据
2024-11-10 17:11:33,124 (ConfBreakBigPrizePreview.java:283) [INFO][TEMP] 表ConfBreakBigPrizePreview有问题!
2024-11-10 20:31:52,160 (WorldStartup.java:71) [INFO][GAME] 正在启动游戏服务器
2024-11-10 20:31:52,207 (WorldStartup.java:85) [INFO][GAME] 正在初始化事件容器
2024-11-10 20:31:52,540 (WorldStartup.java:88) [INFO][GAME] 正在初始化协议函数指针池
2024-11-10 20:31:55,110 (WorldStartup.java:96) [INFO][GAME] 加载策划数据
2024-11-10 20:31:55,455 (ConfBreakBigPrizePreview.java:283) [INFO][TEMP] 表ConfBreakBigPrizePreview有问题!
2024-11-10 20:32:00,182 (GlobalConfVal.java:1032) [INFO][TEMP] ===加载副本表数量=70
2024-11-10 20:32:02,265 (RedisTools.java:46) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379
2024-11-10 20:32:02,644 (RedisTools.java:54) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2024-11-10 20:32:02,664 (MysqlTool.java:97) [INFO][org.gof.core.dbsrv.redis.MysqlTool] MySQLPool setIdleTimeout:0
2024-11-10 20:32:02,704 (MysqlTool.java:40) [INFO][org.gof.core.dbsrv.redis.MysqlTool] ===createMySQLPool poolOptions={"connectionTimeout":30,"connectionTimeoutUnit":"SECONDS","eventLoopSize":0,"idleTimeout":0,"idleTimeoutUnit":"SECONDS","maxLifetime":0,"maxLifetimeUnit":"SECONDS","maxSize":40,"maxWaitQueueSize":-1,"name":"__vertx.DEFAULT","poolCleanerPeriod":1000,"shared":false}
2024-11-10 20:32:02,721 (EntityManager.java:50) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2024-11-10 20:32:02,728 (WorldStartup.java:132) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://************:20108
2024-11-10 20:32:02,751 (WorldStartup.java:135) [INFO][GAME] server init : begin start node...
2024-11-10 20:32:03,087 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:32:03,090 (FieldTable.java:58) [INFO][GAME] server init : db executeQuery...
2024-11-10 20:32:03,140 (FieldTable.java:69) [INFO][GAME] server init : initTable...
2024-11-10 20:32:03,149 (DBStartup.java:57) [INFO][CORE] ===初始化表信息sword_game_dev, ***********************************************************************************************************************, root, 123456
2024-11-10 20:32:03,205 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2024-11-10 20:32:03,210 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2024-11-10 20:32:03,230 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2024-11-10 20:32:03,231 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2024-11-10 20:32:03,232 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2024-11-10 20:32:03,233 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2024-11-10 20:32:03,242 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@241fbec[nodeId=world0,portId=dbPart0]
2024-11-10 20:32:03,252 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@1213ffbc, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:32:03,254 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@1310fcb0[nodeId=world0,portId=dbPart1]
2024-11-10 20:32:03,254 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@1ef31f71, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:32:03,256 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@2199e845[nodeId=world0,portId=dbPart2]
2024-11-10 20:32:03,256 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@27976390, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:32:03,257 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@2fe2965c[nodeId=world0,portId=dbPart3]
2024-11-10 20:32:03,257 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@3375ebd3, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:32:03,259 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@42679fc2[nodeId=world0,portId=dbPart4]
2024-11-10 20:32:03,259 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@5e50df2e, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:32:03,260 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@724bf25f[nodeId=world0,portId=dbPart5]
2024-11-10 20:32:03,260 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@2abafa97, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:32:03,261 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@29f3c438[nodeId=world0,portId=dbPart6]
2024-11-10 20:32:03,262 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@5460edd3, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:32:03,263 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@565aa4ac[nodeId=world0,portId=dbPart7]
2024-11-10 20:32:03,263 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@587c5c1, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:32:03,264 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@1c046c92[nodeId=world0,portId=dbLine0]
2024-11-10 20:32:03,266 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@643ba1ed, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,267 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@153cb763[nodeId=world0,portId=dbLine1]
2024-11-10 20:32:03,268 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@2c3158e0, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,270 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@1f84327b[nodeId=world0,portId=dbLine2]
2024-11-10 20:32:03,271 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@39549f33, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,272 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@44aa2e13[nodeId=world0,portId=dbLine3]
2024-11-10 20:32:03,273 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@599a9cb2, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,274 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@4b55ff0a[nodeId=world0,portId=dbLine4]
2024-11-10 20:32:03,275 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@46a795de, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,276 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@4bf03fee[nodeId=world0,portId=dbLine5]
2024-11-10 20:32:03,276 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@31834a2b, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,277 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@68af87ad[nodeId=world0,portId=dbLine6]
2024-11-10 20:32:03,277 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@43d65a81, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,279 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@601eb4af[nodeId=world0,portId=dbLine7]
2024-11-10 20:32:03,279 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@11ede87f, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,281 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@51cab489[nodeId=world0,portId=dbLine8]
2024-11-10 20:32:03,281 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@2f9a10df, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,282 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@110b7837[nodeId=world0,portId=dbLine9]
2024-11-10 20:32:03,283 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@6ee88e21, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,284 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@28ee0a3c[nodeId=world0,portId=dbLine10]
2024-11-10 20:32:03,284 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@2dd1086, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,285 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@217235f5[nodeId=world0,portId=dbLine11]
2024-11-10 20:32:03,286 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@4b41587d, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,288 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@28393e82[nodeId=world0,portId=dbLine12]
2024-11-10 20:32:03,288 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@697a0948, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,290 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@265a094b[nodeId=world0,portId=dbLine13]
2024-11-10 20:32:03,291 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@1f536481, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,292 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@54c425b1[nodeId=world0,portId=dbLine14]
2024-11-10 20:32:03,293 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@50b734c4, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,294 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@634ca3e7[nodeId=world0,portId=dbLine15]
2024-11-10 20:32:03,295 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@ab4aa5e, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,297 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@1c297897[nodeId=world0,portId=dbLine16]
2024-11-10 20:32:03,297 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@33e0c716, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,299 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@35cec305[nodeId=world0,portId=dbLine17]
2024-11-10 20:32:03,300 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@237add, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,302 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@4fa86cb8[nodeId=world0,portId=dbLine18]
2024-11-10 20:32:03,303 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@54c60202, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,305 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@12952aff[nodeId=world0,portId=dbLine19]
2024-11-10 20:32:03,305 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@76a7fcbd, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:32:03,306 (DBStartup.java:86) [INFO][CORE] ================================================
2024-11-10 20:32:03,306 (DBStartup.java:87) [INFO][CORE] pwdbsrv started.
2024-11-10 20:32:03,306 (DBStartup.java:88) [INFO][CORE] Listen:tcp://************:20108
2024-11-10 20:32:03,306 (DBStartup.java:89) [INFO][CORE] ================================================
2024-11-10 20:32:03,307 (WorldStartup.java:141) [INFO][GAME] server init : begin init portIdAllot...
2024-11-10 20:32:03,309 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@3b78c683[nodeId=world0,portId=idAllot]
2024-11-10 20:32:03,352 (BufferPool.java:34) [INFO][CORE] 创建新的BufferPool缓冲池，已创建总数量：count=1
2024-11-10 20:32:03,417 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:32:03,468 (Port.java:831) [INFO][CORE] addService = org.gof.core.support.idAllot.IdAllotService@7ea1b057, idAllot, class org.gof.core.support.idAllot.IdAllotService
2024-11-10 20:32:03,468 (WorldStartup.java:175) [INFO][GAME] server init : begin check admin and bridge...
2024-11-10 20:32:03,478 (Node.java:379) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@5eb0a686[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:32:03,rogerTime=2024-11-10 20:32:03]
2024-11-10 20:32:03,481 (Node.java:352) [WARN][CORE] [bridge1]建立主动远程Node连接：remote=org.gof.core.RemoteNode@5eb0a686[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:32:03,rogerTime=2024-11-10 20:32:03]
2024-11-10 20:32:03,482 (WorldStartup.java:185) [ERROR][TEMP] ===连接排行跨服， nid=bridge1, localBridgeId=worldBridge30572, org.gof.core.RemoteNode@5eb0a686[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:32:03,rogerTime=2024-11-10 20:32:03]
2024-11-10 20:32:03,485 (Node.java:379) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@73608eb0[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:32:03,rogerTime=2024-11-10 20:32:03]
2024-11-10 20:32:03,490 (Node.java:352) [WARN][CORE] [admin0]建立主动远程Node连接：remote=org.gof.core.RemoteNode@73608eb0[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:32:03,rogerTime=2024-11-10 20:32:03]
2024-11-10 20:32:03,491 (WorldStartup.java:193) [ERROR][TEMP] ===连接管理服， nid=admin0, localBridgeId=worldBridge30572， org.gof.core.RemoteNode@73608eb0[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:32:03,rogerTime=2024-11-10 20:32:03]
2024-11-10 20:32:03,494 (Node.java:379) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@67f9cb52[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:32:03,rogerTime=2024-11-10 20:32:03]
2024-11-10 20:32:03,498 (Node.java:352) [WARN][CORE] [bridgeLeague1]建立主动远程Node连接：remote=org.gof.core.RemoteNode@67f9cb52[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:32:03,rogerTime=2024-11-10 20:32:03]
2024-11-10 20:32:03,499 (WorldStartup.java:204) [ERROR][TEMP] ===连接联盟服， nid=bridgeLeague1, localBridgeId=worldBridge30572, org.gof.core.RemoteNode@67f9cb52[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:32:03,rogerTime=2024-11-10 20:32:03]
2024-11-10 20:32:03,499 (WorldStartup.java:221) [INFO][GAME] server init : begin start platform...
2024-11-10 20:32:03,531 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@7fbf26fc[nodeId=world0,portId=http0]
2024-11-10 20:32:03,532 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@4962b41e[nodeId=world0,portId=http1]
2024-11-10 20:32:03,534 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@5b5f9003[nodeId=world0,portId=http2]
2024-11-10 20:32:03,537 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@58278366[nodeId=world0,portId=http3]
2024-11-10 20:32:03,540 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@31f77791[nodeId=world0,portId=http4]
2024-11-10 20:32:03,543 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@6d293993[nodeId=world0,portId=login0]
2024-11-10 20:32:03,544 (Port.java:831) [INFO][CORE] addService = org.gof.platform.login.LoginService@616a06e3, login, class org.gof.platform.login.LoginService
2024-11-10 20:32:03,547 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@6a55594b[nodeId=world0,portId=httpSend0]
2024-11-10 20:32:04,038 (Port.java:831) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@5faeeb56, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-10 20:32:04,040 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@7781263c[nodeId=world0,portId=httpSend1]
2024-11-10 20:32:04,044 (Port.java:831) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@15d236fd, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-10 20:32:04,046 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@33324c05[nodeId=world0,portId=httpSend2]
2024-11-10 20:32:04,049 (Port.java:831) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@33eb0d4, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-10 20:32:04,052 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@5ca4dce5[nodeId=world0,portId=gift0]
2024-11-10 20:32:04,053 (Port.java:831) [INFO][CORE] addService = org.gof.platform.gift.GiftService@7d133fb7, gift, class org.gof.platform.gift.GiftService
2024-11-10 20:32:04,055 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@6169be09[nodeId=world0,portId=gift1]
2024-11-10 20:32:04,055 (Port.java:831) [INFO][CORE] addService = org.gof.platform.gift.GiftService@5e3db14, gift, class org.gof.platform.gift.GiftService
2024-11-10 20:32:04,056 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@1ee40b5c[nodeId=world0,portId=gift2]
2024-11-10 20:32:04,057 (Port.java:831) [INFO][CORE] addService = org.gof.platform.gift.GiftService@5c448ef, gift, class org.gof.platform.gift.GiftService
2024-11-10 20:32:04,058 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@7741ae1b[nodeId=world0,portId=gift3]
2024-11-10 20:32:04,058 (Port.java:831) [INFO][CORE] addService = org.gof.platform.gift.GiftService@50e5032c, gift, class org.gof.platform.gift.GiftService
2024-11-10 20:32:04,059 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@6885ed19[nodeId=world0,portId=gift4]
2024-11-10 20:32:04,060 (Port.java:831) [INFO][CORE] addService = org.gof.platform.gift.GiftService@10817f46, gift, class org.gof.platform.gift.GiftService
2024-11-10 20:32:04,062 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.chat.ChatPort@16681017[nodeId=world0,portId=chat0]
2024-11-10 20:32:04,062 (Port.java:831) [INFO][CORE] addService = org.gof.platform.chat.ChatService@37775bb1, chat, class org.gof.platform.chat.ChatService
2024-11-10 20:32:04,064 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.chat.ChatPort@37e7c4cc[nodeId=world0,portId=chat1]
2024-11-10 20:32:04,064 (Port.java:831) [INFO][CORE] addService = org.gof.platform.chat.ChatService@3a40bb52, chat, class org.gof.platform.chat.ChatService
2024-11-10 20:32:04,074 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.DefaultPort@477523ba[nodeId=world0,portId=port0]
2024-11-10 20:32:04,074 (WorldStartup.java:233) [INFO][GAME] server init : default port started...
2024-11-10 20:32:04,076 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.SeamService@203e705e, seam, class org.gof.demo.seam.SeamService
2024-11-10 20:32:04,077 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.intergration.PFService@78c74647, worldPF, class org.gof.demo.worldsrv.intergration.PFService
2024-11-10 20:32:04,077 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.test.TestService@39652a30, test, class org.gof.demo.worldsrv.test.TestService
2024-11-10 20:32:04,100 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.common.GameService@6238d5e0, gameValue, class org.gof.demo.worldsrv.common.GameService
2024-11-10 20:32:04,106 (BufferPool.java:34) [INFO][CORE] 创建新的BufferPool缓冲池，已创建总数量：count=2
2024-11-10 20:32:04,108 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.common.ServerListService@3f048c86, serverList, class org.gof.demo.worldsrv.common.ServerListService
2024-11-10 20:32:04,109 (WorldStartup.java:289) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2024-11-10 20:32:04,115 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountService@7e191fda, gate, class org.gof.demo.seam.account.AccountService
2024-11-10 20:32:04,117 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@e3899fd[nodeId=world0,portId=accountPort0]
2024-11-10 20:32:04,119 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@7d484fcd, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:32:04,120 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=port0, costTime = 45
2024-11-10 20:32:04,120 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2024-11-10 20:32:04,121 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@47187f50[nodeId=world0,portId=accountPort1]
2024-11-10 20:32:04,121 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@********, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:32:04,123 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@578c3fd9[nodeId=world0,portId=accountPort2]
2024-11-10 20:32:04,123 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@245cb8df, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:32:04,124 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@125f16b2[nodeId=world0,portId=accountPort3]
2024-11-10 20:32:04,125 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@5384ce66, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:32:04,126 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@79195c22[nodeId=world0,portId=accountPort4]
2024-11-10 20:32:04,126 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@c9b5a99, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:32:04,128 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@19a5b637[nodeId=world0,portId=accountPort5]
2024-11-10 20:32:04,129 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@4f3c7808, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:32:04,130 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@256589a1[nodeId=world0,portId=accountPort6]
2024-11-10 20:32:04,131 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@935493d, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:32:04,132 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@3de79067[nodeId=world0,portId=accountPort7]
2024-11-10 20:32:04,132 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@1eb85a47, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:32:04,133 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@87f501f[nodeId=world0,portId=accountPort8]
2024-11-10 20:32:04,134 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@********, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:32:04,135 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@4863c8ac[nodeId=world0,portId=accountPort9]
2024-11-10 20:32:04,135 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@56fda064, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:32:04,136 (Port.java:831) [INFO][CORE] addService = org.gof.core.statistics.StatisticsService@6cdee57, serv0, class org.gof.core.statistics.StatisticsService
2024-11-10 20:32:04,138 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@4c4c7d6c[nodeId=world0,portId=humanPort0]
2024-11-10 20:32:04,140 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@7f0766ef, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:32:04,141 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@403364e9[nodeId=world0,portId=humanPort1]
2024-11-10 20:32:04,142 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@447521e, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:32:04,143 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@458031da[nodeId=world0,portId=humanPort2]
2024-11-10 20:32:04,143 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@7be94cd6, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:32:04,145 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@27ab206[nodeId=world0,portId=humanPort3]
2024-11-10 20:32:04,146 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@3344d163, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:32:04,148 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@7f22687e[nodeId=world0,portId=humanPort4]
2024-11-10 20:32:04,148 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@6af87130, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:32:04,149 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:32:04,150 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@1e495414[nodeId=world0,portId=humanPort5]
2024-11-10 20:32:04,150 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@3711c71c, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:32:04,151 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@30508066[nodeId=world0,portId=humanPort6]
2024-11-10 20:32:04,152 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@9408857, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:32:04,154 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@1bee0085[nodeId=world0,portId=humanPort7]
2024-11-10 20:32:04,154 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@16e1219f, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:32:04,155 (GameServiceManager.java:28) [INFO][GAME] 开始初始化service
2024-11-10 20:32:04,157 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@7dbae40[nodeId=world0,portId=game0]
2024-11-10 20:32:04,160 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort9, costTime = 25
2024-11-10 20:32:04,164 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort7, costTime = 32
2024-11-10 20:32:04,165 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort5, costTime = 36
2024-11-10 20:32:04,167 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort6, costTime = 36
2024-11-10 20:32:04,172 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort0, costTime = 54
2024-11-10 20:32:04,174 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort2, costTime = 51
2024-11-10 20:32:04,174 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort3, costTime = 46
2024-11-10 20:32:04,174 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort4, costTime = 48
2024-11-10 20:32:04,174 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort1, costTime = 53
2024-11-10 20:32:04,169 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=humanPort0, costTime = 31
2024-11-10 20:32:04,175 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=humanPort1, costTime = 34
2024-11-10 20:32:04,177 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort8, costTime = 43
2024-11-10 20:32:04,187 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=humanPort6, costTime = 35
2024-11-10 20:32:04,193 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=game0, costTime = 35
2024-11-10 20:32:04,193 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=humanPort7, costTime = 39
2024-11-10 20:32:04,197 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=humanPort3, costTime = 52
2024-11-10 20:32:04,197 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=humanPort4, costTime = 49
2024-11-10 20:32:04,197 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=136800001, end=136900000, portId=humanPort5, costTime = 47
2024-11-10 20:32:04,197 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=136500001, end=136600000, portId=humanPort2, costTime = 53
2024-11-10 20:32:11,920 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.confirm.ConfirmGlobalService@f8cfdca, confirm, class org.gof.demo.worldsrv.confirm.ConfirmGlobalService
2024-11-10 20:32:11,926 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@1f12d5e0[nodeId=world0,portId=game1]
2024-11-10 20:32:11,953 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.name.NameService@2059f73f, name, class org.gof.demo.worldsrv.name.NameService
2024-11-10 20:32:11,954 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.common.HumanCreateApplyService@6e93603a, humanCreateApply, class org.gof.demo.worldsrv.common.HumanCreateApplyService
2024-11-10 20:32:11,955 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.gm.GMGameService@79b0281e, gm, class org.gof.demo.worldsrv.gm.GMGameService
2024-11-10 20:32:11,965 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanGlobalService@3955ed2d, humanGlobal, class org.gof.demo.worldsrv.human.HumanGlobalService
2024-11-10 20:32:11,978 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=137200001, end=137300000, portId=game1, costTime = 52
2024-11-10 20:32:12,301 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.mail.MailService@156092ab, mail, class org.gof.demo.worldsrv.mail.MailService
2024-11-10 20:32:12,308 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@53d9af1[nodeId=world0,portId=game2]
2024-11-10 20:32:12,310 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.activity.ActivityControlService@3ad8fe8, activity, class org.gof.demo.worldsrv.activity.ActivityControlService
2024-11-10 20:32:12,311 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.rank.RankService@49405428, rank, class org.gof.demo.worldsrv.rank.RankService
2024-11-10 20:32:12,312 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.inform.ChatService@596a4cc6, chat, class org.gof.demo.worldsrv.inform.ChatService
2024-11-10 20:32:12,312 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.common.DataResetService@1977940f, dataReset, class org.gof.demo.worldsrv.common.DataResetService
2024-11-10 20:32:12,313 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.httpPush.HttpPushService@764a8a06, httpPush, class org.gof.demo.worldsrv.httpPush.HttpPushService
2024-11-10 20:32:12,342 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=137300001, end=137400000, portId=game2, costTime = 34
2024-11-10 20:32:12,759 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.pocketLine.PocketLineService@2d403dc, pocketLine, class org.gof.demo.worldsrv.pocketLine.PocketLineService
2024-11-10 20:32:12,766 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@5d68be4f[nodeId=world0,portId=game3]
2024-11-10 20:32:12,811 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.arena.ArenaRankedService@c13fdf6, arenaRanked, class org.gof.demo.worldsrv.arena.ArenaRankedService
2024-11-10 20:32:12,849 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=137400001, end=137500000, portId=game3, costTime = 76
2024-11-10 20:32:12,893 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.worldBoss.WorldBossService@5724c359, worldBoss, class org.gof.demo.worldsrv.worldBoss.WorldBossService
2024-11-10 20:32:13,267 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@4fef5792[nodeId=world0,portId=game4]
2024-11-10 20:32:13,269 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.team.TeamService@4de464a6, team, class org.gof.demo.worldsrv.team.TeamService
2024-11-10 20:32:13,270 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.carPark.CarParkService@4323836a, carPark, class org.gof.demo.worldsrv.carPark.CarParkService
2024-11-10 20:32:13,273 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.mail.FillMailService@6c311ce0, fillMail, class org.gof.demo.worldsrv.mail.FillMailService
2024-11-10 20:32:13,273 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.captureSlave.CaptureSlaveService@3356eadf, captureSlave, class org.gof.demo.worldsrv.captureSlave.CaptureSlaveService
2024-11-10 20:32:13,306 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=137500001, end=137600000, portId=game4, costTime = 39
2024-11-10 20:32:13,331 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:32:13,639 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@5f0ca069[nodeId=world0,portId=game5]
2024-11-10 20:32:13,650 (CheckWorldService.java:95) [INFO][GAME] 准备注册游戏服信息到中心服！serverId=30572，nodeId=worldBridge30572
2024-11-10 20:32:13,673 (CheckWorldService.java:100) [INFO][TEMP] ===代码未实现，检测的先不处理
2024-11-10 20:32:13,674 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.check.CheckWorldService@78238d61, checkWorld, class org.gof.demo.worldsrv.check.CheckWorldService
2024-11-10 20:32:13,680 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.arena.ArenaService@7fb4c7d6, arena, class org.gof.demo.worldsrv.arena.ArenaService
2024-11-10 20:32:13,684 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=137600001, end=137700000, portId=game5, costTime = 45
2024-11-10 20:32:13,695 (GuildLeagueWarmUpService.java:1079) [ERROR][TEMP] ===timeNow=1731241933650, interval=257266350，开始战斗时间2024-11-13 20:00:00
2024-11-10 20:32:13,695 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.guild.league.GuildLeagueWarmUpService@33d76397, guildLeagueWarmUp, class org.gof.demo.worldsrv.guild.league.GuildLeagueWarmUpService
2024-11-10 20:32:14,007 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:32:14,007 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:32:14,038 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:32:14,053 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@6e66b498[nodeId=world0,portId=game6]
2024-11-10 20:32:14,067 (GuildService.java:161) [INFO][TEMP] =================init GuildService
2024-11-10 20:32:14,068 (GuildService.java:251) [INFO][TEMP] ===gve开始时间=1731297900000，2024-11-11 12:05:00
2024-11-10 20:32:14,068 (GuildService.java:256) [INFO][TEMP] ===gve结束时间=1731297900000，2024-11-11 12:05:00
2024-11-10 20:32:14,097 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=137700001, end=137800000, portId=game6, costTime = 44
2024-11-10 20:32:14,164 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:32:14,223 (GuildService.java:323) [ERROR][TEMP] initCheckRank 0
2024-11-10 20:32:14,225 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.guild.GuildService@41465774, guild, class org.gof.demo.worldsrv.guild.GuildService
2024-11-10 20:32:14,423 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.home.FarmService@4c802834, farm, class org.gof.demo.worldsrv.home.FarmService
2024-11-10 20:32:14,426 (HumanManager.java:4386) [INFO][GAME] 游戏启动时
2024-11-10 20:32:14,426 (WorldStartup.java:333) [ERROR][GAME] ====================
2024-11-10 20:32:14,427 (WorldStartup.java:334) [ERROR][GAME] world0 started.
2024-11-10 20:32:14,427 (WorldStartup.java:335) [ERROR][GAME] Listen:tcp://************:20108
2024-11-10 20:32:14,427 (WorldStartup.java:336) [ERROR][GAME] ====================
2024-11-10 20:32:14,436 (HumanManager.java:4445) [INFO][TEMP] ===去后台请求屏蔽字库
2024-11-10 20:32:14,437 (HumanManager.java:4486) [INFO][TEMP] ===去后台请求跑马灯
2024-11-10 20:32:14,437 (HumanManager.java:4452) [INFO][TEMP] ===ip=************，addr=tcp://************:13000
2024-11-10 20:32:14,438 (HumanManager.java:4468) [ERROR][TEMP] ===通知admin注册ip地址url=http://************:8018/gameServer, jo={port=8018, ip=************, serverId=30572}
2024-11-10 20:32:14,500 (WorldStartup.java:365) [ERROR][GAME] 开启数据热更新扫描...
2024-11-10 20:32:14,547 (ClassScanProcess.java:58) [ERROR][GAME] 开启类热更新扫描调度，每5分钟执行一次
2024-11-10 20:32:14,548 (WorldStartup.java:369) [ERROR][GAME] 开启类热更新扫描...
2024-11-10 20:32:14,550 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@4067634b[nodeId=world0,portId=connPort0]
2024-11-10 20:32:14,551 (Port.java:831) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@3b64f131, conn, class org.gof.core.connsrv.ConnService
2024-11-10 20:32:14,554 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@3c5e4aac[nodeId=world0,portId=connPort1]
2024-11-10 20:32:14,554 (Port.java:831) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@20afd96f, conn, class org.gof.core.connsrv.ConnService
2024-11-10 20:32:14,556 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@4cded2cd[nodeId=world0,portId=connPort2]
2024-11-10 20:32:14,557 (Port.java:831) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@12e40e98, conn, class org.gof.core.connsrv.ConnService
2024-11-10 20:32:14,559 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@5db04bd2[nodeId=world0,portId=connPort3]
2024-11-10 20:32:14,559 (Port.java:831) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@6f921e32, conn, class org.gof.core.connsrv.ConnService
2024-11-10 20:32:14,561 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@1cee3e05[nodeId=world0,portId=connPort4]
2024-11-10 20:32:14,561 (Port.java:831) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@63f2d024, conn, class org.gof.core.connsrv.ConnService
2024-11-10 20:32:14,562 (WorldStartup.java:381) [ERROR][GAME] 开启连接服务
2024-11-10 20:32:14,562 (WorldStartup.java:383) [ERROR][GAME] 启动完成...
2024-11-10 20:32:14,572 (ServCheck.java:68) [ERROR][GAME] 
╔═══════════════════╤══════════════╗
║ service           │ costTime(ms) ║
╠═══════════════════╪══════════════╣
║ guild             │ 157          ║
╟───────────────────┼──────────────╢
║ worldBoss         │ 76           ║
╟───────────────────┼──────────────╢
║ checkWorld        │ 23           ║
╟───────────────────┼──────────────╢
║ name              │ 14           ║
╟───────────────────┼──────────────╢
║ activity          │ 9            ║
╟───────────────────┼──────────────╢
║ guildLeagueWarmUp │ 7            ║
╟───────────────────┼──────────────╢
║ farm              │ 7            ║
╟───────────────────┼──────────────╢
║ arena             │ 4            ║
╟───────────────────┼──────────────╢
║ arenaRanked       │ 0            ║
╟───────────────────┼──────────────╢
║ mail              │ 0            ║
╟───────────────────┼──────────────╢
║ carPark           │ 0            ║
╟───────────────────┼──────────────╢
║ gm                │ 0            ║
╟───────────────────┼──────────────╢
║ team              │ 0            ║
╟───────────────────┼──────────────╢
║ humanCreateApply  │ 0            ║
╟───────────────────┼──────────────╢
║ httpPush          │ 0            ║
╟───────────────────┼──────────────╢
║ confirm           │ 0            ║
╟───────────────────┼──────────────╢
║ chat              │ 0            ║
╟───────────────────┼──────────────╢
║ pocketLine        │ 0            ║
╟───────────────────┼──────────────╢
║ humanGlobal       │ 0            ║
╟───────────────────┼──────────────╢
║ rank              │ 0            ║
╟───────────────────┼──────────────╢
║ dataReset         │ 0            ║
╟───────────────────┼──────────────╢
║ fillMail          │ 0            ║
╟───────────────────┼──────────────╢
║ captureSlave      │ 0            ║
╚═══════════════════╧══════════════╝

2024-11-10 20:32:16,832 (Port.java:307) [WARN][CORE_EFFECT] [httpSend0]本次心跳操作总时间较长，达到了2369毫秒。portName=httpSend0, callCount=3, resultCount=0, countQueue=0, time={call=2369}
2024-11-10 20:32:17,612 (WorldStartup.java:342) [INFO][GAME] 触发关闭服务器操作,开始踢人
2024-11-10 20:32:17,614 (HumanGlobalService.java:567) [INFO][GAME] ===已踢出所有玩家
2024-11-10 20:32:17,614 (WorldStartup.java:351) [INFO][GAME] 关闭服务器-检查db更新队列完成! 15s后服务器将关闭
2024-11-10 20:32:17,763 (Node.java:689) [INFO][CORE] ====humanPort status : active = 8 , dead= 0 , dead list:[]
2024-11-10 20:32:17,763 (Node.java:740) [INFO][CORE] ====accountProt status : active = 10 , dead= 0 , dead list:[]
2024-11-10 20:32:19,648 (DBPartService.java:384) [INFO][CORE_DB] [dbPart3]远程调用刷新数据库全部写缓存。
2024-11-10 20:32:19,648 (DBPartService.java:384) [INFO][CORE_DB] [dbPart4]远程调用刷新数据库全部写缓存。
2024-11-10 20:32:19,648 (DBPartService.java:384) [INFO][CORE_DB] [dbPart7]远程调用刷新数据库全部写缓存。
2024-11-10 20:32:19,648 (DBPartService.java:384) [INFO][CORE_DB] [dbPart1]远程调用刷新数据库全部写缓存。
2024-11-10 20:32:19,648 (DBPartService.java:384) [INFO][CORE_DB] [dbPart0]远程调用刷新数据库全部写缓存。
2024-11-10 20:32:19,648 (DBPartService.java:384) [INFO][CORE_DB] [dbPart5]远程调用刷新数据库全部写缓存。
2024-11-10 20:32:19,648 (DBPartService.java:384) [INFO][CORE_DB] [dbPart2]远程调用刷新数据库全部写缓存。
2024-11-10 20:32:19,648 (DBPartService.java:384) [INFO][CORE_DB] [dbPart6]远程调用刷新数据库全部写缓存。
2024-11-10 20:32:24,558 (ClassScanProcess.java:135) [ERROR][GAME] 开始一次扫描key=216594811709003132515,jarValue=216594811709003132515
2024-11-10 20:35:46,401 (WorldStartup.java:71) [INFO][GAME] 正在启动游戏服务器
2024-11-10 20:35:46,453 (WorldStartup.java:85) [INFO][GAME] 正在初始化事件容器
2024-11-10 20:35:46,884 (WorldStartup.java:88) [INFO][GAME] 正在初始化协议函数指针池
2024-11-10 20:35:49,299 (WorldStartup.java:96) [INFO][GAME] 加载策划数据
2024-11-10 20:35:49,610 (ConfBreakBigPrizePreview.java:283) [INFO][TEMP] 表ConfBreakBigPrizePreview有问题!
2024-11-10 20:35:53,240 (GlobalConfVal.java:1032) [INFO][TEMP] ===加载副本表数量=70
2024-11-10 20:35:55,028 (RedisTools.java:46) [INFO][org.gof.core.dbsrv.redis.RedisTools] initRedisClient redis constr:redis://127.0.0.1:6379
2024-11-10 20:35:55,329 (RedisTools.java:54) [INFO][org.gof.core.dbsrv.redis.RedisTools] ###################### redis connection success
2024-11-10 20:35:55,347 (MysqlTool.java:97) [INFO][org.gof.core.dbsrv.redis.MysqlTool] MySQLPool setIdleTimeout:0
2024-11-10 20:35:55,379 (MysqlTool.java:40) [INFO][org.gof.core.dbsrv.redis.MysqlTool] ===createMySQLPool poolOptions={"connectionTimeout":30,"connectionTimeoutUnit":"SECONDS","eventLoopSize":0,"idleTimeout":0,"idleTimeoutUnit":"SECONDS","maxLifetime":0,"maxLifetimeUnit":"SECONDS","maxSize":40,"maxWaitQueueSize":-1,"name":"__vertx.DEFAULT","poolCleanerPeriod":1000,"shared":false}
2024-11-10 20:35:55,394 (EntityManager.java:50) [INFO][org.gof.core.dbsrv.redis.EntityManager] EntityManager init 
2024-11-10 20:35:55,401 (WorldStartup.java:132) [INFO][GAME] 前置准备完成，开始启动Node, nodeId=world0, nodeAddr=tcp://************:20108
2024-11-10 20:35:55,421 (WorldStartup.java:135) [INFO][GAME] server init : begin start node...
2024-11-10 20:35:55,707 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:35:55,709 (FieldTable.java:58) [INFO][GAME] server init : db executeQuery...
2024-11-10 20:35:55,756 (FieldTable.java:69) [INFO][GAME] server init : initTable...
2024-11-10 20:35:55,764 (DBStartup.java:57) [INFO][CORE] ===初始化表信息sword_game_dev, ***********************************************************************************************************************, root, 123456
2024-11-10 20:35:55,812 (StdSchedulerFactory.java:1208) [INFO][org.quartz.impl.StdSchedulerFactory] Using default implementation for ThreadExecutor
2024-11-10 20:35:55,817 (SimpleThreadPool.java:268) [INFO][org.quartz.simpl.SimpleThreadPool] Job execution threads will use class loader of thread: main
2024-11-10 20:35:55,837 (SchedulerSignalerImpl.java:61) [INFO][org.quartz.core.SchedulerSignalerImpl] Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2024-11-10 20:35:55,838 (RAMJobStore.java:155) [INFO][org.quartz.simpl.RAMJobStore] RAMJobStore initialized.
2024-11-10 20:35:55,839 (StdSchedulerFactory.java:1362) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler 'DefaultQuartzScheduler' initialized from default resource file in Quartz package: 'quartz.properties'
2024-11-10 20:35:55,839 (StdSchedulerFactory.java:1366) [INFO][org.quartz.impl.StdSchedulerFactory] Quartz scheduler version: 2.4.0-SNAPSHOT
2024-11-10 20:35:55,848 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@715fa8c5[nodeId=world0,portId=dbPart0]
2024-11-10 20:35:55,857 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@68a305eb, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:35:55,859 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@1ef31f71[nodeId=world0,portId=dbPart1]
2024-11-10 20:35:55,859 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@6e8fdd19, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:35:55,860 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@27976390[nodeId=world0,portId=dbPart2]
2024-11-10 20:35:55,861 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@37e0056e, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:35:55,862 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@3375ebd3[nodeId=world0,portId=dbPart3]
2024-11-10 20:35:55,862 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@40943a6, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:35:55,864 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@5e50df2e[nodeId=world0,portId=dbPart4]
2024-11-10 20:35:55,865 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@100aa331, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:35:55,867 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@2abafa97[nodeId=world0,portId=dbPart5]
2024-11-10 20:35:55,868 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@6f6cc7da, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:35:55,869 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@5460edd3[nodeId=world0,portId=dbPart6]
2024-11-10 20:35:55,869 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@5dbbb292, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:35:55,871 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@587c5c1[nodeId=world0,portId=dbPart7]
2024-11-10 20:35:55,871 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBPartService@528c8c1, serv0, class org.gof.core.dbsrv.DBPartService
2024-11-10 20:35:55,872 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@27960b1e[nodeId=world0,portId=dbLine0]
2024-11-10 20:35:55,873 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@3d3a1903, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,875 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@2c3158e0[nodeId=world0,portId=dbLine1]
2024-11-10 20:35:55,875 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@412ebe64, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,877 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@39549f33[nodeId=world0,portId=dbLine2]
2024-11-10 20:35:55,877 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@dbddbe3, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,878 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@599a9cb2[nodeId=world0,portId=dbLine3]
2024-11-10 20:35:55,880 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@3a1b36a1, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,881 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@46a795de[nodeId=world0,portId=dbLine4]
2024-11-10 20:35:55,882 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@256a0d95, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,883 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@31834a2b[nodeId=world0,portId=dbLine5]
2024-11-10 20:35:55,884 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@44f0ff2b, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,885 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@43d65a81[nodeId=world0,portId=dbLine6]
2024-11-10 20:35:55,885 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@9cfc77, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,887 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@11ede87f[nodeId=world0,portId=dbLine7]
2024-11-10 20:35:55,888 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@7675c171, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,889 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@2f9a10df[nodeId=world0,portId=dbLine8]
2024-11-10 20:35:55,889 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@773c2214, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,890 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@6ee88e21[nodeId=world0,portId=dbLine9]
2024-11-10 20:35:55,891 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@78d23d6a, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,892 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@2dd1086[nodeId=world0,portId=dbLine10]
2024-11-10 20:35:55,892 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@6b8d54da, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,894 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@4b41587d[nodeId=world0,portId=dbLine11]
2024-11-10 20:35:55,894 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@4aebee4b, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,896 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@697a0948[nodeId=world0,portId=dbLine12]
2024-11-10 20:35:55,897 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@7cf63b9a, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,899 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@1f536481[nodeId=world0,portId=dbLine13]
2024-11-10 20:35:55,900 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@5234b61a, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,901 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@50b734c4[nodeId=world0,portId=dbLine14]
2024-11-10 20:35:55,901 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@2744dcae, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,902 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@ab4aa5e[nodeId=world0,portId=dbLine15]
2024-11-10 20:35:55,903 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@b14b60a, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,904 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@33e0c716[nodeId=world0,portId=dbLine16]
2024-11-10 20:35:55,905 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@1d6a8386, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,906 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@237add[nodeId=world0,portId=dbLine17]
2024-11-10 20:35:55,906 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@491cafec, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,907 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@54c60202[nodeId=world0,portId=dbLine18]
2024-11-10 20:35:55,908 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@7889b4b9, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,909 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@76a7fcbd[nodeId=world0,portId=dbLine19]
2024-11-10 20:35:55,909 (Port.java:831) [INFO][CORE] addService = org.gof.core.dbsrv.DBLineService@433ea2ac, serv0, class org.gof.core.dbsrv.DBLineService
2024-11-10 20:35:55,909 (DBStartup.java:86) [INFO][CORE] ================================================
2024-11-10 20:35:55,909 (DBStartup.java:87) [INFO][CORE] pwdbsrv started.
2024-11-10 20:35:55,909 (DBStartup.java:88) [INFO][CORE] Listen:tcp://************:20108
2024-11-10 20:35:55,910 (DBStartup.java:89) [INFO][CORE] ================================================
2024-11-10 20:35:55,911 (WorldStartup.java:141) [INFO][GAME] server init : begin init portIdAllot...
2024-11-10 20:35:55,912 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.dbsrv.DBPort@7f5614f9[nodeId=world0,portId=idAllot]
2024-11-10 20:35:55,956 (BufferPool.java:34) [INFO][CORE] 创建新的BufferPool缓冲池，已创建总数量：count=1
2024-11-10 20:35:56,049 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:35:56,080 (Port.java:831) [INFO][CORE] addService = org.gof.core.support.idAllot.IdAllotService@293ce3aa, idAllot, class org.gof.core.support.idAllot.IdAllotService
2024-11-10 20:35:56,080 (WorldStartup.java:175) [INFO][GAME] server init : begin check admin and bridge...
2024-11-10 20:35:56,089 (Node.java:379) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@73608eb0[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:35:56,rogerTime=2024-11-10 20:35:56]
2024-11-10 20:35:56,093 (Node.java:352) [WARN][CORE] [bridge1]建立主动远程Node连接：remote=org.gof.core.RemoteNode@73608eb0[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:35:56,rogerTime=2024-11-10 20:35:56]
2024-11-10 20:35:56,093 (WorldStartup.java:185) [ERROR][TEMP] ===连接排行跨服， nid=bridge1, localBridgeId=worldBridge30572, org.gof.core.RemoteNode@73608eb0[remoteId=bridge1,remoteAddr=tcp://127.0.0.1:34000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:35:56,rogerTime=2024-11-10 20:35:56]
2024-11-10 20:35:56,096 (Node.java:379) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@67f9cb52[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:35:56,rogerTime=2024-11-10 20:35:56]
2024-11-10 20:35:56,101 (Node.java:352) [WARN][CORE] [admin0]建立主动远程Node连接：remote=org.gof.core.RemoteNode@67f9cb52[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:35:56,rogerTime=2024-11-10 20:35:56]
2024-11-10 20:35:56,101 (WorldStartup.java:193) [ERROR][TEMP] ===连接管理服， nid=admin0, localBridgeId=worldBridge30572， org.gof.core.RemoteNode@67f9cb52[remoteId=admin0,remoteAddr=tcp://************:13000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:35:56,rogerTime=2024-11-10 20:35:56]
2024-11-10 20:35:56,104 (Node.java:379) [INFO][CORE_REMOTE] [world0]建立[主动]远程Node连接：remote=org.gof.core.RemoteNode@2de9ca6[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:35:56,rogerTime=2024-11-10 20:35:56]
2024-11-10 20:35:56,112 (Node.java:352) [WARN][CORE] [bridgeLeague1]建立主动远程Node连接：remote=org.gof.core.RemoteNode@2de9ca6[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:35:56,rogerTime=2024-11-10 20:35:56]
2024-11-10 20:35:56,113 (WorldStartup.java:204) [ERROR][TEMP] ===连接联盟服， nid=bridgeLeague1, localBridgeId=worldBridge30572, org.gof.core.RemoteNode@2de9ca6[remoteId=bridgeLeague1,remoteAddr=tcp://127.0.0.1:44000,localAlias=worldBridge30572,connected=false,main=true,createTime=2024-11-10 20:35:56,rogerTime=2024-11-10 20:35:56]
2024-11-10 20:35:56,113 (WorldStartup.java:221) [INFO][GAME] server init : begin start platform...
2024-11-10 20:35:56,136 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@11c78080[nodeId=world0,portId=http0]
2024-11-10 20:35:56,138 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@4fecf308[nodeId=world0,portId=http1]
2024-11-10 20:35:56,139 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@7c11d32[nodeId=world0,portId=http2]
2024-11-10 20:35:56,140 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@7f5fcfe9[nodeId=world0,portId=http3]
2024-11-10 20:35:56,182 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.HttpPort@552fee7a[nodeId=world0,portId=http4]
2024-11-10 20:35:56,192 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.login.LoginPort@6d293993[nodeId=world0,portId=login0]
2024-11-10 20:35:56,193 (Port.java:831) [INFO][CORE] addService = org.gof.platform.login.LoginService@475f5672, login, class org.gof.platform.login.LoginService
2024-11-10 20:35:56,195 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@66e827a8[nodeId=world0,portId=httpSend0]
2024-11-10 20:35:56,733 (Port.java:831) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@129c4d19, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-10 20:35:56,736 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@4a31ed12[nodeId=world0,portId=httpSend1]
2024-11-10 20:35:56,740 (Port.java:831) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@cbf1997, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-10 20:35:56,742 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.http.HttpAsyncPort@5396eeb1[nodeId=world0,portId=httpSend2]
2024-11-10 20:35:56,746 (Port.java:831) [INFO][CORE] addService = org.gof.platform.http.HttpAsyncSendService@5fdd97c1, httpSend, class org.gof.platform.http.HttpAsyncSendService
2024-11-10 20:35:56,750 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@6b9c42bd[nodeId=world0,portId=gift0]
2024-11-10 20:35:56,752 (Port.java:831) [INFO][CORE] addService = org.gof.platform.gift.GiftService@584ca390, gift, class org.gof.platform.gift.GiftService
2024-11-10 20:35:56,754 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@3e53c4ad[nodeId=world0,portId=gift1]
2024-11-10 20:35:56,755 (Port.java:831) [INFO][CORE] addService = org.gof.platform.gift.GiftService@33eb0d4, gift, class org.gof.platform.gift.GiftService
2024-11-10 20:35:56,756 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@2aea717c[nodeId=world0,portId=gift2]
2024-11-10 20:35:56,756 (Port.java:831) [INFO][CORE] addService = org.gof.platform.gift.GiftService@5ca4dce5, gift, class org.gof.platform.gift.GiftService
2024-11-10 20:35:56,757 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@40bd0f8[nodeId=world0,portId=gift3]
2024-11-10 20:35:56,757 (Port.java:831) [INFO][CORE] addService = org.gof.platform.gift.GiftService@7eb27768, gift, class org.gof.platform.gift.GiftService
2024-11-10 20:35:56,759 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.gift.GiftPort@5fb54740[nodeId=world0,portId=gift4]
2024-11-10 20:35:56,759 (Port.java:831) [INFO][CORE] addService = org.gof.platform.gift.GiftService@325162e9, gift, class org.gof.platform.gift.GiftService
2024-11-10 20:35:56,762 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.chat.ChatPort@703e8050[nodeId=world0,portId=chat0]
2024-11-10 20:35:56,763 (Port.java:831) [INFO][CORE] addService = org.gof.platform.chat.ChatService@49c1e294, chat, class org.gof.platform.chat.ChatService
2024-11-10 20:35:56,764 (Port.java:182) [INFO][CORE] 启动Port=org.gof.platform.chat.ChatPort@545d2560[nodeId=world0,portId=chat1]
2024-11-10 20:35:56,764 (Port.java:831) [INFO][CORE] addService = org.gof.platform.chat.ChatService@124eb83d, chat, class org.gof.platform.chat.ChatService
2024-11-10 20:35:56,772 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.DefaultPort@3079c26a[nodeId=world0,portId=port0]
2024-11-10 20:35:56,773 (WorldStartup.java:233) [INFO][GAME] server init : default port started...
2024-11-10 20:35:56,775 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.SeamService@1e2445f6, seam, class org.gof.demo.seam.SeamService
2024-11-10 20:35:56,776 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.intergration.PFService@17ec335a, worldPF, class org.gof.demo.worldsrv.intergration.PFService
2024-11-10 20:35:56,776 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.test.TestService@284990de, test, class org.gof.demo.worldsrv.test.TestService
2024-11-10 20:35:56,797 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.common.GameService@21274afe, gameValue, class org.gof.demo.worldsrv.common.GameService
2024-11-10 20:35:56,802 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.common.ServerListService@3e43f049, serverList, class org.gof.demo.worldsrv.common.ServerListService
2024-11-10 20:35:56,802 (WorldStartup.java:289) [INFO][GAME] server nodeID=world0, defaultNodeId=world0
2024-11-10 20:35:56,809 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountService@70c29356, gate, class org.gof.demo.seam.account.AccountService
2024-11-10 20:35:56,811 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@61b60600[nodeId=world0,portId=accountPort0]
2024-11-10 20:35:56,813 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@27f71195, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:35:56,817 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@336f49a1[nodeId=world0,portId=accountPort1]
2024-11-10 20:35:56,817 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@2c8b8de0, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:35:56,820 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@226d5af0[nodeId=world0,portId=accountPort2]
2024-11-10 20:35:56,821 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@527937d0, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:35:56,823 (BufferPool.java:34) [INFO][CORE] 创建新的BufferPool缓冲池，已创建总数量：count=2
2024-11-10 20:35:56,825 (BufferPool.java:34) [INFO][CORE] 创建新的BufferPool缓冲池，已创建总数量：count=3
2024-11-10 20:35:56,826 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@24d8f87a[nodeId=world0,portId=accountPort3]
2024-11-10 20:35:56,827 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@3f048c86, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:35:56,831 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=port0, costTime = 59
2024-11-10 20:35:56,833 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@7e191fda[nodeId=world0,portId=accountPort4]
2024-11-10 20:35:56,833 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@6562cc23, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:35:56,833 (HttpServer.java:46) [INFO][PLATFORM] PlatformHTTP服务启动成功 端口:8018
2024-11-10 20:35:56,834 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@e3899fd[nodeId=world0,portId=accountPort5]
2024-11-10 20:35:56,835 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@7d484fcd, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:35:56,836 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@47187f50[nodeId=world0,portId=accountPort6]
2024-11-10 20:35:56,837 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@********, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:35:56,838 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@578c3fd9[nodeId=world0,portId=accountPort7]
2024-11-10 20:35:56,839 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@245cb8df, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:35:56,840 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@125f16b2[nodeId=world0,portId=accountPort8]
2024-11-10 20:35:56,840 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@5384ce66, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:35:56,842 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.seam.account.AccountPort@79195c22[nodeId=world0,portId=accountPort9]
2024-11-10 20:35:56,842 (Port.java:831) [INFO][CORE] addService = org.gof.demo.seam.account.AccountLoginService@c9b5a99, accountLoginServer, class org.gof.demo.seam.account.AccountLoginService
2024-11-10 20:35:56,842 (Port.java:831) [INFO][CORE] addService = org.gof.core.statistics.StatisticsService@70be89ec, serv0, class org.gof.core.statistics.StatisticsService
2024-11-10 20:35:56,844 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@4b4969ea[nodeId=world0,portId=humanPort0]
2024-11-10 20:35:56,846 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@11c581a0, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:35:56,850 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@9b367c8[nodeId=world0,portId=humanPort1]
2024-11-10 20:35:56,851 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@34c62fdf, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:35:56,854 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort2, costTime = 31
2024-11-10 20:35:56,854 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@ca7e37f[nodeId=world0,portId=humanPort2]
2024-11-10 20:35:56,854 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@99a8de3, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:35:56,855 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort1, costTime = 36
2024-11-10 20:35:56,856 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort0, costTime = 45
2024-11-10 20:35:56,857 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@67c6f4d8[nodeId=world0,portId=humanPort3]
2024-11-10 20:35:56,857 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@3a6e9856, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:35:56,859 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@6cdee57[nodeId=world0,portId=humanPort4]
2024-11-10 20:35:56,859 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@482f7af0, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:35:56,860 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@4c4c7d6c[nodeId=world0,portId=humanPort5]
2024-11-10 20:35:56,861 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@7f0766ef, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:35:56,862 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@403364e9[nodeId=world0,portId=humanPort6]
2024-11-10 20:35:56,862 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@447521e, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:35:56,865 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.human.HumanPort@458031da[nodeId=world0,portId=humanPort7]
2024-11-10 20:35:56,864 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort9, costTime = 22
2024-11-10 20:35:56,866 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort8, costTime = 25
2024-11-10 20:35:56,866 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanService@7be94cd6, humanServer, class org.gof.demo.worldsrv.human.HumanService
2024-11-10 20:35:56,864 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort7, costTime = 25
2024-11-10 20:35:56,866 (GameServiceManager.java:28) [INFO][GAME] 开始初始化service
2024-11-10 20:35:56,867 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort4, costTime = 34
2024-11-10 20:35:56,868 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort6, costTime = 32
2024-11-10 20:35:56,870 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort3, costTime = 41
2024-11-10 20:35:56,870 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=accountPort5, costTime = 35
2024-11-10 20:35:56,871 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@3344d163[nodeId=world0,portId=game0]
2024-11-10 20:35:56,871 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=humanPort0, costTime = 27
2024-11-10 20:35:56,877 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:35:56,883 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=humanPort4, costTime = 24
2024-11-10 20:35:56,884 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=humanPort3, costTime = 26
2024-11-10 20:35:56,884 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=*********, end=*********, portId=humanPort1, costTime = 32
2024-11-10 20:35:56,884 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=139100001, end=139200000, portId=humanPort2, costTime = 28
2024-11-10 20:35:56,886 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=139500001, end=139600000, portId=humanPort6, costTime = 24
2024-11-10 20:35:56,887 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=139400001, end=139500000, portId=humanPort5, costTime = 27
2024-11-10 20:35:56,903 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=139700001, end=139800000, portId=game0, costTime = 31
2024-11-10 20:35:56,918 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=139600001, end=139700000, portId=humanPort7, costTime = 46
2024-11-10 20:36:05,123 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@43cf5bff[nodeId=world0,portId=game1]
2024-11-10 20:36:05,145 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.name.NameService@ddffec3, name, class org.gof.demo.worldsrv.name.NameService
2024-11-10 20:36:05,155 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.human.HumanGlobalService@19ed9a8, humanGlobal, class org.gof.demo.worldsrv.human.HumanGlobalService
2024-11-10 20:36:05,156 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.gm.GMGameService@63ff72ce, gm, class org.gof.demo.worldsrv.gm.GMGameService
2024-11-10 20:36:05,156 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.common.HumanCreateApplyService@2df4782a, humanCreateApply, class org.gof.demo.worldsrv.common.HumanCreateApplyService
2024-11-10 20:36:05,156 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.confirm.ConfirmGlobalService@e3997dc, confirm, class org.gof.demo.worldsrv.confirm.ConfirmGlobalService
2024-11-10 20:36:05,161 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=139800001, end=139900000, portId=game1, costTime = 37
2024-11-10 20:36:05,468 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@77f7352a[nodeId=world0,portId=game2]
2024-11-10 20:36:05,479 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.rank.RankService@67744a2a, rank, class org.gof.demo.worldsrv.rank.RankService
2024-11-10 20:36:05,483 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.inform.ChatService@288eb28f, chat, class org.gof.demo.worldsrv.inform.ChatService
2024-11-10 20:36:05,483 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.common.DataResetService@6d6e98ae, dataReset, class org.gof.demo.worldsrv.common.DataResetService
2024-11-10 20:36:05,484 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.mail.MailService@196cfbaf, mail, class org.gof.demo.worldsrv.mail.MailService
2024-11-10 20:36:05,484 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.httpPush.HttpPushService@15668f29, httpPush, class org.gof.demo.worldsrv.httpPush.HttpPushService
2024-11-10 20:36:05,492 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.activity.ActivityControlService@2be332d3, activity, class org.gof.demo.worldsrv.activity.ActivityControlService
2024-11-10 20:36:05,505 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=139900001, end=140000000, portId=game2, costTime = 36
2024-11-10 20:36:05,824 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@65a2755e[nodeId=world0,portId=game3]
2024-11-10 20:36:05,830 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.pocketLine.PocketLineService@bf8cd08, pocketLine, class org.gof.demo.worldsrv.pocketLine.PocketLineService
2024-11-10 20:36:05,834 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.arena.ArenaRankedService@53f2207, arenaRanked, class org.gof.demo.worldsrv.arena.ArenaRankedService
2024-11-10 20:36:05,849 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.worldBoss.WorldBossService@49bf5c9b, worldBoss, class org.gof.demo.worldsrv.worldBoss.WorldBossService
2024-11-10 20:36:05,857 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=140000001, end=140100000, portId=game3, costTime = 32
2024-11-10 20:36:06,198 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@2bf0c70d[nodeId=world0,portId=game4]
2024-11-10 20:36:06,213 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.team.TeamService@7ca3836d, team, class org.gof.demo.worldsrv.team.TeamService
2024-11-10 20:36:06,214 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.captureSlave.CaptureSlaveService@72647289, captureSlave, class org.gof.demo.worldsrv.captureSlave.CaptureSlaveService
2024-11-10 20:36:06,216 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.carPark.CarParkService@765be81d, carPark, class org.gof.demo.worldsrv.carPark.CarParkService
2024-11-10 20:36:06,218 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.mail.FillMailService@2a0ed9c, fillMail, class org.gof.demo.worldsrv.mail.FillMailService
2024-11-10 20:36:06,241 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=140100001, end=140200000, portId=game4, costTime = 41
2024-11-10 20:36:06,269 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:36:06,611 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@652f26da[nodeId=world0,portId=game5]
2024-11-10 20:36:06,622 (GuildLeagueWarmUpService.java:1079) [ERROR][TEMP] ===timeNow=1731242166614, interval=257033386，开始战斗时间2024-11-13 20:00:00
2024-11-10 20:36:06,623 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.guild.league.GuildLeagueWarmUpService@1c17739a, guildLeagueWarmUp, class org.gof.demo.worldsrv.guild.league.GuildLeagueWarmUpService
2024-11-10 20:36:06,623 (CheckWorldService.java:95) [INFO][GAME] 准备注册游戏服信息到中心服！serverId=30572，nodeId=worldBridge30572
2024-11-10 20:36:06,647 (CheckWorldService.java:100) [INFO][TEMP] ===代码未实现，检测的先不处理
2024-11-10 20:36:06,647 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.check.CheckWorldService@20338cc6, checkWorld, class org.gof.demo.worldsrv.check.CheckWorldService
2024-11-10 20:36:06,648 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=140200001, end=140300000, portId=game5, costTime = 37
2024-11-10 20:36:06,655 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.arena.ArenaService@2f192b65, arena, class org.gof.demo.worldsrv.arena.ArenaService
2024-11-10 20:36:06,923 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:36:06,923 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:36:06,955 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:36:06,955 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:36:07,007 (Port.java:182) [INFO][CORE] 启动Port=org.gof.demo.worldsrv.common.GamePort@3acc3ee[nodeId=world0,portId=game6]
2024-11-10 20:36:07,018 (GuildService.java:161) [INFO][TEMP] =================init GuildService
2024-11-10 20:36:07,019 (GuildService.java:251) [INFO][TEMP] ===gve开始时间=1731297900000，2024-11-11 12:05:00
2024-11-10 20:36:07,019 (GuildService.java:256) [INFO][TEMP] ===gve结束时间=1731297900000，2024-11-11 12:05:00
2024-11-10 20:36:07,048 (IdAllotPoolBase.java:134) [INFO][CORE] [ID分配]同步方式向服务端申请可分配ID范围：begin=140300001, end=140400000, portId=game6, costTime = 40
2024-11-10 20:36:07,084 (DBConnection.java:90) [INFO][CORE_DB] 创建新的数据库连接。
2024-11-10 20:36:07,142 (GuildService.java:323) [ERROR][TEMP] initCheckRank 0
2024-11-10 20:36:07,144 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.guild.GuildService@bf47a0e, guild, class org.gof.demo.worldsrv.guild.GuildService
2024-11-10 20:36:07,374 (Port.java:831) [INFO][CORE] addService = org.gof.demo.worldsrv.home.FarmService@da3be7a, farm, class org.gof.demo.worldsrv.home.FarmService
2024-11-10 20:36:07,376 (HumanManager.java:4386) [INFO][GAME] 游戏启动时
2024-11-10 20:36:07,377 (WorldStartup.java:333) [ERROR][GAME] ====================
2024-11-10 20:36:07,377 (WorldStartup.java:334) [ERROR][GAME] world0 started.
2024-11-10 20:36:07,377 (WorldStartup.java:335) [ERROR][GAME] Listen:tcp://************:20108
2024-11-10 20:36:07,377 (WorldStartup.java:336) [ERROR][GAME] ====================
2024-11-10 20:36:07,386 (HumanManager.java:4445) [INFO][TEMP] ===去后台请求屏蔽字库
2024-11-10 20:36:07,387 (HumanManager.java:4486) [INFO][TEMP] ===去后台请求跑马灯
2024-11-10 20:36:07,387 (HumanManager.java:4452) [INFO][TEMP] ===ip=************，addr=tcp://************:13000
2024-11-10 20:36:07,387 (HumanManager.java:4468) [ERROR][TEMP] ===通知admin注册ip地址url=http://************:8018/gameServer, jo={port=8018, ip=************, serverId=30572}
2024-11-10 20:36:07,444 (WorldStartup.java:365) [ERROR][GAME] 开启数据热更新扫描...
2024-11-10 20:36:07,488 (ClassScanProcess.java:58) [ERROR][GAME] 开启类热更新扫描调度，每5分钟执行一次
2024-11-10 20:36:07,489 (WorldStartup.java:369) [ERROR][GAME] 开启类热更新扫描...
2024-11-10 20:36:07,490 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@b3a8455[nodeId=world0,portId=connPort0]
2024-11-10 20:36:07,491 (Port.java:831) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@5c930fc3, conn, class org.gof.core.connsrv.ConnService
2024-11-10 20:36:07,492 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@2447940d[nodeId=world0,portId=connPort1]
2024-11-10 20:36:07,493 (Port.java:831) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@60ee7a51, conn, class org.gof.core.connsrv.ConnService
2024-11-10 20:36:07,494 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@1618c98a[nodeId=world0,portId=connPort2]
2024-11-10 20:36:07,495 (Port.java:831) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@5b715ea, conn, class org.gof.core.connsrv.ConnService
2024-11-10 20:36:07,496 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@18b45500[nodeId=world0,portId=connPort3]
2024-11-10 20:36:07,496 (Port.java:831) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@25110bb9, conn, class org.gof.core.connsrv.ConnService
2024-11-10 20:36:07,498 (Port.java:182) [INFO][CORE] 启动Port=org.gof.core.connsrv.ConnPort@7bef7505[nodeId=world0,portId=connPort4]
2024-11-10 20:36:07,498 (Port.java:831) [INFO][CORE] addService = org.gof.core.connsrv.ConnService@568ef502, conn, class org.gof.core.connsrv.ConnService
2024-11-10 20:36:07,499 (WorldStartup.java:381) [ERROR][GAME] 开启连接服务
2024-11-10 20:36:07,499 (WorldStartup.java:383) [ERROR][GAME] 启动完成...
2024-11-10 20:36:07,505 (ServCheck.java:68) [ERROR][GAME] 
╔═══════════════════╤══════════════╗
║ service           │ costTime(ms) ║
╠═══════════════════╪══════════════╣
║ guild             │ 126          ║
╟───────────────────┼──────────────╢
║ checkWorld        │ 23           ║
╟───────────────────┼──────────────╢
║ name              │ 16           ║
╟───────────────────┼──────────────╢
║ worldBoss         │ 12           ║
╟───────────────────┼──────────────╢
║ farm              │ 8            ║
╟───────────────────┼──────────────╢
║ activity          │ 6            ║
╟───────────────────┼──────────────╢
║ guildLeagueWarmUp │ 5            ║
╟───────────────────┼──────────────╢
║ arena             │ 4            ║
╟───────────────────┼──────────────╢
║ arenaRanked       │ 0            ║
╟───────────────────┼──────────────╢
║ mail              │ 0            ║
╟───────────────────┼──────────────╢
║ carPark           │ 0            ║
╟───────────────────┼──────────────╢
║ gm                │ 0            ║
╟───────────────────┼──────────────╢
║ team              │ 0            ║
╟───────────────────┼──────────────╢
║ humanCreateApply  │ 0            ║
╟───────────────────┼──────────────╢
║ httpPush          │ 0            ║
╟───────────────────┼──────────────╢
║ confirm           │ 0            ║
╟───────────────────┼──────────────╢
║ chat              │ 0            ║
╟───────────────────┼──────────────╢
║ pocketLine        │ 0            ║
╟───────────────────┼──────────────╢
║ humanGlobal       │ 0            ║
╟───────────────────┼──────────────╢
║ rank              │ 0            ║
╟───────────────────┼──────────────╢
║ dataReset         │ 0            ║
╟───────────────────┼──────────────╢
║ captureSlave      │ 0            ║
╟───────────────────┼──────────────╢
║ fillMail          │ 0            ║
╚═══════════════════╧══════════════╝

2024-11-10 20:36:09,736 (Port.java:307) [WARN][CORE_EFFECT] [httpSend0]本次心跳操作总时间较长，达到了2330毫秒。portName=httpSend0, callCount=3, resultCount=0, countQueue=0, time={call=2331}
2024-11-10 20:36:10,425 (Node.java:689) [INFO][CORE] ====humanPort status : active = 8 , dead= 0 , dead list:[]
2024-11-10 20:36:10,425 (Node.java:740) [INFO][CORE] ====accountProt status : active = 10 , dead= 0 , dead list:[]
2024-11-10 20:36:10,549 (WorldStartup.java:342) [INFO][GAME] 触发关闭服务器操作,开始踢人
2024-11-10 20:36:10,551 (HumanGlobalService.java:567) [INFO][GAME] ===已踢出所有玩家
2024-11-10 20:36:10,551 (WorldStartup.java:351) [INFO][GAME] 关闭服务器-检查db更新队列完成! 15s后服务器将关闭
2024-11-10 20:36:12,551 (DBPartService.java:384) [INFO][CORE_DB] [dbPart6]远程调用刷新数据库全部写缓存。
2024-11-10 20:36:12,551 (DBPartService.java:384) [INFO][CORE_DB] [dbPart1]远程调用刷新数据库全部写缓存。
2024-11-10 20:36:12,582 (DBPartService.java:384) [INFO][CORE_DB] [dbPart3]远程调用刷新数据库全部写缓存。
2024-11-10 20:36:12,582 (DBPartService.java:384) [INFO][CORE_DB] [dbPart0]远程调用刷新数据库全部写缓存。
2024-11-10 20:36:12,582 (DBPartService.java:384) [INFO][CORE_DB] [dbPart4]远程调用刷新数据库全部写缓存。
2024-11-10 20:36:12,582 (DBPartService.java:384) [INFO][CORE_DB] [dbPart7]远程调用刷新数据库全部写缓存。
2024-11-10 20:36:12,582 (DBPartService.java:384) [INFO][CORE_DB] [dbPart2]远程调用刷新数据库全部写缓存。
2024-11-10 20:36:12,582 (DBPartService.java:384) [INFO][CORE_DB] [dbPart5]远程调用刷新数据库全部写缓存。
2024-11-10 20:36:17,490 (ClassScanProcess.java:135) [ERROR][GAME] 开始一次扫描key=216594811709003132515,jarValue=216594811709003132515
