package org.gof.demo.seam.main;

import com.pwrd.op.LogOp;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.logging.log4j.LogManager;
import org.gof.core.Node;
import org.gof.core.Port;
import org.gof.core.PortPulseQueue;
import org.gof.core.dbsrv.main.DBStartup;
import org.gof.core.dbsrv.redis.AwaitUtil;
import org.gof.core.dbsrv.redis.DBMainVerticle;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.statistics.StatisticsService;
import org.gof.core.support.*;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.observer.MsgSender;
import org.gof.demo.CommonSerializer;
import org.gof.demo.ListenerInit;
import org.gof.demo.MsgReceiverInit;
import org.gof.demo.MsgSerializer;
import org.gof.demo.seam.DefaultPort;
import org.gof.demo.support.ClassScanProcess;
import org.gof.demo.support.DataReloadManager;
import org.gof.demo.support.DataScanProcess;

import org.gof.demo.worldsrv.common.GameService;
import org.gof.demo.worldsrv.common.ServerListService;
import org.gof.demo.worldsrv.intergration.PFService;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.observer.Event;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.platform.main.PlatformStartup;

public class CrossStart {
    public static void main(String[] args) throws Exception {

        S.isCross = true;
        System.setProperty("logFileName", "cross0");
        System.setProperty("vertx.logger-delegate-factory-class-name", "io.vertx.core.logging.Log4j2LogDelegateFactory");
        Log.game.info("正在启动游戏服务器");

        //初始化commonUID
        CommonUid.initServerId(Integer.parseInt(Config.GAME_SERVER_ID));

        //初始化基本环境
        Log.game.info("正在初始化事件容器");
        MsgReceiverInit.init(MsgSender.instance);
        ListenerInit.init(Event.instance);
        Log.game.info("正在初始化协议函数指针池");
        MsgSerializer.init();
        CommonSerializer.init();

        //强制加载全部数据，这样可以避免业务执行过程中加载。
        Log.game.info("加载策划数据");
        DataReloadManager.inst().initAllData();
        AwaitUtil.awaitResult(handler->{
            startVertxRedis(res->{
                if(res.failed()){
                    Log.game.error("启动redis服务失败！！！", res.cause());
                    System.exit(1);
                }
                handler.handle(Future.succeededFuture());
            });
        });
        try {
            startServices();
        }catch (Throwable ex){
            Log.game.error("启动服务器失败！！！", ex);
            System.exit(1);
        }
    }

    //启动redis服务
    private static void startVertxRedis(Handler<AsyncResult<Boolean>> handler){
        if(S.isRedis){
            // 启动vertx服务
            Vertx vertx = Vertx.vertx();
            Future<String> stringFuture = vertx.deployVerticle(new DBMainVerticle());
            stringFuture.onFailure(cause->handler.handle(Future.failedFuture(cause)));
            stringFuture.onSuccess(result->handler.handle(Future.succeededFuture(true)));
        }
    }

    //启动其他服务
    private static void startServices() throws Exception {
        //创建Node
        String nodeId = Distr.NODE_ID;
        String nodeAddr = Distr.getNodeAddr(nodeId);
        Log.game.info("前置准备完成，开始启动Node, nodeId={}, nodeAddr={}", nodeId, nodeAddr);
        Node node = new Node(nodeId, nodeAddr, NodeType.CROSS.getCode());

        Log.game.info("server init : begin start node...");
        DBStartup.startup(node);

        //1.4 平台服务器
        Log.game.info("server init : begin start platform...");
        PlatformStartup.startup(node);

        //启动远程数据日志
        String logPath = Utils.class.getClassLoader().getResource("operlog.properties").getPath();
        LogOp.init(logPath);

        /* 2 加载系统数据 */
        //2.1 创建个临时Port
        DefaultPort portDef = new DefaultPort(Distr.PORT_DEFAULT);
        portDef.startup(node);
        Log.game.info("server init : default port started...");
        //平台服务
        PFService pfService = new PFService(portDef);
        pfService.startup();
        portDef.addService(pfService);

        //gameService
        GameService gameService = new GameService(portDef);
        gameService.startup();
        portDef.addService(gameService);
        portDef.addQueue(new PortPulseQueue() {
            @Override
            public void execute(Port port) {
                gameService.init();
            }
        });

        //拉取服务器列表
        ServerListService slService = new ServerListService(portDef);
        slService.startup();
        portDef.addService(slService);

        /* 3 启动系统默认服务 */
        //只在默认Node上启动
        Log.game.info("server nodeID={}, defaultNodeId={}",nodeId, Distr.NODE_DEFAULT);
        if(Config.STATISTICS_ENABLE) {
            StatisticsService statisticsService = new StatisticsService(portDef);
            statisticsService.startup();
            portDef.addService(statisticsService);
        }

        //发布服务器初始化开始事件
        Event.fire(EventKey.GAME_STARTUP_BEFORE, "node", node);

        //Node正式启动
        node.startup();

        while(!ServCheck.isAllStarted()) {
            try {
                Thread.sleep(10);
                //发布服务器初始化结束事件
            } catch (InterruptedException e) {
                LogCore.core.error(ExceptionUtils.getStackTrace(e));
            }
        }

        Log.game.info("server init : begin connect admin...");
        // 连接管理服（中心服admin）
        String adminNodeId = D.NODE_ADMIN_PREFIX + 0;
        //连接远程
        node.addRemoteNode(adminNodeId, NodeType.ADMIN.getCode());
        Log.temp.error("===连接中心管理服， nid={}, localBridgeId={}， {}", adminNodeId , nodeId, node.getRemoteNode(adminNodeId));

        Event.fire(EventKey.GAME_STARTUP_FINISH, "node", node);

        //启动日志信息
        Log.game.error("====================");
        Log.game.error(nodeId + " started.");
        Log.game.error("Listen:" + node.getAddr());
        Log.game.error("====================");

        //系统关闭时进行清理
        Runtime.getRuntime().addShutdownHook(new Thread() {
            public void run() {
                try {
                    S.isClose = true;
                    Port port = node.getPort("game0");
                    //通知游戏服和跨服，中心服暂时停止工作
                    EntityManager.redisClient.close();
                    LogManager.shutdown();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });

        //设定support外围环境并加载
        if(Config.RELOAD_CONF_DATA){
            DataReloadManager.inst().initReloadSupport();
            DataScanProcess.getInstance().init();
            Log.game.error("开启数据热更新扫描...");
        }
        if(Config.RELOAD_CLASS){
            ClassScanProcess.getInstance().init();
            Log.game.error("开启类热更新扫描...");
        }

        Log.game.error("启动Cross完成...");
        ServCheck.printServTimeResult();
        S.gameStartupFinish = true;

    }
}
