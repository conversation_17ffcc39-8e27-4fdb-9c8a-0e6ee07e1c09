package org.gof.demo.seam.main;

import java.io.BufferedReader;
import java.io.InputStreamReader;

public class MyStartup {

    public static void main(String[] args) throws Exception {
        startRedis();
        WorldStartup.main(args);
    }


    public static void startRedis() {
        if (isRedisStart()) {
            return;
        }

        try {
            Runtime.getRuntime().exec("cmd /c start D:\\DataBases\\Redis-5.0.14.1\\redis-server.exe D:\\DataBases\\Redis-5.0.14.1\\redis.project.kumo.dev.conf");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static boolean isRedisStart() {
        boolean isStart = false;
        try {
            Process process = Runtime.getRuntime().exec("tasklist");
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.contains("redis")) {
                    isStart = true;
                    break;
                }
            }
            reader.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return isStart;
    }
}
