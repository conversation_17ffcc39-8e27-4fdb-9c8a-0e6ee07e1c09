package org.gof.demo.seam.account;

import org.gof.core.CallPoint;
import org.gof.core.Chunk;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.connsrv.ConnectionProxy;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.support.*;
import org.gof.demo.seam.msg.AccountExtendMsgHandler;
import org.gof.demo.worldsrv.msg.MsgIds;
import org.gof.demo.worldsrv.msg.MsgLogin;
import org.gof.demo.worldsrv.support.Log;

import java.util.*;

@DistrClass(
	importClass = {ConnectionStatus.class}
)
public class AccountService extends Service {
	//消息处理类
	private AccountExtendMsgHandler msgHandler = MsgHandler.getInstance(AccountExtendMsgHandler.class);
	
	//存储选人阶段的信息：key:connId
	private Map<Long, AccountObject> datas = new HashMap<>();
	private Map<String, AccountObject> name2Accounts = new HashMap<>();
	
	//申请进入列表
	public List<Long> loginApply = new LinkedList<>();
	
	//最大在线人数(在线的+选角色界面的玩家)
	private int loginMaxOnline;
	
	//选角色界面的玩家数量(在Connection修改玩家状态的时候维护)
	private int loginGateNum;
		
	//服务器人数是否已满
	public boolean humanOnlineFull = false;
	
	//服务器是否已满  <渠道Id,是否已满>
	public Map<String, Boolean> serverFullMap = new HashMap<String, Boolean>(); 
	
	//服务器是否可以进行注册
	public boolean isServerFull = false;
	
	//本秒允许玩家登陆数量
	public int loginedNumPerSec = 0;
	
	//本次提示玩家间隔期允许登陆玩家数量（仅作显示记录，不参与任何逻辑）
	public int loginedNumPerTips = 0;

	public static String nodeId;
	public static String portId;
	
	/**
	 * 清理已经不存在的请求，清理操作时间间隔
	 */
	private TickTimer loginClearTimer =  new TickTimer(1 * Time.SEC);
	
	/**
	 * 给登陆队列中角色的提示间隔(秒)
	 */
	private TickTimer loginTipsTimer = new TickTimer(1 * Time.SEC);
	
	/**
	 * 每批次登录的时间间隔
	 */
	private TickTimer loginTimer = new TickTimer(1 * Time.SEC);

	/**
	 * 每分钟打印一下登陆队列信息
	 */
	private TickTimer infoTimer = new TickTimer(1 * Time.MIN);

	/**
	 * 构造函数
	 * @param port
	 */
	public AccountService(Port port) {
		super(port);
		loginMaxOnline = Config.LOGIN_MAX_ONLINE;
		AccountService.nodeId = port.getNodeId();
		AccountService.portId = port.getId();
	}



	@DistrMethod
	public void msgHandler(long connId, ConnectionStatus status, byte[] msgbuf) {
		CallPoint connPoint = new CallPoint();
		connPoint.nodeId = port.getCallFromNodeId();
		connPoint.portId = port.getCallFromPortId();
		connPoint.servId = connId;
		
		msgHandler.handle(msgbuf, "connPoint", connPoint, "serv", this, "connId", connId, "connStatus", status);
	}
	
	@DistrMethod
	public void connClosed(long connId) {
		Log.temp.error("====链接关闭 connId={}", connId);
		datas.remove(connId);
		checkGateNum(ConnectionStatus.STATUS_LOSTED);
	}
	
	@DistrMethod
	public void connCheck(long connId) {
//		if(!datas.containsKey(connId)){
//			Log.temp.error("====链接不存在，可能被清了， connId={}, datas={}", connId, datas);
//		}
		port.returns(datas.containsKey(connId));
	}
	
	/**
	 * 同步服务器玩家数已满状态
	 * @param onlineNum
	 */
	@DistrMethod
	public void setHumanOnlineFull(int onlineNum) {
		int totalNum = onlineNum + loginGateNum;
		//Log.temp.info("总人数totalNum={}",totalNum);
		this.humanOnlineFull = totalNum >= loginMaxOnline;
	}

	@Override
	public Object getId() {
		return Distr.SERV_GATE;
	}
	
	@Override
	public void pulseOverride() {
		super.pulseOverride();
		
		//当前时间
		long now = Port.getTime();
		
		//检查需要开始登陆加载的角色
		if(loginTimer.isPeriod(now)) {
			loginedNumPerSec = 0;
			loginQueue();
		}

		//对登陆队列中的玩家进行提示，临时移除
		plusCharLoginQueueTips(now);
		//登陆队列数据维护清理
		plusCharLoginClear(now);
	}

	public Port getPort() {
		return port;
	}
	
	/**
	 * 创建代理类
	 * @return
	 */
	public static AccountServiceProxy createProxy() {
		return AccountServiceProxy.newInstance(nodeId,  portId, Distr.SERV_GATE);
	}
	
	//----登录排队-开始----
	/**
	 * 增加登陆申请
	 * @param accountObj
	 */
	@DistrMethod
	public void applyLogin(AccountObject accountObj){
		//如果之前在队列中，移除之前的登录
		checkAccountIsLoginNow(accountObj.status.account);
		//队列满了，直接返回失败
		if(loginApply.size()>Config.LOGIN_MAX_QUEUE){
			AccountManager.inst().loginFailed(this,accountObj,9);
			return;
		}
		Long connId = accountObj.id;
		datas.put(accountObj.id,accountObj);
		name2Accounts.put(accountObj.status.account, accountObj);
		loginApplyAdd(connId);
	}

	public void loginApplyAdd(Long connId) {
		//加入登录队列
		loginApply.add(connId);
		//Log.temp.info("connId={}",connId);
		//立即触发一次登录检查
		loginQueue();
		
		//立即登录没有成功，提示玩家等待
		if(loginApply.contains(connId)) {
			//当前时间
			long now = port.getTimeCurrent();
			plusCharLoginQueueTipsOne(now, connId, loginApply.size());
		}
	}
	
	/**
	 * 检查需要开始登陆加载的角色
	 */
	private void loginQueue() {
		//每秒登录人数检测
		int secMaxNum = Config.LOGIN_SECOND_NUM;
		while(!loginApply.isEmpty() && loginedNumPerSec < secMaxNum) {
			Log.game.info("排队人数={}", loginApply.size());
			//维护登陆队列状态
			long connId = loginApply.remove(0);
			
			AccountObject obj = datas.get(connId);
			if(obj == null) {
				Log.game.info("++++++++debug 排队发现accountObj is null");
				continue;
			}
//			if(obj.eCheckType != null){
//				// 自动创角/登录玩家
////				AccountManager.inst().autoLogin(obj);
//				AccountLoginServiceProxy loginServProxy = AccountLoginService.createProxy(obj.status.account);
//				loginServProxy.loginContinue(obj);
//				continue;
//			}
			//获取连接代理
			ConnectionProxy prx = ConnectionProxy.newInstance(obj.connPoint);
			//服务器达到最大在线人数, 不再继续登陆新玩家
			if(humanOnlineFull){
//				Random random = new Random();
//				int x = random.nextInt(2);//1:Male,2:Female
//				int serverDataSn = (x == 1) ? 91104 : 91105;
//				AccountManager.inst().sendMsg_role_login_s2c(obj.connPoint, serverDataSn);
				Log.game.info("服务器到达到达最大在线人数，正在登录人数 {}", loginedNumPerSec);
				//TODO:需要增加消息码，提示服务器在线人数已达上限
				AccountManager.inst().loginFailed(this,obj,9);
				return;
			}
			//记录本次允许登陆人数
			loginedNumPerSec++;
			loginedNumPerTips++;

			//更新链接状态
			obj.status.status = ConnectionStatus.STATUS_GATE;
			prx.updateStatus(obj.status);
			checkGateNum(obj.status.status);

//			if(Config.KEY_ACTIVATE){
//				KeyActivateServiceProxy prox = KeyActivateServiceProxy.newInstance();
//				prox.isActivate(obj.status.account);
//				prox.listenResult(this::_result_isActivate, "obj", obj, "reply", reply);
//			}else{
//				//发送登录请求结果
//				AccountManager.inst().sendMsg_role_login_s2c(obj.connPoint, 0);
//			}

			// 自动创角登录玩家
			AccountLoginServiceProxy loginServProxy = AccountLoginService.createProxy(obj.status.account);
			loginServProxy.loginContinue(obj);
//			AccountManager.inst().autoLogin(obj);
		}
	}


	/**
	 * 查看玩家是否激活
	 * @param results
	 * @param context
	 */
	public void _result_isActivate(Param results, Param context){
		AccountObject obj = context.get("obj");
		boolean result = results.get("result");
		//发送登录请求结果
		AccountManager.inst().sendMsg_role_login_s2c(obj.connPoint, result ? 0 : -1);

		if(result){
			// 自动登录玩家
			AccountManager.inst().autoLogin(obj);
		}
	}
	
	/**
	 * 登陆队列数据维护清理
	 * @param now
	 */
	private void plusCharLoginClear(long now) {
		if(!loginClearTimer.isPeriod(now)) return;
		loginClearTimer.start(Config.LOGIN_INTERVAL_CLEAR * Time.SEC);
		
		//清理已经不存在的请求
		for(Iterator<Long> iter = loginApply.iterator(); iter.hasNext();) {
			Long connId = iter.next();
			if(!datas.containsKey(connId)) {
				iter.remove();
			}
		}
	}
	
	/**
	 * 对登陆队列中的玩家进行提示
	 */
	private void plusCharLoginQueueTips(long now) {
		if(!loginTipsTimer.isPeriod(now)) return;
		loginTipsTimer.start(Config.LOGIN_INTERVAL_TIPS * Time.SEC);
		
		//每隔ConfParamKey.LOGIN_INTERVAL_TIPS秒，提示排队队列中的所有玩家，现在的排队状况
		for(int i = 0; i < loginApply.size(); i++) {
			Long connId = loginApply.get(i);
			//排队人数
			int num = i + 1;

			//提示队列中的玩家
			plusCharLoginQueueTipsOne(now, connId, num);
		}

		//如果有排队 那么输出排队信息
		if(!loginApply.isEmpty()) {
			Log.game.info("当前排队中：申请登陆人数={}，正在登陆人数={}", loginApply.size(), loginedNumPerTips);
			loginedNumPerTips = 0;
		}
	}
	/**
	 * 对登陆队列中的玩家进行提示(有多少人在排队，需要等多长时间)
	 */
	private void plusCharLoginQueueTipsOne(long now, Long connId, int num) {
		//登陆信息
		AccountObject obj = datas.get(connId);
		if(obj == null) return;

		//剩余时间
		int sec;
		if(humanOnlineFull) {
			sec = (num + 1) * Config.LOGIN_TIME_FULL;
		} else {
			sec = num / Config.LOGIN_MAX_QUEUE + 1;
		}

		//提示客户端: 前面还有多少人
//		ConnectionProxy prxConn = ConnectionProxy.newInstance(obj.connPoint);
//		MsgLogin.role_login_queue_s2c msg = MsgLogin.role_login_queue_s2c.newBuilder().setSec(sec).setFull(humanOnlineFull).setNum(num).build();
//		prxConn.sendMsg(MsgIds.role_login_queue_s2c,new Chunk(msg));
//		Log.game.info("role_login_queue_s2c-{} 已发送, msg={}",obj.status.account, msg);
	}
	
	//----登录排队-结束----
	
	/**
	 * 同步更新选角色界面的玩家数量
	 * @param status
	 */
	@DistrMethod
	public void checkGateNum(int status){
		if(status == ConnectionStatus.STATUS_GATE){
			loginGateNum++;
		}else{
			loginGateNum--;
			if(loginGateNum < 0){
				loginGateNum = 0;
			}
		}
	}
	
	/**
	 * 修改最大在线人数
	 * @param maxOnline
	 */
	@DistrMethod
	public void setLoginMaxOnline(int maxOnline){
		loginMaxOnline = maxOnline;
	}

	public void addAccount(AccountObject obj) {
		datas.put(obj.getId(), obj);
		name2Accounts.put(obj.status.account, obj);
	}

	@DistrMethod
	public void removeAccount(AccountObject obj) {
		AccountObject accountObj = name2Accounts.remove(obj.status.account);
		if(accountObj!=null){
			datas.remove(accountObj.getId());
		}
	}

	@DistrMethod
	public void finishLogin(String account){
		AccountObject accountObj = name2Accounts.remove(account);
		if(accountObj!=null){
			datas.remove(accountObj.getId());
		}
	}

	public AccountObject findAccount(String account) {
		return name2Accounts.get(account);
	}

	public AccountObject findAccountById(long id) {
		return datas.get(id);
	}

	@DistrMethod
	public void reCreateAccount(String account, CallPoint cp) {
		ConnectionStatus connStatus = new ConnectionStatus();
		connStatus.status = ConnectionStatus.STATUS_GATE;
		connStatus.account = account;
		AccountObject obj = new AccountObject((long)cp.servId, connStatus, cp);
		datas.put((long)cp.servId, obj);
	}

	@DistrMethod
	public void backMethod1(String param) {

	}

	@DistrMethod
	public void backMethod2(String param) {

	}

	@DistrMethod
	public void backupMethod3(String param) {

	}

	@DistrMethod
	public void backupMethod4(String param) {

	}

	private void checkAccountIsLoginNow(String account) {
		AccountObject accOld = name2Accounts.get(account);
		if (accOld != null && accOld.eCheckType == null) {
			ConnectionProxy connPrx = ConnectionProxy.newInstance(accOld.connPoint);
			Log.game.info("loginTrace[{}] login_step 600, 已有登录账号{} 旧的链接关闭，踢人下线", accOld.connPoint.servId, account);
			MsgLogin.logout_s2c.Builder logout = MsgLogin.logout_s2c.newBuilder();
			logout.setCode(20);//顶号踢下线
			connPrx.sendMsg(MsgIds.logout_s2c, new Chunk(logout));
			removeAccount(accOld);
			//断开另一个玩家的连接
//			connPrx.close(); 不断链接，客户端会断
		}
	}

}
