package org.gof.demo.seam.account;


import org.gof.core.CallPoint;
import org.gof.core.Service;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.support.*;
import org.gof.demo.seam.msg.MsgParamAccount;
import org.gof.demo.worldsrv.msg.MsgLogin;
import org.gof.demo.worldsrv.support.D;
import org.gof.demo.worldsrv.support.Log;

@DistrClass(importClass = { Param.class })
public class AccountLoginService extends Service {

	public static Object SERV_ID = D.SERV_ACCOUNT_LOGIN_SERVER;
	private static String nodeId;
	private static String portId;


	public TickTimer tickTimer = new TickTimer(5 * Time.MIN);

	public AccountLoginService(AccountPort port, String nodeId) {
		super(port);
		this.nodeId = nodeId;
		this.portId = port.getId();
	}

	@Override
	public Object getId() {
		return D.SERV_ACCOUNT_LOGIN_SERVER;
	}

	@Override
	public void pulseOverride() {

	}

	/**
	 * 创建代理类
	 * 
	 * @return
	 */
	public static AccountLoginServiceProxy createProxy(String account) {
		int hash = Math.abs(Utils.hash(account.hashCode()));
		String portIdNow = D.PORT_ACCOUNT + hash % D.PORT_STARTUP_NUM_ACCOUNT_LOGIN;
		return AccountLoginServiceProxy.newInstance(nodeId, portIdNow, D.SERV_ACCOUNT_LOGIN_SERVER);
	}

	@DistrMethod
	public void login(String account, Param param) {
		CallPoint connPoint = Utils.getParamValue(param, "connPoint", null);
		MsgLogin.role_login_c2s msg = Utils.getParamValue(param, "msg", null);
		ConnectionStatus connStatus =  Utils.getParamValue(param, "connStatus", null);
		if(connPoint == null || msg == null || connStatus == null){
			Log.human.error("===登录数据不正确，无法登录，connPoint:{}, msg:{}, connStatus:{}====", connPoint, msg, connStatus);
			return;
		}
		AccountManager.inst().login(connPoint, connStatus, msg);
	}

	@DistrMethod(argsImmutable = true)
	public void accountReconnect(String account, Param param) {
//		long humanId, MsgParamAccount msgParam；
//		MsgLogin.role_reconnect_c2s msg = Utils.getParamValue(param, "msgParam", null);
//		long humanId = Utils.getParamValue(param, "humanId", null);
//		if(msgParam == null || humanId == 0){
//			Log.human.error("===重连数据不正确，无法重连，connPoint:{}, msg:{}, connStatus:{}====", msg, humanId);
//			return;
//		}
//		AccountManager.inst().onCSAccountReconnect(humanId, msg);
	}

	/**
	 * 登录排队完成，继续后续登录流程
	 * @param accountObj
	 */
	@DistrMethod
	public void loginContinue(AccountObject accountObj){
		AccountManager.inst().loginContinue(accountObj);
	}

	/**
	 * 每个service预留空方法
	 * 
	 * @param objs
	 */
	@DistrMethod
	public void update(Object... objs) {

	}

	/**
	 * 通知删除玩家服务
	 */
	@DistrMethod
	public void delService(long humanId) {
		// 删除玩家服务
//		port.delServiceBySafe(humanId);
	}

	@DistrMethod
	public void update1(String json) {

	}

}
