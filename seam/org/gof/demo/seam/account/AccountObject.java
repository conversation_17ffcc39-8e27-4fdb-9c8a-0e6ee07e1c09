package org.gof.demo.seam.account;

import org.gof.core.CallPoint;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.ConnectionStatus;
import org.gof.demo.worldsrv.check.ECheckTypeKey;
import org.gof.demo.worldsrv.msg.Define;

import java.io.IOException;

public class AccountObject implements ISerilizable  {
	public ConnectionStatus status;			//当前连接状态
//	public AccountService serv;				//当前所属线程
	public long id;							//主键ID等于连接ID
	public long humanId;					//玩家ID
	public CallPoint connPoint;				//连接点
	public ECheckTypeKey eCheckType;		//检测类型, 非空检测机器人
	public String pid = "";					//pid
	public int eLanguage;		//语言
	public int serverId;					//服务器id
	public int zone;						//服务器区
	public String regional;						//货币区域


	public AccountObject() {

	}
	public AccountObject(long id, ConnectionStatus status, CallPoint connPoint) {
		this.id = id;
//		this.serv = serv;
		this.status = status; 
		this.connPoint = connPoint;
	}
	
	public long getId() {
		return id;
	}
	
	@Override
	public void writeTo(OutputStream out) throws IOException {
		out.write(status);
		out.write(id);
		out.write(humanId);
		out.write(connPoint);
		out.write(pid);
		out.write(eCheckType);
		out.write(eLanguage);
		out.write(serverId);
		out.write(zone);
		out.write(regional);
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		status =  in.read();
		id = in.read();
		humanId = in.read();
		connPoint = in.read();
		pid = in.read();
		eCheckType = in.read();
		eLanguage = in.read();
		serverId = in.read();
		zone = in.read();
		regional = in.read();
	}
}