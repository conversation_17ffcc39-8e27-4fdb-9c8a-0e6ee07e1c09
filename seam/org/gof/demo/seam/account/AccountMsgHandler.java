package org.gof.demo.seam.account;

import com.google.protobuf.GeneratedMessageV3;
import org.gof.core.CallPoint;
import org.gof.core.support.ConnectionStatus;
import org.gof.core.support.S;
import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParamAccount;
import org.gof.demo.worldsrv.msg.MsgLogin;
import org.gof.demo.worldsrv.support.Log;

import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Set;

public class AccountMsgHandler {
	
	//下属监听消息
	private static final Set<Class<? extends GeneratedMessageV3>> methods = new HashSet<>();

	static {
		//寻找本类监听的消息
		Method[] mths = AccountMsgHandler.class.getMethods();
		for(Method m : mths) {
			//不是监听函数的忽略
			if(!m.isAnnotationPresent(MsgReceiver.class)) {
				continue;
			}
			
			//记录
			MsgReceiver ann = m.getAnnotation(MsgReceiver.class);
			methods.add(ann.value()[0]);
		}
	}
	
	public static boolean methodFilter(Class<? extends GeneratedMessageV3> clazz) {
		return methods.contains(clazz);
	}



	/**
	 * 玩家登陆请求
	 * @param param
	 */
	@MsgReceiver(MsgLogin.login_auth_c2s.class)
	public void _msg_login_auth_c2s(MsgParamAccount param) {
		MsgLogin.login_auth_c2s msg = param.getMsg();
		CallPoint connPoint = param.getConnPoint();
		ConnectionStatus connStatus = param.getConnStatus();
//		AccountManager.inst()._msg_login_auth_c2s(connPoint, connStatus, msg);
	}


	/**
	 * 玩家登陆请求
	 * @param param
	 */
	@MsgReceiver(MsgLogin.role_login_c2s.class)
	public void _msg_role_login_c2s(MsgParamAccount param) {
		MsgLogin.role_login_c2s msg = param.getMsg();
		CallPoint connPoint = param.getConnPoint();
		ConnectionStatus connStatus = param.getConnStatus();
		if(S.isClose){
			return;
		}
		//登录
		AccountManager.inst().accountLogin(connPoint, connStatus, msg);
	}


	/**
	 * 断线重连
	 * @param param
	 */
	@MsgReceiver(MsgLogin.role_reconnect_c2s.class)
	public void onCSAccountReconnect(MsgParamAccount param) {
		MsgLogin.role_reconnect_c2s msg = param.getMsg();

		if(S.isClose){
			return;
		}
		AccountManager.inst().onCSAccountReconnect(msg.getRoleId(), param);
	}
	
}
