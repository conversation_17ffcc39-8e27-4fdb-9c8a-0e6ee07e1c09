package org.gof.demo.worldsrv.config;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.StringUtils;
import org.gof.core.support.ConfigJSON;
import org.gof.core.support.IReloadSupport;
import org.gof.core.support.OrderBy;
import org.gof.core.support.OrderByField;
import org.gof.core.support.SysException;
import org.gof.core.support.Utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.demo.worldsrv.support.Log;

/**
 * BreakGoldEggLayers_砸金蛋数量 
 * BreakGoldEggLayers_砸金蛋数量.xlsx
 * <AUTHOR>
 * 此类是系统自动生成类 不要直接修改，修改后也会被覆盖
 */
@ConfigJSON
public class ConfBreakGoldEggLayers {
	public final int sn;			//层数id
	public final int layers;			//层数
	public final int type;			//类型
	public final int group_id;			//组id
	public final int num;			//金蛋数量
	

	public ConfBreakGoldEggLayers(int sn, int layers, int type, int group_id, int num) {
		this.sn = sn;
		this.layers = layers;
		this.type = type;
		this.group_id = group_id;
		this.num = num;
	}

	private static IReloadSupport support = null;
	
	public static void initReloadSupport(IReloadSupport s){
		support = s;
	}
	
	public static void reLoad() {
		if(support != null)
			support.beforeReload();
		DATA._init();
		
		if(support != null)
			support.afterReload();
	}
	
	/**
	 * 获取全部数据
	 * @return
	 */
	public static Collection<ConfBreakGoldEggLayers> findAll() {
		return DATA.getList();
	}

	/**
	 * 通过SN获取数据
	 * @param sn
	 * @return
	 */
	public static ConfBreakGoldEggLayers get(Integer sn) {
		return DATA.getMap().get(sn);
	}
	
	/**
	 * 通过多个组件组合成sn获取数据
	 * @param obj 多组件按顺序
	 * @return ConfBreakGoldEggLayers 对象
	 */

	
	/**
	 * 通过属性获取单条数据
	 * @param params
	 * @return
	 */
	public static ConfBreakGoldEggLayers getBy(Object...params) {
		List<ConfBreakGoldEggLayers> list = utilBase(params);
		
		if(list.isEmpty()) return null;
		else return list.get(0);
	}
	
	/**
	 * 通过属性获取数据集合
	 * @param params
	 * @return
	 */
	public static List<ConfBreakGoldEggLayers> findBy(Object...params) {
		return utilBase(params);
	}
	
	/**
	 * 通过属性获取数据集合 支持排序
	 * @param params
	 * @return
	 */
	public static List<ConfBreakGoldEggLayers> utilBase(Object...params) {
		List<Object> settings = Utils.ofList(params);
		
		//查询参数
		final Map<String, Object> paramsFilter = new LinkedHashMap<>();		//过滤条件
		final List<OrderByField> paramsOrder = new ArrayList<>();		//排序规则
				
		//参数数量
		int len = settings.size();
		
		//参数必须成对出现
		if(len % 2 != 0) {
			throw new SysException("查询参数必须成对出现:query={}", settings);
		}
		
		//处理成对参数
		for(int i = 0; i < len; i += 2) {
			String key = (String)settings.get(i);
			Object val = settings.get(i + 1);
			
			//参数 排序规则
			if(val instanceof OrderBy) {
				paramsOrder.add(new OrderByField(key, (OrderBy) val));
			} else {	//参数 过滤条件
				paramsFilter.put(key, val);
			}
		}
		
		//返回结果
		List<ConfBreakGoldEggLayers> result = new ArrayList<>();
		
		try {
			//通过条件获取结果
			for(ConfBreakGoldEggLayers c : DATA.getList()) {
				//本行数据是否符合过滤条件
				boolean bingo = true;
				
				//判断过滤条件
				for(Entry<String, Object> p : paramsFilter.entrySet()) {
					//实际结果
					Object valTrue = c.getFieldValue(p.getKey());
					//期望结果
					Object valWish = p.getValue();
					
					//有不符合过滤条件的
					if(!valWish.equals(valTrue)) {
						bingo = false;
						break;
					}
				}
				
				//记录符合结果
				if(bingo) {
					result.add(c);
				}
			}
		} catch (Exception e) {
			throw new SysException(e);
		}
		
		//对结果进行排序
		Collections.sort(result, (a, b) -> a.compareTo(b, paramsOrder));
		
		return result;
	}

	/**
	 * 属性关键字
	 */
	public static final class K {
		public static final String sn = "sn";	//层数id
		public static final String layers = "layers";	//层数
		public static final String type = "type";	//类型
		public static final String group_id = "group_id";	//组id
		public static final String num = "num";	//金蛋数量
	}
	
	/**
	 *
	 * 取得属性值
	 * @param classInstance 实例
	 * @key 属性名称
	 *
	 */
	@SuppressWarnings("unchecked")
	public <T> T getFieldValue(String key) {
		Object value = null;
		switch (key) {
			case "sn": {
				value = this.sn;
				break;
			}
			case "layers": {
				value = this.layers;
				break;
			}
			case "type": {
				value = this.type;
				break;
			}
			case "group_id": {
				value = this.group_id;
				break;
			}
			case "num": {
				value = this.num;
				break;
			}
			default: break;
		}
		
		return (T) value;
	}

	/**
	 * 数据集
	 * 单独提出来也是为了做数据延迟初始化
	 * 避免启动遍历类时，触发了static静态块
	 */
	@SuppressWarnings({"unused"})
	private static final class DATA {
		//全部数据
		private static Map<Integer, ConfBreakGoldEggLayers> _map;
		
		/**
		 * 获取数据的值集合
		 * @return
		 */
		public static Collection<ConfBreakGoldEggLayers> getList() {
			return getMap().values();
		}
		
		/**
		 * 获取Map类型数据集合
		 * @return
		 */
		public static Map<Integer, ConfBreakGoldEggLayers> getMap() {
			//延迟初始化
			if(_map == null) {
				synchronized (DATA.class) {
					if(_map == null) {
						_init();
					}
				}
			}
			
			return _map;
		}


		/**
		 * 初始化数据
		 */
		private static void _init() {
			Map<Integer, ConfBreakGoldEggLayers> dataMap = new java.util.concurrent.ConcurrentHashMap<>();
			
			//JSON数据
			String confJSON = _readConfFile();
			if(StringUtils.isBlank(confJSON)) return;
			
			//填充实体数据
			JSONArray confs = (JSONArray)JSONArray.parse(confJSON);
			
			JSONObject confTemp = confs.getJSONObject(0);
			if(!confTemp.containsKey("sn")){
				Log.temp.info("表{}有问题!", "ConfBreakGoldEggLayers");
			}
			for(int i = 0 ; i < confs.size() ; i++){
				JSONObject conf = confs.getJSONObject(i);
				
				/*if(conf.get("sn") instanceof String && conf.get("sn") == null){
					continue;
				}*/
				ConfBreakGoldEggLayers object = new ConfBreakGoldEggLayers(conf.getIntValue("sn"), conf.getIntValue("layers"), conf.getIntValue("type"), conf.getIntValue("group_id"), 
				conf.getIntValue("num"));
				if(!conf.containsKey("sn")){
					
				    continue;
                }
				dataMap.put(conf.getInteger("sn"), object);
			}

			//保存数据
			_map = Collections.unmodifiableMap(dataMap);
		}
		
		
		
		public static double[] parseDoubleArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				double []temp = new double[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.doubleValue(elems[i]);
				}
				return temp;
			}
			return null;
	  }
	  
		public static float[] parseFloatArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				float []temp = new float[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.floatValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static int[] parseIntArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return  null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				int []temp = new int[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.intValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static float[][] parseFloatArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			float[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					float[] floatArr = Utils.strToFloatArray(strArr[i]);
					if(elems == null){
						elems = new float[strArr.length][floatArr.length];
					}
					elems[i] = new float[floatArr.length];
					for(int m = 0; m < floatArr.length; m++) {
						elems[i][m]=floatArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static int[][] parseIntArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			int[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					int[] intArr = Utils.arrayStrToInt(strArr[i]);
					if(elems == null){
						elems = new int[strArr.length][intArr.length];
					}
					elems[i] = new int[intArr.length];
					for(int m = 0; m < intArr.length; m++) {
						elems[i][m]=intArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
	
		public static String[] parseStringArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			//if(value.contains(",")){
			//	elems = value.split("\\,");
			//}
			if(elems.length > 0) {
				String []temp = new String[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = elems[i];
				}
				return temp;
			}
			return null;
		}
		
		public static String[][] parseStringArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}

			String[][] elems = null;
			String[] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					String[] arr = Utils.splitStr(strArr[i], "\\,");
					if(elems == null){
						elems = new String[strArr.length][arr.length];
					}
					elems[i] = new String[arr.length];
					for(int m = 0; m < arr.length; m++) {
						elems[i][m]=arr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static long[] parseLongArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				long []temp = new long[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.longValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static long[][] parseLongArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			long[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					long[] longArr = Utils.arrayStrToLong(strArr[i]);
					if(elems == null){
						elems = new long[strArr.length][longArr.length];
					}
					elems[i] = new long[longArr.length];
					for(int m = 0; m < longArr.length; m++) {
						elems[i][m]=longArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static boolean[] parseBoolArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
		
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				boolean []temp = new boolean[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.booleanValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
	
		/**
		 * 读取游戏配置
		 */
		private static String _readConfFile() {
			String result = "";
			BufferedReader reader = null;
			
			try {
				String filePath = Thread.currentThread().getContextClassLoader().getResource("ConfBreakGoldEggLayers.json").getPath();
				InputStream is = new FileInputStream(filePath);
				
				reader = new BufferedReader(new InputStreamReader(is , "UTF-8"));
			    String tempString = "";
			    while ((tempString = reader.readLine()) != null) {
				result += tempString;
			    }
			    
			} catch (Exception e) {
			    throw new RuntimeException(e);
			} finally {
				if(reader != null)
					try {
						reader.close();
					} catch (IOException e) {
						throw new RuntimeException(e);
					}
			}

			return result;
		}
	}
	
	/**
	 * 比较函数
	 * 
	 * @param cell 比较的对象
	 * @param params 自定义排序字段
	 * @return
	 */
	public int compare(ConfBreakGoldEggLayers cell, Object...params) {
		List<Object> settings = Utils.ofList(params);
		List<OrderByField> paramsOrder = new ArrayList<>();		//排序规则
		
		//参数数量
		int len = settings.size();
		
		//参数必须成对出现
		if(len % 2 != 0) {
			throw new SysException("查询参数必须成对出现:query={}", settings);
		}
		
		//处理成对参数
		for(int i = 0; i < len; i += 2) {
			String key = (String)settings.get(i);
			Object val = settings.get(i + 1);
			
			//参数 排序规则
			if(val instanceof OrderBy) {
				paramsOrder.add(new OrderByField(key, (OrderBy) val));
			}
		}
		
		return compareTo(cell, paramsOrder);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	private int compareTo(ConfBreakGoldEggLayers cell, List<OrderByField> paramsOrder) {
		try {
			for(OrderByField e : paramsOrder) {
				//两方字段值
				Comparable va = this.getFieldValue(e.getKey());
				Comparable vb = cell.getFieldValue(e.getKey());
				
				//值排序结果
				int compareResult = va.compareTo(vb);
				
				//相等时 根据下一个值进行排序
				if(va.compareTo(vb) == 0) continue;
				
				//配置排序规则
				OrderBy order = e.getOrderBy();
				if(order == OrderBy.ASC) return compareResult;		//正序
				else return -1 * compareResult;					//倒序
			}
		} catch (Exception e) {
			throw new SysException(e);
		}

		return 0;
	}
    
}