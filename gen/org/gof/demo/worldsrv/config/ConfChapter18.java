package org.gof.demo.worldsrv.config;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.StringUtils;
import org.gof.core.support.ConfigJSON;
import org.gof.core.support.IReloadSupport;
import org.gof.core.support.OrderBy;
import org.gof.core.support.OrderByField;
import org.gof.core.support.SysException;
import org.gof.core.support.Utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.demo.worldsrv.support.Log;

/**
 * Chapter18_主线副本18 
 * Chapter18_主线副本18.xlsx
 * <AUTHOR>
 * 此类是系统自动生成类 不要直接修改，修改后也会被覆盖
 */
@ConfigJSON
public class ConfChapter18 {
	public final int sn;			//sn
	public final int index;			//？？？
	public final int type;			//？？？
	public final int level;			//？？？
	public final String power;			//？？？
	public final int map;			//地图
	public final int chapter;			//章节
	public final int section;			//段落
	public final int part;			//波次
	public final int part_type;			//波次类型
	public final int next_part;			//下一波
	public final float[] interval;			//间隔
	public final int time;			//挑战时间
	public final int[][] online_reward1;			//通关奖励
	public final int[][] online_reward2;			//通关奖励
	public final int[][] offline_reward1;			//离线奖励
	public final int[][] offline_reward2;			//离线奖励
	

	public ConfChapter18(int sn, int index, int type, int level, String power, int map, int chapter, int section, int part, int part_type, int next_part, float[] interval, int time, int[][] online_reward1, int[][] online_reward2, int[][] offline_reward1, int[][] offline_reward2) {
		this.sn = sn;
		this.index = index;
		this.type = type;
		this.level = level;
		this.power = power;
		this.map = map;
		this.chapter = chapter;
		this.section = section;
		this.part = part;
		this.part_type = part_type;
		this.next_part = next_part;
		this.interval = interval;
		this.time = time;
		this.online_reward1 = online_reward1;
		this.online_reward2 = online_reward2;
		this.offline_reward1 = offline_reward1;
		this.offline_reward2 = offline_reward2;
	}

	private static IReloadSupport support = null;
	
	public static void initReloadSupport(IReloadSupport s){
		support = s;
	}
	
	public static void reLoad() {
		if(support != null)
			support.beforeReload();
		DATA._init();
		
		if(support != null)
			support.afterReload();
	}
	
	/**
	 * 获取全部数据
	 * @return
	 */
	public static Collection<ConfChapter18> findAll() {
		return DATA.getList();
	}

	/**
	 * 通过SN获取数据
	 * @param sn
	 * @return
	 */
	public static ConfChapter18 get(Integer sn) {
		return DATA.getMap().get(sn);
	}
	
	/**
	 * 通过多个组件组合成sn获取数据
	 * @param obj 多组件按顺序
	 * @return ConfChapter18 对象
	 */

	
	/**
	 * 通过属性获取单条数据
	 * @param params
	 * @return
	 */
	public static ConfChapter18 getBy(Object...params) {
		List<ConfChapter18> list = utilBase(params);
		
		if(list.isEmpty()) return null;
		else return list.get(0);
	}
	
	/**
	 * 通过属性获取数据集合
	 * @param params
	 * @return
	 */
	public static List<ConfChapter18> findBy(Object...params) {
		return utilBase(params);
	}
	
	/**
	 * 通过属性获取数据集合 支持排序
	 * @param params
	 * @return
	 */
	public static List<ConfChapter18> utilBase(Object...params) {
		List<Object> settings = Utils.ofList(params);
		
		//查询参数
		final Map<String, Object> paramsFilter = new LinkedHashMap<>();		//过滤条件
		final List<OrderByField> paramsOrder = new ArrayList<>();		//排序规则
				
		//参数数量
		int len = settings.size();
		
		//参数必须成对出现
		if(len % 2 != 0) {
			throw new SysException("查询参数必须成对出现:query={}", settings);
		}
		
		//处理成对参数
		for(int i = 0; i < len; i += 2) {
			String key = (String)settings.get(i);
			Object val = settings.get(i + 1);
			
			//参数 排序规则
			if(val instanceof OrderBy) {
				paramsOrder.add(new OrderByField(key, (OrderBy) val));
			} else {	//参数 过滤条件
				paramsFilter.put(key, val);
			}
		}
		
		//返回结果
		List<ConfChapter18> result = new ArrayList<>();
		
		try {
			//通过条件获取结果
			for(ConfChapter18 c : DATA.getList()) {
				//本行数据是否符合过滤条件
				boolean bingo = true;
				
				//判断过滤条件
				for(Entry<String, Object> p : paramsFilter.entrySet()) {
					//实际结果
					Object valTrue = c.getFieldValue(p.getKey());
					//期望结果
					Object valWish = p.getValue();
					
					//有不符合过滤条件的
					if(!valWish.equals(valTrue)) {
						bingo = false;
						break;
					}
				}
				
				//记录符合结果
				if(bingo) {
					result.add(c);
				}
			}
		} catch (Exception e) {
			throw new SysException(e);
		}
		
		//对结果进行排序
		Collections.sort(result, (a, b) -> a.compareTo(b, paramsOrder));
		
		return result;
	}

	/**
	 * 属性关键字
	 */
	public static final class K {
		public static final String sn = "sn";	//sn
		public static final String index = "index";	//？？？
		public static final String type = "type";	//？？？
		public static final String level = "level";	//？？？
		public static final String power = "power";	//？？？
		public static final String map = "map";	//地图
		public static final String chapter = "chapter";	//章节
		public static final String section = "section";	//段落
		public static final String part = "part";	//波次
		public static final String part_type = "part_type";	//波次类型
		public static final String next_part = "next_part";	//下一波
		public static final String interval = "interval";	//间隔
		public static final String time = "time";	//挑战时间
		public static final String online_reward1 = "online_reward1";	//通关奖励
		public static final String online_reward2 = "online_reward2";	//通关奖励
		public static final String offline_reward1 = "offline_reward1";	//离线奖励
		public static final String offline_reward2 = "offline_reward2";	//离线奖励
	}
	
	/**
	 *
	 * 取得属性值
	 * @param classInstance 实例
	 * @key 属性名称
	 *
	 */
	@SuppressWarnings("unchecked")
	public <T> T getFieldValue(String key) {
		Object value = null;
		switch (key) {
			case "sn": {
				value = this.sn;
				break;
			}
			case "index": {
				value = this.index;
				break;
			}
			case "type": {
				value = this.type;
				break;
			}
			case "level": {
				value = this.level;
				break;
			}
			case "power": {
				value = this.power;
				break;
			}
			case "map": {
				value = this.map;
				break;
			}
			case "chapter": {
				value = this.chapter;
				break;
			}
			case "section": {
				value = this.section;
				break;
			}
			case "part": {
				value = this.part;
				break;
			}
			case "part_type": {
				value = this.part_type;
				break;
			}
			case "next_part": {
				value = this.next_part;
				break;
			}
			case "interval": {
				value = this.interval;
				break;
			}
			case "time": {
				value = this.time;
				break;
			}
			case "online_reward1": {
				value = this.online_reward1;
				break;
			}
			case "online_reward2": {
				value = this.online_reward2;
				break;
			}
			case "offline_reward1": {
				value = this.offline_reward1;
				break;
			}
			case "offline_reward2": {
				value = this.offline_reward2;
				break;
			}
			default: break;
		}
		
		return (T) value;
	}

	/**
	 * 数据集
	 * 单独提出来也是为了做数据延迟初始化
	 * 避免启动遍历类时，触发了static静态块
	 */
	@SuppressWarnings({"unused"})
	private static final class DATA {
		//全部数据
		private static Map<Integer, ConfChapter18> _map;
		
		/**
		 * 获取数据的值集合
		 * @return
		 */
		public static Collection<ConfChapter18> getList() {
			return getMap().values();
		}
		
		/**
		 * 获取Map类型数据集合
		 * @return
		 */
		public static Map<Integer, ConfChapter18> getMap() {
			//延迟初始化
			if(_map == null) {
				synchronized (DATA.class) {
					if(_map == null) {
						_init();
					}
				}
			}
			
			return _map;
		}


		/**
		 * 初始化数据
		 */
		private static void _init() {
			Map<Integer, ConfChapter18> dataMap = new java.util.concurrent.ConcurrentHashMap<>();
			
			//JSON数据
			String confJSON = _readConfFile();
			if(StringUtils.isBlank(confJSON)) return;
			
			//填充实体数据
			JSONArray confs = (JSONArray)JSONArray.parse(confJSON);
			
			JSONObject confTemp = confs.getJSONObject(0);
			if(!confTemp.containsKey("sn")){
				Log.temp.info("表{}有问题!", "ConfChapter18");
			}
			for(int i = 0 ; i < confs.size() ; i++){
				JSONObject conf = confs.getJSONObject(i);
				
				/*if(conf.get("sn") instanceof String && conf.get("sn") == null){
					continue;
				}*/
				ConfChapter18 object = new ConfChapter18(conf.getIntValue("sn"), conf.getIntValue("index"), conf.getIntValue("type"), conf.getIntValue("level"), 
				conf.getString("power"), conf.getIntValue("map"), conf.getIntValue("chapter"), conf.getIntValue("section"), 
				conf.getIntValue("part"), conf.getIntValue("part_type"), conf.getIntValue("next_part"), parseFloatArray(conf.getString("interval")), 
				conf.getIntValue("time"), parseIntArray2(conf.getString("online_reward1")), parseIntArray2(conf.getString("online_reward2")), parseIntArray2(conf.getString("offline_reward1")), 
				parseIntArray2(conf.getString("offline_reward2")));
				if(!conf.containsKey("sn")){
					
				    continue;
                }
				dataMap.put(conf.getInteger("sn"), object);
			}

			//保存数据
			_map = Collections.unmodifiableMap(dataMap);
		}
		
		
		
		public static double[] parseDoubleArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				double []temp = new double[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.doubleValue(elems[i]);
				}
				return temp;
			}
			return null;
	  }
	  
		public static float[] parseFloatArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				float []temp = new float[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.floatValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static int[] parseIntArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return  null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				int []temp = new int[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.intValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static float[][] parseFloatArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			float[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					float[] floatArr = Utils.strToFloatArray(strArr[i]);
					if(elems == null){
						elems = new float[strArr.length][floatArr.length];
					}
					elems[i] = new float[floatArr.length];
					for(int m = 0; m < floatArr.length; m++) {
						elems[i][m]=floatArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static int[][] parseIntArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			int[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					int[] intArr = Utils.arrayStrToInt(strArr[i]);
					if(elems == null){
						elems = new int[strArr.length][intArr.length];
					}
					elems[i] = new int[intArr.length];
					for(int m = 0; m < intArr.length; m++) {
						elems[i][m]=intArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
	
		public static String[] parseStringArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			//if(value.contains(",")){
			//	elems = value.split("\\,");
			//}
			if(elems.length > 0) {
				String []temp = new String[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = elems[i];
				}
				return temp;
			}
			return null;
		}
		
		public static String[][] parseStringArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}

			String[][] elems = null;
			String[] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					String[] arr = Utils.splitStr(strArr[i], "\\,");
					if(elems == null){
						elems = new String[strArr.length][arr.length];
					}
					elems[i] = new String[arr.length];
					for(int m = 0; m < arr.length; m++) {
						elems[i][m]=arr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static long[] parseLongArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				long []temp = new long[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.longValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static long[][] parseLongArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			long[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					long[] longArr = Utils.arrayStrToLong(strArr[i]);
					if(elems == null){
						elems = new long[strArr.length][longArr.length];
					}
					elems[i] = new long[longArr.length];
					for(int m = 0; m < longArr.length; m++) {
						elems[i][m]=longArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static boolean[] parseBoolArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
		
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				boolean []temp = new boolean[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.booleanValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
	
		/**
		 * 读取游戏配置
		 */
		private static String _readConfFile() {
			String result = "";
			BufferedReader reader = null;
			
			try {
				String filePath = Thread.currentThread().getContextClassLoader().getResource("ConfChapter18.json").getPath();
				InputStream is = new FileInputStream(filePath);
				
				reader = new BufferedReader(new InputStreamReader(is , "UTF-8"));
			    String tempString = "";
			    while ((tempString = reader.readLine()) != null) {
				result += tempString;
			    }
			    
			} catch (Exception e) {
			    throw new RuntimeException(e);
			} finally {
				if(reader != null)
					try {
						reader.close();
					} catch (IOException e) {
						throw new RuntimeException(e);
					}
			}

			return result;
		}
	}
	
	/**
	 * 比较函数
	 * 
	 * @param cell 比较的对象
	 * @param params 自定义排序字段
	 * @return
	 */
	public int compare(ConfChapter18 cell, Object...params) {
		List<Object> settings = Utils.ofList(params);
		List<OrderByField> paramsOrder = new ArrayList<>();		//排序规则
		
		//参数数量
		int len = settings.size();
		
		//参数必须成对出现
		if(len % 2 != 0) {
			throw new SysException("查询参数必须成对出现:query={}", settings);
		}
		
		//处理成对参数
		for(int i = 0; i < len; i += 2) {
			String key = (String)settings.get(i);
			Object val = settings.get(i + 1);
			
			//参数 排序规则
			if(val instanceof OrderBy) {
				paramsOrder.add(new OrderByField(key, (OrderBy) val));
			}
		}
		
		return compareTo(cell, paramsOrder);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	private int compareTo(ConfChapter18 cell, List<OrderByField> paramsOrder) {
		try {
			for(OrderByField e : paramsOrder) {
				//两方字段值
				Comparable va = this.getFieldValue(e.getKey());
				Comparable vb = cell.getFieldValue(e.getKey());
				
				//值排序结果
				int compareResult = va.compareTo(vb);
				
				//相等时 根据下一个值进行排序
				if(va.compareTo(vb) == 0) continue;
				
				//配置排序规则
				OrderBy order = e.getOrderBy();
				if(order == OrderBy.ASC) return compareResult;		//正序
				else return -1 * compareResult;					//倒序
			}
		} catch (Exception e) {
			throw new SysException(e);
		}

		return 0;
	}
    
}