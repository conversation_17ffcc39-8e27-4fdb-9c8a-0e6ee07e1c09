package org.gof.demo.worldsrv.config;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.StringUtils;
import org.gof.core.support.ConfigJSON;
import org.gof.core.support.IReloadSupport;
import org.gof.core.support.OrderBy;
import org.gof.core.support.OrderByField;
import org.gof.core.support.SysException;
import org.gof.core.support.Utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.demo.worldsrv.support.Log;

/**
 * Marry_结婚表 
 * Marry_结婚表.xlsx
 * <AUTHOR>
 * 此类是系统自动生成类 不要直接修改，修改后也会被覆盖
 */
@ConfigJSON
public class ConfMarry {
	public final int sn;			//sn
	public final int[][] spend;			//花费
	public final int[][] reward;			//奖励
	public final int[][] show_reward;			//展示奖励
	public final int invite_num;			//达到数量
	public final int num_max;			//数量上限
	public final int[] enter_spend;			//进入花费
	public final int[] process_time;			//过程时间
	public final int[] cheers_time;			//干杯时间
	public final int[] output0;			//产出
	public final int[][] active_free_num;			//免费激活数量
	public final int active_time;			//激活时间
	public final int[] active_standard;			//激活标准
	public final int output_ratio;			//产出音效
	public final int active_rate;			//激活速度
	public final int output_get_num;			//产出获取数量
	public final int output_num;			//产出数量
	public final int[] output1;			//产出1
	public final int[] output2;			//产出2
	public final int[] output3;			//产出3
	public final int final_reward;			//最终奖励
	public final int[][] points;			//分数
	public final int[][] pos_limit;			//极限坐标
	public final int[] desc;			//文本
	

	public ConfMarry(int sn, int[][] spend, int[][] reward, int[][] show_reward, int invite_num, int num_max, int[] enter_spend, int[] process_time, int[] cheers_time, int[] output0, int[][] active_free_num, int active_time, int[] active_standard, int output_ratio, int active_rate, int output_get_num, int output_num, int[] output1, int[] output2, int[] output3, int final_reward, int[][] points, int[][] pos_limit, int[] desc) {
		this.sn = sn;
		this.spend = spend;
		this.reward = reward;
		this.show_reward = show_reward;
		this.invite_num = invite_num;
		this.num_max = num_max;
		this.enter_spend = enter_spend;
		this.process_time = process_time;
		this.cheers_time = cheers_time;
		this.output0 = output0;
		this.active_free_num = active_free_num;
		this.active_time = active_time;
		this.active_standard = active_standard;
		this.output_ratio = output_ratio;
		this.active_rate = active_rate;
		this.output_get_num = output_get_num;
		this.output_num = output_num;
		this.output1 = output1;
		this.output2 = output2;
		this.output3 = output3;
		this.final_reward = final_reward;
		this.points = points;
		this.pos_limit = pos_limit;
		this.desc = desc;
	}

	private static IReloadSupport support = null;
	
	public static void initReloadSupport(IReloadSupport s){
		support = s;
	}
	
	public static void reLoad() {
		if(support != null)
			support.beforeReload();
		DATA._init();
		
		if(support != null)
			support.afterReload();
	}
	
	/**
	 * 获取全部数据
	 * @return
	 */
	public static Collection<ConfMarry> findAll() {
		return DATA.getList();
	}

	/**
	 * 通过SN获取数据
	 * @param sn
	 * @return
	 */
	public static ConfMarry get(Integer sn) {
		return DATA.getMap().get(sn);
	}
	
	/**
	 * 通过多个组件组合成sn获取数据
	 * @param obj 多组件按顺序
	 * @return ConfMarry 对象
	 */

	
	/**
	 * 通过属性获取单条数据
	 * @param params
	 * @return
	 */
	public static ConfMarry getBy(Object...params) {
		List<ConfMarry> list = utilBase(params);
		
		if(list.isEmpty()) return null;
		else return list.get(0);
	}
	
	/**
	 * 通过属性获取数据集合
	 * @param params
	 * @return
	 */
	public static List<ConfMarry> findBy(Object...params) {
		return utilBase(params);
	}
	
	/**
	 * 通过属性获取数据集合 支持排序
	 * @param params
	 * @return
	 */
	public static List<ConfMarry> utilBase(Object...params) {
		List<Object> settings = Utils.ofList(params);
		
		//查询参数
		final Map<String, Object> paramsFilter = new LinkedHashMap<>();		//过滤条件
		final List<OrderByField> paramsOrder = new ArrayList<>();		//排序规则
				
		//参数数量
		int len = settings.size();
		
		//参数必须成对出现
		if(len % 2 != 0) {
			throw new SysException("查询参数必须成对出现:query={}", settings);
		}
		
		//处理成对参数
		for(int i = 0; i < len; i += 2) {
			String key = (String)settings.get(i);
			Object val = settings.get(i + 1);
			
			//参数 排序规则
			if(val instanceof OrderBy) {
				paramsOrder.add(new OrderByField(key, (OrderBy) val));
			} else {	//参数 过滤条件
				paramsFilter.put(key, val);
			}
		}
		
		//返回结果
		List<ConfMarry> result = new ArrayList<>();
		
		try {
			//通过条件获取结果
			for(ConfMarry c : DATA.getList()) {
				//本行数据是否符合过滤条件
				boolean bingo = true;
				
				//判断过滤条件
				for(Entry<String, Object> p : paramsFilter.entrySet()) {
					//实际结果
					Object valTrue = c.getFieldValue(p.getKey());
					//期望结果
					Object valWish = p.getValue();
					
					//有不符合过滤条件的
					if(!valWish.equals(valTrue)) {
						bingo = false;
						break;
					}
				}
				
				//记录符合结果
				if(bingo) {
					result.add(c);
				}
			}
		} catch (Exception e) {
			throw new SysException(e);
		}
		
		//对结果进行排序
		Collections.sort(result, (a, b) -> a.compareTo(b, paramsOrder));
		
		return result;
	}

	/**
	 * 属性关键字
	 */
	public static final class K {
		public static final String sn = "sn";	//sn
		public static final String spend = "spend";	//花费
		public static final String reward = "reward";	//奖励
		public static final String show_reward = "show_reward";	//展示奖励
		public static final String invite_num = "invite_num";	//达到数量
		public static final String num_max = "num_max";	//数量上限
		public static final String enter_spend = "enter_spend";	//进入花费
		public static final String process_time = "process_time";	//过程时间
		public static final String cheers_time = "cheers_time";	//干杯时间
		public static final String output0 = "output0";	//产出
		public static final String active_free_num = "active_free_num";	//免费激活数量
		public static final String active_time = "active_time";	//激活时间
		public static final String active_standard = "active_standard";	//激活标准
		public static final String output_ratio = "output_ratio";	//产出音效
		public static final String active_rate = "active_rate";	//激活速度
		public static final String output_get_num = "output_get_num";	//产出获取数量
		public static final String output_num = "output_num";	//产出数量
		public static final String output1 = "output1";	//产出1
		public static final String output2 = "output2";	//产出2
		public static final String output3 = "output3";	//产出3
		public static final String final_reward = "final_reward";	//最终奖励
		public static final String points = "points";	//分数
		public static final String pos_limit = "pos_limit";	//极限坐标
		public static final String desc = "desc";	//文本
	}
	
	/**
	 *
	 * 取得属性值
	 * @param classInstance 实例
	 * @key 属性名称
	 *
	 */
	@SuppressWarnings("unchecked")
	public <T> T getFieldValue(String key) {
		Object value = null;
		switch (key) {
			case "sn": {
				value = this.sn;
				break;
			}
			case "spend": {
				value = this.spend;
				break;
			}
			case "reward": {
				value = this.reward;
				break;
			}
			case "show_reward": {
				value = this.show_reward;
				break;
			}
			case "invite_num": {
				value = this.invite_num;
				break;
			}
			case "num_max": {
				value = this.num_max;
				break;
			}
			case "enter_spend": {
				value = this.enter_spend;
				break;
			}
			case "process_time": {
				value = this.process_time;
				break;
			}
			case "cheers_time": {
				value = this.cheers_time;
				break;
			}
			case "output0": {
				value = this.output0;
				break;
			}
			case "active_free_num": {
				value = this.active_free_num;
				break;
			}
			case "active_time": {
				value = this.active_time;
				break;
			}
			case "active_standard": {
				value = this.active_standard;
				break;
			}
			case "output_ratio": {
				value = this.output_ratio;
				break;
			}
			case "active_rate": {
				value = this.active_rate;
				break;
			}
			case "output_get_num": {
				value = this.output_get_num;
				break;
			}
			case "output_num": {
				value = this.output_num;
				break;
			}
			case "output1": {
				value = this.output1;
				break;
			}
			case "output2": {
				value = this.output2;
				break;
			}
			case "output3": {
				value = this.output3;
				break;
			}
			case "final_reward": {
				value = this.final_reward;
				break;
			}
			case "points": {
				value = this.points;
				break;
			}
			case "pos_limit": {
				value = this.pos_limit;
				break;
			}
			case "desc": {
				value = this.desc;
				break;
			}
			default: break;
		}
		
		return (T) value;
	}

	/**
	 * 数据集
	 * 单独提出来也是为了做数据延迟初始化
	 * 避免启动遍历类时，触发了static静态块
	 */
	@SuppressWarnings({"unused"})
	private static final class DATA {
		//全部数据
		private static Map<Integer, ConfMarry> _map;
		
		/**
		 * 获取数据的值集合
		 * @return
		 */
		public static Collection<ConfMarry> getList() {
			return getMap().values();
		}
		
		/**
		 * 获取Map类型数据集合
		 * @return
		 */
		public static Map<Integer, ConfMarry> getMap() {
			//延迟初始化
			if(_map == null) {
				synchronized (DATA.class) {
					if(_map == null) {
						_init();
					}
				}
			}
			
			return _map;
		}


		/**
		 * 初始化数据
		 */
		private static void _init() {
			Map<Integer, ConfMarry> dataMap = new java.util.concurrent.ConcurrentHashMap<>();
			
			//JSON数据
			String confJSON = _readConfFile();
			if(StringUtils.isBlank(confJSON)) return;
			
			//填充实体数据
			JSONArray confs = (JSONArray)JSONArray.parse(confJSON);
			
			JSONObject confTemp = confs.getJSONObject(0);
			if(!confTemp.containsKey("sn")){
				Log.temp.info("表{}有问题!", "ConfMarry");
			}
			for(int i = 0 ; i < confs.size() ; i++){
				JSONObject conf = confs.getJSONObject(i);
				
				/*if(conf.get("sn") instanceof String && conf.get("sn") == null){
					continue;
				}*/
				ConfMarry object = new ConfMarry(conf.getIntValue("sn"), parseIntArray2(conf.getString("spend")), parseIntArray2(conf.getString("reward")), parseIntArray2(conf.getString("show_reward")), 
				conf.getIntValue("invite_num"), conf.getIntValue("num_max"), parseIntArray(conf.getString("enter_spend")), parseIntArray(conf.getString("process_time")), 
				parseIntArray(conf.getString("cheers_time")), parseIntArray(conf.getString("output0")), parseIntArray2(conf.getString("active_free_num")), conf.getIntValue("active_time"), 
				parseIntArray(conf.getString("active_standard")), conf.getIntValue("output_ratio"), conf.getIntValue("active_rate"), conf.getIntValue("output_get_num"), 
				conf.getIntValue("output_num"), parseIntArray(conf.getString("output1")), parseIntArray(conf.getString("output2")), parseIntArray(conf.getString("output3")), 
				conf.getIntValue("final_reward"), parseIntArray2(conf.getString("points")), parseIntArray2(conf.getString("pos_limit")), parseIntArray(conf.getString("desc")));
				if(!conf.containsKey("sn")){
					
				    continue;
                }
				dataMap.put(conf.getInteger("sn"), object);
			}

			//保存数据
			_map = Collections.unmodifiableMap(dataMap);
		}
		
		
		
		public static double[] parseDoubleArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				double []temp = new double[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.doubleValue(elems[i]);
				}
				return temp;
			}
			return null;
	  }
	  
		public static float[] parseFloatArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				float []temp = new float[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.floatValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static int[] parseIntArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return  null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				int []temp = new int[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.intValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static float[][] parseFloatArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			float[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					float[] floatArr = Utils.strToFloatArray(strArr[i]);
					if(elems == null){
						elems = new float[strArr.length][floatArr.length];
					}
					elems[i] = new float[floatArr.length];
					for(int m = 0; m < floatArr.length; m++) {
						elems[i][m]=floatArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static int[][] parseIntArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			int[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					int[] intArr = Utils.arrayStrToInt(strArr[i]);
					if(elems == null){
						elems = new int[strArr.length][intArr.length];
					}
					elems[i] = new int[intArr.length];
					for(int m = 0; m < intArr.length; m++) {
						elems[i][m]=intArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
	
		public static String[] parseStringArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			//if(value.contains(",")){
			//	elems = value.split("\\,");
			//}
			if(elems.length > 0) {
				String []temp = new String[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = elems[i];
				}
				return temp;
			}
			return null;
		}
		
		public static String[][] parseStringArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}

			String[][] elems = null;
			String[] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					String[] arr = Utils.splitStr(strArr[i], "\\,");
					if(elems == null){
						elems = new String[strArr.length][arr.length];
					}
					elems[i] = new String[arr.length];
					for(int m = 0; m < arr.length; m++) {
						elems[i][m]=arr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static long[] parseLongArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				long []temp = new long[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.longValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static long[][] parseLongArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			long[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					long[] longArr = Utils.arrayStrToLong(strArr[i]);
					if(elems == null){
						elems = new long[strArr.length][longArr.length];
					}
					elems[i] = new long[longArr.length];
					for(int m = 0; m < longArr.length; m++) {
						elems[i][m]=longArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static boolean[] parseBoolArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
		
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				boolean []temp = new boolean[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.booleanValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
	
		/**
		 * 读取游戏配置
		 */
		private static String _readConfFile() {
			String result = "";
			BufferedReader reader = null;
			
			try {
				String filePath = Thread.currentThread().getContextClassLoader().getResource("ConfMarry.json").getPath();
				InputStream is = new FileInputStream(filePath);
				
				reader = new BufferedReader(new InputStreamReader(is , "UTF-8"));
			    String tempString = "";
			    while ((tempString = reader.readLine()) != null) {
				result += tempString;
			    }
			    
			} catch (Exception e) {
			    throw new RuntimeException(e);
			} finally {
				if(reader != null)
					try {
						reader.close();
					} catch (IOException e) {
						throw new RuntimeException(e);
					}
			}

			return result;
		}
	}
	
	/**
	 * 比较函数
	 * 
	 * @param cell 比较的对象
	 * @param params 自定义排序字段
	 * @return
	 */
	public int compare(ConfMarry cell, Object...params) {
		List<Object> settings = Utils.ofList(params);
		List<OrderByField> paramsOrder = new ArrayList<>();		//排序规则
		
		//参数数量
		int len = settings.size();
		
		//参数必须成对出现
		if(len % 2 != 0) {
			throw new SysException("查询参数必须成对出现:query={}", settings);
		}
		
		//处理成对参数
		for(int i = 0; i < len; i += 2) {
			String key = (String)settings.get(i);
			Object val = settings.get(i + 1);
			
			//参数 排序规则
			if(val instanceof OrderBy) {
				paramsOrder.add(new OrderByField(key, (OrderBy) val));
			}
		}
		
		return compareTo(cell, paramsOrder);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	private int compareTo(ConfMarry cell, List<OrderByField> paramsOrder) {
		try {
			for(OrderByField e : paramsOrder) {
				//两方字段值
				Comparable va = this.getFieldValue(e.getKey());
				Comparable vb = cell.getFieldValue(e.getKey());
				
				//值排序结果
				int compareResult = va.compareTo(vb);
				
				//相等时 根据下一个值进行排序
				if(va.compareTo(vb) == 0) continue;
				
				//配置排序规则
				OrderBy order = e.getOrderBy();
				if(order == OrderBy.ASC) return compareResult;		//正序
				else return -1 * compareResult;					//倒序
			}
		} catch (Exception e) {
			throw new SysException(e);
		}

		return 0;
	}
    
}