package org.gof.demo.worldsrv.config;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang3.StringUtils;
import org.gof.core.support.ConfigJSON;
import org.gof.core.support.IReloadSupport;
import org.gof.core.support.OrderBy;
import org.gof.core.support.OrderByField;
import org.gof.core.support.SysException;
import org.gof.core.support.Utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.gof.demo.worldsrv.support.Log;

/**
 * ExchangeRate_充值汇率表 
 * ExchangeRate_充值汇率表.xlsx
 * <AUTHOR>
 * 此类是系统自动生成类 不要直接修改，修改后也会被覆盖
 */
@ConfigJSON
public class ConfExchangeRate {
	public final int sn;			//gear
	public final float CNY;			//中文
	public final float JPY;			//日
	public final float USD;			//美元
	public final float AUD;			//AUD
	public final float TWD;			//TWD
	public final float HKD;			//HKD
	public final float GBP;			//GBP
	public final float KRW;			//KRW
	public final float CAD;			//CAD
	public final float EUR;			//EUR
	public final float CHF;			//CHF
	public final float CZK;			//CZK
	public final float DKK;			//DKK
	public final float HUF;			//HUF
	public final float NOK;			//NOK
	public final float NZD;			//NZD
	public final float PLN;			//PLN
	public final float SEK;			//SEK
	public final float SGD;			//SGD
	public final float THB;			//THB
	public final float PHP;			//PHP
	public final float MXN;			//MXN
	public final float ILS;			//ILS
	public final float RUB;			//RUB
	public final float VND;			//VND
	public final float IDR;			//IDR
	public final float CLP;			//CLP
	public final float MYR;			//MYR
	public final float BRL;			//BRL
	public final float AED;			//AED
	public final float BDT;			//BDT
	public final float PEN;			//PEN
	public final float RON;			//RON
	public final float SAR;			//SAR
	public final float INR;			//INR
	public final float ISK;			//ISK
	public final float BGN;			//BGN
	public final float ARS;			//ARS
	public final float MOP;			//MOP
	

	public ConfExchangeRate(int sn, float CNY, float JPY, float USD, float AUD, float TWD, float HKD, float GBP, float KRW, float CAD, float EUR, float CHF, float CZK, float DKK, float HUF, float NOK, float NZD, float PLN, float SEK, float SGD, float THB, float PHP, float MXN, float ILS, float RUB, float VND, float IDR, float CLP, float MYR, float BRL, float AED, float BDT, float PEN, float RON, float SAR, float INR, float ISK, float BGN, float ARS, float MOP) {
		this.sn = sn;
		this.CNY = CNY;
		this.JPY = JPY;
		this.USD = USD;
		this.AUD = AUD;
		this.TWD = TWD;
		this.HKD = HKD;
		this.GBP = GBP;
		this.KRW = KRW;
		this.CAD = CAD;
		this.EUR = EUR;
		this.CHF = CHF;
		this.CZK = CZK;
		this.DKK = DKK;
		this.HUF = HUF;
		this.NOK = NOK;
		this.NZD = NZD;
		this.PLN = PLN;
		this.SEK = SEK;
		this.SGD = SGD;
		this.THB = THB;
		this.PHP = PHP;
		this.MXN = MXN;
		this.ILS = ILS;
		this.RUB = RUB;
		this.VND = VND;
		this.IDR = IDR;
		this.CLP = CLP;
		this.MYR = MYR;
		this.BRL = BRL;
		this.AED = AED;
		this.BDT = BDT;
		this.PEN = PEN;
		this.RON = RON;
		this.SAR = SAR;
		this.INR = INR;
		this.ISK = ISK;
		this.BGN = BGN;
		this.ARS = ARS;
		this.MOP = MOP;
	}

	private static IReloadSupport support = null;
	
	public static void initReloadSupport(IReloadSupport s){
		support = s;
	}
	
	public static void reLoad() {
		if(support != null)
			support.beforeReload();
		DATA._init();
		
		if(support != null)
			support.afterReload();
	}
	
	/**
	 * 获取全部数据
	 * @return
	 */
	public static Collection<ConfExchangeRate> findAll() {
		return DATA.getList();
	}

	/**
	 * 通过SN获取数据
	 * @param sn
	 * @return
	 */
	public static ConfExchangeRate get(Integer sn) {
		return DATA.getMap().get(sn);
	}
	
	/**
	 * 通过多个组件组合成sn获取数据
	 * @param obj 多组件按顺序
	 * @return ConfExchangeRate 对象
	 */

	
	/**
	 * 通过属性获取单条数据
	 * @param params
	 * @return
	 */
	public static ConfExchangeRate getBy(Object...params) {
		List<ConfExchangeRate> list = utilBase(params);
		
		if(list.isEmpty()) return null;
		else return list.get(0);
	}
	
	/**
	 * 通过属性获取数据集合
	 * @param params
	 * @return
	 */
	public static List<ConfExchangeRate> findBy(Object...params) {
		return utilBase(params);
	}
	
	/**
	 * 通过属性获取数据集合 支持排序
	 * @param params
	 * @return
	 */
	public static List<ConfExchangeRate> utilBase(Object...params) {
		List<Object> settings = Utils.ofList(params);
		
		//查询参数
		final Map<String, Object> paramsFilter = new LinkedHashMap<>();		//过滤条件
		final List<OrderByField> paramsOrder = new ArrayList<>();		//排序规则
				
		//参数数量
		int len = settings.size();
		
		//参数必须成对出现
		if(len % 2 != 0) {
			throw new SysException("查询参数必须成对出现:query={}", settings);
		}
		
		//处理成对参数
		for(int i = 0; i < len; i += 2) {
			String key = (String)settings.get(i);
			Object val = settings.get(i + 1);
			
			//参数 排序规则
			if(val instanceof OrderBy) {
				paramsOrder.add(new OrderByField(key, (OrderBy) val));
			} else {	//参数 过滤条件
				paramsFilter.put(key, val);
			}
		}
		
		//返回结果
		List<ConfExchangeRate> result = new ArrayList<>();
		
		try {
			//通过条件获取结果
			for(ConfExchangeRate c : DATA.getList()) {
				//本行数据是否符合过滤条件
				boolean bingo = true;
				
				//判断过滤条件
				for(Entry<String, Object> p : paramsFilter.entrySet()) {
					//实际结果
					Object valTrue = c.getFieldValue(p.getKey());
					//期望结果
					Object valWish = p.getValue();
					
					//有不符合过滤条件的
					if(!valWish.equals(valTrue)) {
						bingo = false;
						break;
					}
				}
				
				//记录符合结果
				if(bingo) {
					result.add(c);
				}
			}
		} catch (Exception e) {
			throw new SysException(e);
		}
		
		//对结果进行排序
		Collections.sort(result, (a, b) -> a.compareTo(b, paramsOrder));
		
		return result;
	}

	/**
	 * 属性关键字
	 */
	public static final class K {
		public static final String sn = "sn";	//gear
		public static final String CNY = "CNY";	//中文
		public static final String JPY = "JPY";	//日
		public static final String USD = "USD";	//美元
		public static final String AUD = "AUD";	//AUD
		public static final String TWD = "TWD";	//TWD
		public static final String HKD = "HKD";	//HKD
		public static final String GBP = "GBP";	//GBP
		public static final String KRW = "KRW";	//KRW
		public static final String CAD = "CAD";	//CAD
		public static final String EUR = "EUR";	//EUR
		public static final String CHF = "CHF";	//CHF
		public static final String CZK = "CZK";	//CZK
		public static final String DKK = "DKK";	//DKK
		public static final String HUF = "HUF";	//HUF
		public static final String NOK = "NOK";	//NOK
		public static final String NZD = "NZD";	//NZD
		public static final String PLN = "PLN";	//PLN
		public static final String SEK = "SEK";	//SEK
		public static final String SGD = "SGD";	//SGD
		public static final String THB = "THB";	//THB
		public static final String PHP = "PHP";	//PHP
		public static final String MXN = "MXN";	//MXN
		public static final String ILS = "ILS";	//ILS
		public static final String RUB = "RUB";	//RUB
		public static final String VND = "VND";	//VND
		public static final String IDR = "IDR";	//IDR
		public static final String CLP = "CLP";	//CLP
		public static final String MYR = "MYR";	//MYR
		public static final String BRL = "BRL";	//BRL
		public static final String AED = "AED";	//AED
		public static final String BDT = "BDT";	//BDT
		public static final String PEN = "PEN";	//PEN
		public static final String RON = "RON";	//RON
		public static final String SAR = "SAR";	//SAR
		public static final String INR = "INR";	//INR
		public static final String ISK = "ISK";	//ISK
		public static final String BGN = "BGN";	//BGN
		public static final String ARS = "ARS";	//ARS
		public static final String MOP = "MOP";	//MOP
	}
	
	/**
	 *
	 * 取得属性值
	 * @param classInstance 实例
	 * @key 属性名称
	 *
	 */
	@SuppressWarnings("unchecked")
	public <T> T getFieldValue(String key) {
		Object value = null;
		switch (key) {
			case "sn": {
				value = this.sn;
				break;
			}
			case "CNY": {
				value = this.CNY;
				break;
			}
			case "JPY": {
				value = this.JPY;
				break;
			}
			case "USD": {
				value = this.USD;
				break;
			}
			case "AUD": {
				value = this.AUD;
				break;
			}
			case "TWD": {
				value = this.TWD;
				break;
			}
			case "HKD": {
				value = this.HKD;
				break;
			}
			case "GBP": {
				value = this.GBP;
				break;
			}
			case "KRW": {
				value = this.KRW;
				break;
			}
			case "CAD": {
				value = this.CAD;
				break;
			}
			case "EUR": {
				value = this.EUR;
				break;
			}
			case "CHF": {
				value = this.CHF;
				break;
			}
			case "CZK": {
				value = this.CZK;
				break;
			}
			case "DKK": {
				value = this.DKK;
				break;
			}
			case "HUF": {
				value = this.HUF;
				break;
			}
			case "NOK": {
				value = this.NOK;
				break;
			}
			case "NZD": {
				value = this.NZD;
				break;
			}
			case "PLN": {
				value = this.PLN;
				break;
			}
			case "SEK": {
				value = this.SEK;
				break;
			}
			case "SGD": {
				value = this.SGD;
				break;
			}
			case "THB": {
				value = this.THB;
				break;
			}
			case "PHP": {
				value = this.PHP;
				break;
			}
			case "MXN": {
				value = this.MXN;
				break;
			}
			case "ILS": {
				value = this.ILS;
				break;
			}
			case "RUB": {
				value = this.RUB;
				break;
			}
			case "VND": {
				value = this.VND;
				break;
			}
			case "IDR": {
				value = this.IDR;
				break;
			}
			case "CLP": {
				value = this.CLP;
				break;
			}
			case "MYR": {
				value = this.MYR;
				break;
			}
			case "BRL": {
				value = this.BRL;
				break;
			}
			case "AED": {
				value = this.AED;
				break;
			}
			case "BDT": {
				value = this.BDT;
				break;
			}
			case "PEN": {
				value = this.PEN;
				break;
			}
			case "RON": {
				value = this.RON;
				break;
			}
			case "SAR": {
				value = this.SAR;
				break;
			}
			case "INR": {
				value = this.INR;
				break;
			}
			case "ISK": {
				value = this.ISK;
				break;
			}
			case "BGN": {
				value = this.BGN;
				break;
			}
			case "ARS": {
				value = this.ARS;
				break;
			}
			case "MOP": {
				value = this.MOP;
				break;
			}
			default: break;
		}
		
		return (T) value;
	}

	/**
	 * 数据集
	 * 单独提出来也是为了做数据延迟初始化
	 * 避免启动遍历类时，触发了static静态块
	 */
	@SuppressWarnings({"unused"})
	private static final class DATA {
		//全部数据
		private static Map<Integer, ConfExchangeRate> _map;
		
		/**
		 * 获取数据的值集合
		 * @return
		 */
		public static Collection<ConfExchangeRate> getList() {
			return getMap().values();
		}
		
		/**
		 * 获取Map类型数据集合
		 * @return
		 */
		public static Map<Integer, ConfExchangeRate> getMap() {
			//延迟初始化
			if(_map == null) {
				synchronized (DATA.class) {
					if(_map == null) {
						_init();
					}
				}
			}
			
			return _map;
		}


		/**
		 * 初始化数据
		 */
		private static void _init() {
			Map<Integer, ConfExchangeRate> dataMap = new java.util.concurrent.ConcurrentHashMap<>();
			
			//JSON数据
			String confJSON = _readConfFile();
			if(StringUtils.isBlank(confJSON)) return;
			
			//填充实体数据
			JSONArray confs = (JSONArray)JSONArray.parse(confJSON);
			
			JSONObject confTemp = confs.getJSONObject(0);
			if(!confTemp.containsKey("sn")){
				Log.temp.info("表{}有问题!", "ConfExchangeRate");
			}
			for(int i = 0 ; i < confs.size() ; i++){
				JSONObject conf = confs.getJSONObject(i);
				
				/*if(conf.get("sn") instanceof String && conf.get("sn") == null){
					continue;
				}*/
				ConfExchangeRate object = new ConfExchangeRate(conf.getIntValue("sn"), conf.getFloatValue("CNY"), conf.getFloatValue("JPY"), conf.getFloatValue("USD"), 
				conf.getFloatValue("AUD"), conf.getFloatValue("TWD"), conf.getFloatValue("HKD"), conf.getFloatValue("GBP"), 
				conf.getFloatValue("KRW"), conf.getFloatValue("CAD"), conf.getFloatValue("EUR"), conf.getFloatValue("CHF"), 
				conf.getFloatValue("CZK"), conf.getFloatValue("DKK"), conf.getFloatValue("HUF"), conf.getFloatValue("NOK"), 
				conf.getFloatValue("NZD"), conf.getFloatValue("PLN"), conf.getFloatValue("SEK"), conf.getFloatValue("SGD"), 
				conf.getFloatValue("THB"), conf.getFloatValue("PHP"), conf.getFloatValue("MXN"), conf.getFloatValue("ILS"), 
				conf.getFloatValue("RUB"), conf.getFloatValue("VND"), conf.getFloatValue("IDR"), conf.getFloatValue("CLP"), 
				conf.getFloatValue("MYR"), conf.getFloatValue("BRL"), conf.getFloatValue("AED"), conf.getFloatValue("BDT"), 
				conf.getFloatValue("PEN"), conf.getFloatValue("RON"), conf.getFloatValue("SAR"), conf.getFloatValue("INR"), 
				conf.getFloatValue("ISK"), conf.getFloatValue("BGN"), conf.getFloatValue("ARS"), conf.getFloatValue("MOP"));
				if(!conf.containsKey("sn")){
					
				    continue;
                }
				dataMap.put(conf.getInteger("sn"), object);
			}

			//保存数据
			_map = Collections.unmodifiableMap(dataMap);
		}
		
		
		
		public static double[] parseDoubleArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				double []temp = new double[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.doubleValue(elems[i]);
				}
				return temp;
			}
			return null;
	  }
	  
		public static float[] parseFloatArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				float []temp = new float[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.floatValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static int[] parseIntArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return  null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				int []temp = new int[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.intValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static float[][] parseFloatArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			float[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					float[] floatArr = Utils.strToFloatArray(strArr[i]);
					if(elems == null){
						elems = new float[strArr.length][floatArr.length];
					}
					elems[i] = new float[floatArr.length];
					for(int m = 0; m < floatArr.length; m++) {
						elems[i][m]=floatArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static int[][] parseIntArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			int[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					int[] intArr = Utils.arrayStrToInt(strArr[i]);
					if(elems == null){
						elems = new int[strArr.length][intArr.length];
					}
					elems[i] = new int[intArr.length];
					for(int m = 0; m < intArr.length; m++) {
						elems[i][m]=intArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
	
		public static String[] parseStringArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			//if(value.contains(",")){
			//	elems = value.split("\\,");
			//}
			if(elems.length > 0) {
				String []temp = new String[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = elems[i];
				}
				return temp;
			}
			return null;
		}
		
		public static String[][] parseStringArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}

			String[][] elems = null;
			String[] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					String[] arr = Utils.splitStr(strArr[i], "\\,");
					if(elems == null){
						elems = new String[strArr.length][arr.length];
					}
					elems[i] = new String[arr.length];
					for(int m = 0; m < arr.length; m++) {
						elems[i][m]=arr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static long[] parseLongArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				long []temp = new long[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.longValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
		
		public static long[][] parseLongArray2(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
			
			long[][] elems = null;
			String [] strArr = Utils.splitStr(value, "\\|");
			if(strArr.length > 0){
				for(int i = 0; i < strArr.length; i++){
					long[] longArr = Utils.arrayStrToLong(strArr[i]);
					if(elems == null){
						elems = new long[strArr.length][longArr.length];
					}
					elems[i] = new long[longArr.length];
					for(int m = 0; m < longArr.length; m++) {
						elems[i][m]=longArr[m];
					}
				}
				return elems;
			}
			return null;
		}
		
		public static boolean[] parseBoolArray(String value) {
			if(value == null) value = "";
			if(StringUtils.isEmpty(value)){
				return null;
			}
		
			String[] elems = value.split("\\|");
			if(value.contains(",")){
				elems = value.split("\\,");
			}
			if(elems.length > 0) {
				boolean []temp = new boolean[elems.length] ;
				for(int i = 0 ; i < elems.length ; i++) {
					temp[i] = Utils.booleanValue(elems[i]);
				}
				return temp;
			}
			return null;
		}
	
		/**
		 * 读取游戏配置
		 */
		private static String _readConfFile() {
			String result = "";
			BufferedReader reader = null;
			
			try {
				String filePath = Thread.currentThread().getContextClassLoader().getResource("ConfExchangeRate.json").getPath();
				InputStream is = new FileInputStream(filePath);
				
				reader = new BufferedReader(new InputStreamReader(is , "UTF-8"));
			    String tempString = "";
			    while ((tempString = reader.readLine()) != null) {
				result += tempString;
			    }
			    
			} catch (Exception e) {
			    throw new RuntimeException(e);
			} finally {
				if(reader != null)
					try {
						reader.close();
					} catch (IOException e) {
						throw new RuntimeException(e);
					}
			}

			return result;
		}
	}
	
	/**
	 * 比较函数
	 * 
	 * @param cell 比较的对象
	 * @param params 自定义排序字段
	 * @return
	 */
	public int compare(ConfExchangeRate cell, Object...params) {
		List<Object> settings = Utils.ofList(params);
		List<OrderByField> paramsOrder = new ArrayList<>();		//排序规则
		
		//参数数量
		int len = settings.size();
		
		//参数必须成对出现
		if(len % 2 != 0) {
			throw new SysException("查询参数必须成对出现:query={}", settings);
		}
		
		//处理成对参数
		for(int i = 0; i < len; i += 2) {
			String key = (String)settings.get(i);
			Object val = settings.get(i + 1);
			
			//参数 排序规则
			if(val instanceof OrderBy) {
				paramsOrder.add(new OrderByField(key, (OrderBy) val));
			}
		}
		
		return compareTo(cell, paramsOrder);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	private int compareTo(ConfExchangeRate cell, List<OrderByField> paramsOrder) {
		try {
			for(OrderByField e : paramsOrder) {
				//两方字段值
				Comparable va = this.getFieldValue(e.getKey());
				Comparable vb = cell.getFieldValue(e.getKey());
				
				//值排序结果
				int compareResult = va.compareTo(vb);
				
				//相等时 根据下一个值进行排序
				if(va.compareTo(vb) == 0) continue;
				
				//配置排序规则
				OrderBy order = e.getOrderBy();
				if(order == OrderBy.ASC) return compareResult;		//正序
				else return -1 * compareResult;					//倒序
			}
		} catch (Exception e) {
			throw new SysException(e);
		}

		return 0;
	}
    
}