package org.gof.demo.worldsrv.carPark;
                    
import org.gof.core.CallPoint;
import org.gof.core.Port;
import org.gof.core.Service;
import org.gof.core.gen.proxy.ProxyBase;
import org.gof.core.support.*;
import org.gof.core.support.Distr;
import org.gof.core.support.log.LogCore;
import org.gof.core.support.Param;
import org.gof.core.support.Utils;
import org.gof.core.support.function.*;
import org.gof.core.gen.GofGenFile;
import org.gof.demo.worldsrv.carPark.ParkSpaceVo;
import java.util.List;
import org.gof.demo.worldsrv.msg.Define.p_role_change;
import org.gof.demo.worldsrv.msg.Define.p_role_figure;
import java.util.Map;

@GofGenFile
public final class CarParkServiceProxy extends ProxyBase {
	public final class EnumCall{
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ADDMOUNTVO_LONG_INT = 1;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_BACKMETHOD1_STRING = 2;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_BACKMETHOD2_STRING = 3;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKCARUP_LONG_INT_INT = 4;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKCOMBAT_LONG_INT_LONG_INT = 5;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKMOUNTID_LONG_INT = 6;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPARKINGSTART_LONG_INT_LONG_LONG_INT_INT_INT_P_ROLE_CHANGE_P_ROLE_FIGURE = 7;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPARKINGSTOP_LONG_LONG_P_ROLE_CHANGE_P_ROLE_FIGURE = 8;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPROTECT_LONG_INT_INT_INT = 9;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKRENAME_LONG_STRING = 10;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKRESULT_LONG_LONG_LONG_INT_LONG_INT_LONG = 11;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKSKINUP_LONG_INT_INT_INT = 12;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKSKINUSE_LONG_INT_INT_INT_INT_INT = 13;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETCARPARKCARINFO_LONG = 14;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETHUMANCARPARKINFO_LONG_LONG_BOOLEAN = 15;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETMOUNTMAP_LONG = 16;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETPUBLICCARPARKINFO_LONG_INT_INT = 17;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETPUBLICCARPARKNULLNUM_INT = 18;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETSKINLEVEL_LONG_INT = 19;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GM_LONG_STRING_PARAM = 20;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_HELPCOLLECTCARPARK_LONG_LONG_INT = 21;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ISEXITINCACHE_LONG = 22;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_LOADSERVER_LIST = 23;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONCARPARKFIX_LONG_INT_PARAM = 24;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONHUMANLOGIN_LONG = 25;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONHUMANLOGOUT_LONG = 26;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE1_STRING = 27;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE2_OBJECTS = 28;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE3_PARAM = 29;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE4_STRING = 30;
		public static final int ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATEREWARDADD_LONG_MAP = 31;
	}

	private static final String SERV_ID = "carPark";
	
	private CallPoint remote;
	private Port localPort;
	private String callerInfo;
	//当此参数为true时同NODE间传递将不在进行克隆，直接使用此对象进行。可极大提高性能，但如果设置不当可能引起错误。
	private boolean immutableOnce;

	//创建目标访问点语句 设置此语句后接管默认方案
    public static GofReturnFunction1<String,String> remoteNodeIdFunc;

	
	/**
	 * 私有构造函数
	 * 防止实例被私自创建 必须通过newInstance函数
	 */
	private CarParkServiceProxy() {}
	
	/**
	 * 获取函数指针
	 */
	@Override
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public Object getMethodFunction(Service service, int methodKey) {
		CarParkService serv = (CarParkService)service;
		switch (methodKey) {
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ADDMOUNTVO_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::addMountVo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_BACKMETHOD1_STRING: {
				return (GofFunction1<String>)serv::backMethod1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_BACKMETHOD2_STRING: {
				return (GofFunction1<String>)serv::backMethod2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKCARUP_LONG_INT_INT: {
				return (GofFunction3<Long, Integer, Integer>)serv::carParkCarUp;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKCOMBAT_LONG_INT_LONG_INT: {
				return (GofFunction4<Long, Integer, Long, Integer>)serv::carParkCombat;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKMOUNTID_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::carParkMountId;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPARKINGSTART_LONG_INT_LONG_LONG_INT_INT_INT_P_ROLE_CHANGE_P_ROLE_FIGURE: {
				return (GofFunction9<Long, Integer, Long, Long, Integer, Integer, Integer, p_role_change, p_role_figure>)serv::carParkParkingStart;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPARKINGSTOP_LONG_LONG_P_ROLE_CHANGE_P_ROLE_FIGURE: {
				return (GofFunction4<Long, Long, p_role_change, p_role_figure>)serv::carParkParkingStop;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPROTECT_LONG_INT_INT_INT: {
				return (GofFunction4<Long, Integer, Integer, Integer>)serv::carParkProtect;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKRENAME_LONG_STRING: {
				return (GofFunction2<Long, String>)serv::carParkRename;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKRESULT_LONG_LONG_LONG_INT_LONG_INT_LONG: {
				return (GofFunction7<Long, Long, Long, Integer, Long, Integer, Long>)serv::carParkResult;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKSKINUP_LONG_INT_INT_INT: {
				return (GofFunction4<Long, Integer, Integer, Integer>)serv::carParkSkinUp;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKSKINUSE_LONG_INT_INT_INT_INT_INT: {
				return (GofFunction6<Long, Integer, Integer, Integer, Integer, Integer>)serv::carParkSkinUse;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETCARPARKCARINFO_LONG: {
				return (GofFunction1<Long>)serv::getCarParkCarInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETHUMANCARPARKINFO_LONG_LONG_BOOLEAN: {
				return (GofFunction3<Long, Long, Boolean>)serv::getHumanCarParkInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETMOUNTMAP_LONG: {
				return (GofFunction1<Long>)serv::getMountMap;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETPUBLICCARPARKINFO_LONG_INT_INT: {
				return (GofFunction3<Long, Integer, Integer>)serv::getPublicCarParkInfo;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETPUBLICCARPARKNULLNUM_INT: {
				return (GofFunction1<Integer>)serv::getPublicCarParkNullNum;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETSKINLEVEL_LONG_INT: {
				return (GofFunction2<Long, Integer>)serv::getSkinLevel;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GM_LONG_STRING_PARAM: {
				return (GofFunction3<Long, String, Param>)serv::gm;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_HELPCOLLECTCARPARK_LONG_LONG_INT: {
				return (GofFunction3<Long, Long, Integer>)serv::helpCollectCarPark;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ISEXITINCACHE_LONG: {
				return (GofFunction1<Long>)serv::isExitInCache;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_LOADSERVER_LIST: {
				return (GofFunction1<List>)serv::loadServer;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONCARPARKFIX_LONG_INT_PARAM: {
				return (GofFunction3<Long, Integer, Param>)serv::onCarParkFix;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONHUMANLOGIN_LONG: {
				return (GofFunction1<Long>)serv::onHumanLogin;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONHUMANLOGOUT_LONG: {
				return (GofFunction1<Long>)serv::onHumanLogout;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE1_STRING: {
				return (GofFunction1<String>)serv::update1;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE2_OBJECTS: {
				return (GofFunction1<Object[]>)serv::update2;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE3_PARAM: {
				return (GofFunction1<Param>)serv::update3;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE4_STRING: {
				return (GofFunction1<String>)serv::update4;
			}
			case EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATEREWARDADD_LONG_MAP: {
				return (GofFunction2<Long, Map>)serv::updateRewardAdd;
			}
			default: break;
		}
		return null;
	}

	/**
	 * 获取实例
	 * 大多数情况下可用此函数获取
	 * @return
	 */
	public static CarParkServiceProxy newInstance() {
		String portId = Distr.getPortId(SERV_ID);
		if(portId == null) {
			LogCore.remote.error("通过servId未能找到查找上级Port: servId={}", SERV_ID);
			return null;
		}
		String nodeId = (remoteNodeIdFunc == null) ? Distr.getNodeId(portId) : remoteNodeIdFunc.apply(SERV_ID);
		if(nodeId == null) {
			LogCore.remote.error("通过portId未能找到查找上级Node: portId={}", portId);
			return null;
		}
		return createInstance(nodeId, portId, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static CarParkServiceProxy newInstance(String node) {
		return createInstance(node, null, SERV_ID);
	}

	/**
	 * 获取实例,不关心执行port，消息接收方会根据serviceId自动定位
	 * @return
	 */
	public static CarParkServiceProxy newInstance(String node, Object serviceId) {
		return createInstance(node, null, serviceId);
	}

	/**
	 * 根据游戏服serverId获取实例
	 * @return
	 */
	public static CarParkServiceProxy newInstance(int serverId) {
		String worldNodeId=Distr.NODE_DEFAULT;
		if(serverId!= Config.SERVER_ID){
			worldNodeId = Distr.NODE_DEFAULT + "_" + serverId;
		}
		return createInstance(worldNodeId, null, SERV_ID);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static CarParkServiceProxy newInstance(CallPoint targetPoint) {
		return createInstance(targetPoint.nodeId, targetPoint.portId, targetPoint.servId);
	}

	/**
	 * 获取实例
	 * @return
	 */
	public static CarParkServiceProxy newInstance(String node, String port, Object serviceId) {
		return createInstance(node, port, serviceId);
	}

	
	/**
	 * 创建实例
	 * @param node
	 * @param port
	 * @param id
	 * @return
	 */
	private static CarParkServiceProxy createInstance(String node, String port, Object serviceId) {
		CarParkServiceProxy inst = new CarParkServiceProxy();
		inst.localPort = Port.getCurrent();
		inst.remote = new CallPoint(node, port, serviceId);
		return inst;
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	/**
	 * 监听返回值
	 * @param method
	 * @param context
	 */
	public void listenResult(GofFunction2<Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Object...context) {
		listenResult(method, new Param(context));
	}
	
	public void listenResult(GofFunction3<Boolean, Param, Param> method, Param context) {
		context.put("_callerInfo", callerInfo);
		localPort.listenResult(method, context);
	}
	
	
	/**
	 * 等待返回值
	 */
	public Param waitForResult() {
		return localPort.waitForResult();
	}
	
	/**
	 * 设置后预先提醒框架下一次RPC调用参数是不可变的，可进行通信优化。<br/>
	 * 同Node间通信将不在进行Call对象克隆，可极大提高性能。<br/>
	 * 但设置后由于没进行克隆操作，接发双方都可对同一对象进行操作，可能会引起错误。<br/>
	 * 
	 * *由于有危险性，并且大多数时候RPC成本不高，建议只有业务中频繁调用或参数克隆成本较高时才使用本函数。<br/>
	 * *当接发双方仅有一方会对通信参数进行处理时，哪怕参数中有可变类型，也可以调用本函数进行优化。<br/>
	 * *当接发双方Node不相同时，本参数无效，双方处理不同对象；<br/>
	 *  当接发双方Node相同时，双方处理相同对象，这种差异逻辑会对分布式应用带来隐患，要小心使用。 <br/>
	 */
	public void immutableOnce() {
		this.immutableOnce = true;
	}

	
	/** {@link CarParkService#addMountVo(long humanId, int unlock)}*/
	public void addMountVo(long humanId, int unlock) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ADDMOUNTVO_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ADDMOUNTVO_LONG_INT", new Object[] {humanId, unlock});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#backMethod1(String param)}*/
	public void backMethod1(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_BACKMETHOD1_STRING,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_BACKMETHOD1_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#backMethod2(String param)}*/
	public void backMethod2(String param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_BACKMETHOD2_STRING,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_BACKMETHOD2_STRING", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#carParkCarUp(long humanId, int mountId, int costNum)}*/
	public void carParkCarUp(long humanId, int mountId, int costNum) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKCARUP_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKCARUP_LONG_INT_INT", new Object[] {humanId, mountId, costNum});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#carParkCombat(long humanId, int type, long masterId, int pos)}*/
	public void carParkCombat(long humanId, int type, long masterId, int pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKCOMBAT_LONG_INT_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKCOMBAT_LONG_INT_LONG_INT", new Object[] {humanId, type, masterId, pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#carParkMountId(long humanId, int mountId)}*/
	public void carParkMountId(long humanId, int mountId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKMOUNTID_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKMOUNTID_LONG_INT", new Object[] {humanId, mountId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#carParkParkingStart(long humanId, int type, long masterId, long mountId, int pos, int isProtect, int isReplace, p_role_change roleChange, p_role_figure roleFigure)}*/
	public void carParkParkingStart(long humanId, int type, long masterId, long mountId, int pos, int isProtect, int isReplace, p_role_change roleChange, p_role_figure roleFigure) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPARKINGSTART_LONG_INT_LONG_LONG_INT_INT_INT_P_ROLE_CHANGE_P_ROLE_FIGURE,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPARKINGSTART_LONG_INT_LONG_LONG_INT_INT_INT_P_ROLE_CHANGE_P_ROLE_FIGURE", new Object[] {humanId, type, masterId, mountId, pos, isProtect, isReplace, roleChange, roleFigure});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#carParkParkingStop(long humanId, long mountId, p_role_change roleChange, p_role_figure roleFigure)}*/
	public void carParkParkingStop(long humanId, long mountId, p_role_change roleChange, p_role_figure roleFigure) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(true, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPARKINGSTOP_LONG_LONG_P_ROLE_CHANGE_P_ROLE_FIGURE,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPARKINGSTOP_LONG_LONG_P_ROLE_CHANGE_P_ROLE_FIGURE", new Object[] {humanId, mountId, roleChange, roleFigure});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#carParkProtect(long humanId, int isOpen, int protectType, int protectRatio)}*/
	public void carParkProtect(long humanId, int isOpen, int protectType, int protectRatio) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPROTECT_LONG_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKPROTECT_LONG_INT_INT_INT", new Object[] {humanId, isOpen, protectType, protectRatio});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#carParkRename(long humanId, String name)}*/
	public void carParkRename(long humanId, String name) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKRENAME_LONG_STRING,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKRENAME_LONG_STRING", new Object[] {humanId, name});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#carParkResult(long humanId, long winId, long hp, int type, long masterId, int pos, long attackPower)}*/
	public void carParkResult(long humanId, long winId, long hp, int type, long masterId, int pos, long attackPower) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKRESULT_LONG_LONG_LONG_INT_LONG_INT_LONG,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKRESULT_LONG_LONG_LONG_INT_LONG_INT_LONG", new Object[] {humanId, winId, hp, type, masterId, pos, attackPower});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#carParkSkinUp(long humanId, int type, int skinId, int currentLevel)}*/
	public void carParkSkinUp(long humanId, int type, int skinId, int currentLevel) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKSKINUP_LONG_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKSKINUP_LONG_INT_INT_INT", new Object[] {humanId, type, skinId, currentLevel});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#carParkSkinUse(long humanId, int type, int skinId, int isUse, int x, int y)}*/
	public void carParkSkinUse(long humanId, int type, int skinId, int isUse, int x, int y) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKSKINUSE_LONG_INT_INT_INT_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_CARPARKSKINUSE_LONG_INT_INT_INT_INT_INT", new Object[] {humanId, type, skinId, isUse, x, y});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#getCarParkCarInfo(long humanId)}*/
	public void getCarParkCarInfo(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETCARPARKCARINFO_LONG,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETCARPARKCARINFO_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#getHumanCarParkInfo(long humanId, long masterId, boolean isCollected)}*/
	public void getHumanCarParkInfo(long humanId, long masterId, boolean isCollected) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETHUMANCARPARKINFO_LONG_LONG_BOOLEAN,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETHUMANCARPARKINFO_LONG_LONG_BOOLEAN", new Object[] {humanId, masterId, isCollected});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#getMountMap(long humanId)}*/
	public void getMountMap(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETMOUNTMAP_LONG,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETMOUNTMAP_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#getPublicCarParkInfo(long humanId, int serverId, int ceng)}*/
	public void getPublicCarParkInfo(long humanId, int serverId, int ceng) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETPUBLICCARPARKINFO_LONG_INT_INT,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETPUBLICCARPARKINFO_LONG_INT_INT", new Object[] {humanId, serverId, ceng});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#getPublicCarParkNullNum(int serverId)}*/
	public void getPublicCarParkNullNum(int serverId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETPUBLICCARPARKNULLNUM_INT,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETPUBLICCARPARKNULLNUM_INT", new Object[] {serverId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#getSkinLevel(long humanId, int skinId)}*/
	public void getSkinLevel(long humanId, int skinId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETSKINLEVEL_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GETSKINLEVEL_LONG_INT", new Object[] {humanId, skinId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#gm(long humanId, String funStr, Param param)}*/
	public void gm(long humanId, String funStr, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GM_LONG_STRING_PARAM,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_GM_LONG_STRING_PARAM", new Object[] {humanId, funStr, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#helpCollectCarPark(long humanId, long target, int pos)}*/
	public void helpCollectCarPark(long humanId, long target, int pos) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_HELPCOLLECTCARPARK_LONG_LONG_INT,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_HELPCOLLECTCARPARK_LONG_LONG_INT", new Object[] {humanId, target, pos});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#isExitInCache(long roleId)}*/
	public void isExitInCache(long roleId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ISEXITINCACHE_LONG,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ISEXITINCACHE_LONG", new Object[] {roleId});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link CarParkService#loadServer(List serverIds)}*/
	public void loadServer(List serverIds) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_LOADSERVER_LIST,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_LOADSERVER_LIST", new Object[] {serverIds});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#onCarParkFix(long humanId, int type, Param param)}*/
	public void onCarParkFix(long humanId, int type, Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONCARPARKFIX_LONG_INT_PARAM,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONCARPARKFIX_LONG_INT_PARAM", new Object[] {humanId, type, param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#onHumanLogin(long humanId)}*/
	public void onHumanLogin(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONHUMANLOGIN_LONG,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONHUMANLOGIN_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#onHumanLogout(long humanId)}*/
	public void onHumanLogout(long humanId) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONHUMANLOGOUT_LONG,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_ONHUMANLOGOUT_LONG", new Object[] {humanId});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#update1(String json)}*/
	public void update1(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE1_STRING,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE1_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#update2(Object... objs)}*/
	public void update2(Object... objs) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE2_OBJECTS,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE2_OBJECTS", new Object[] {objs});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#update3(Param param)}*/
	public void update3(Param param) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE3_PARAM,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE3_PARAM", new Object[] {param});
		if(immutableOnce) immutableOnce = false;
	}

	
	/** {@link CarParkService#update4(String json)}*/
	public void update4(String json) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE4_STRING,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATE4_STRING", new Object[] {json});
		if(immutableOnce) immutableOnce = false;
	}

	@SuppressWarnings("rawtypes")
	/** {@link CarParkService#updateRewardAdd(long humanId, Map rewardAddMap)}*/
	public void updateRewardAdd(long humanId, Map rewardAddMap) {
		callerInfo = Utils.getCallerInfo();
		remote.callerInfo = callerInfo;
		localPort.call(immutableOnce, remote, EnumCall.ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATEREWARDADD_LONG_MAP,"ORG_GOF_DEMO_WORLDSRV_CARPARK_CARPARKSERVICE_UPDATEREWARDADD_LONG_MAP", new Object[] {humanId, rewardAddMap});
		if(immutableOnce) immutableOnce = false;
	}
}
